package io.aicloudware.portal.api_rest.framework.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;


/**
 * @Author: zheng.mingdong
 * @Date: Created in 14:50 2020/9/4
 */
public class AesUtils3 {
    private static final String KEY_ALGORITHM = "AES";

    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";// 默认的加密算法

    /**
     * 加密
     *
     * @param content
     * @param password
     * @return
     */
    public static String encrypt(String content, String password) {
        try {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);// 创建密码器

            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8.name());

            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(password));// 初始化为加密模式的密码器

            byte[] result = cipher.doFinal(byteContent);// 加密

            return Base64.encodeBase64String(result);// 通过Base64转码返回
        }
        catch (Exception ex) {
            System.out.println("加密异常:{}"+ex.toString());
        }

        return null;
    }

    /**
     * AES 解密操作
     *
     * @param content
     * @param password
     * @return
     */
    public static String decrypt(String content, String password) {

        try {
            // 实例化
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);

            // 使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(password));

            // 执行操作
            byte[] result = cipher.doFinal(Base64.decodeBase64(content));

            return new String(result, StandardCharsets.UTF_8.name());
        }
        catch (Exception ex) {
            System.out.println("解密异常:{}"+ ex.toString());
        }

        return null;
    }

    /**
     * @description: 生成加密秘钥
     * @author: HWY
     * @date: 2020/10/23 10:36
     * @param: password
     * @return: javax.crypto.spec.SecretKeySpec
     */
    private static SecretKeySpec getSecretKey(final String password) {
        // 返回生成指定算法密钥生成器的 KeyGenerator 对象
        try {
            KeyGenerator kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG", "SUN");
            random.setSeed(password.getBytes());
            kg.init(128, random);
            // 生成一个密钥
            SecretKey secretKey = kg.generateKey();
            // 转换为AES专用密钥
            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);
        }
        catch (NoSuchAlgorithmException | NoSuchProviderException e) {
            e.printStackTrace();
        }

        return null;
    }


    public static void main(String[] args) {
        String beforeEncrypt = "{\"customNo\":\"18656201336\",\"userId\":\"15613365620\",\"userName\":\"张三\"}";
        System.out.println("beforeEncrypt:" + beforeEncrypt);
        String afterEncrypt = encrypt(beforeEncrypt, "4z@bqu7m$8@iQtyX");
        System.out.println("afterEncrypt:" + afterEncrypt);
        String decrypt = decrypt(afterEncrypt, "4z@bqu7m$8@iQtyX");
        System.out.println("decrypt:" + decrypt);
        
        System.out.println(decrypt("aWnAKP05/y9MpX3WBAXOjJ/lv0KzgR+Gu1i+fF/ZX6nPgVySi7PRsFJYvuiHZ+ahxF6X5V2s6GnhrGo74ZZzaJKBUPsGD1xg59ox52ut9Lg=","4z@bqu7m$8@iQtyX"));
        
        String afterEncrypt1 = "aWnAKP05/y9MpX3WBAXOjJ/lv0KzgR+Gu1i+fF/ZX6nPgVySi7PRsFJYvuiHZX5V2s6GnhrGo74ZZzaJKBUPsGD1xg59ox52ut9Lg=";
        System.out.println(decrypt(afterEncrypt1, "4z@bqu7m$8@iQtyX"));
        
        String str = "3PtPU/2jEKDgauVMIHpF6muF6WFQuOKE9xgp4DX/55v59RBIDaKBZo473k8H3jj5HQzNoCWmA6+54XNPtTvtoQ==";
        
        System.out.println(decrypt(str, "4z@bqu7m$8@iQtyX"));
    }
}