package io.aicloudware.portal.api_vcpp.controller.quota;

import javax.validation.Valid;

import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaDetailBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaResultBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@Controller
@RequestMapping("/quota")
@Api(value = "/quota", description = "资源配额", position = 508)
public class UpOrderQuotaController extends BaseUpController<UpOrderQuota, UpOrderQuotaBean, UpOrderQuotaResultBean> {
	
	@Autowired
	private IUpOrderQuotaService quotaService;
	
    @RequestMapping(value = "/query/{type}", method = RequestMethod.GET)
    @ApiOperation(notes = "/query/{type}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpOrderQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean queryQuota(@ApiParam(value = "对象ID") @PathVariable String type) {
    	UpOrderQuotaBean[] beans = quotaService.queryQuota(type, QuotaCatalog.NEW);
    	UpOrderQuotaResultBean result = new UpOrderQuotaResultBean();
    	result.setDataList(beans);
    	return ResponseBean.success(result);
    }

//	@RequestMapping(value = "/add/temp", method = RequestMethod.POST)
//	@ApiOperation(notes = "/add/temp", httpMethod = "POST", value = "添加实例")
//	@ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpOrderQuotaBean.class)})
//	@ResponseBody
//	public ResponseBean addQuotaTemp1(@ApiParam(value = "实例对象") @Valid @RequestBody UpOrderQuotaBean bean, BindingResult bindingResult) {
//		return ResponseBean.success(quotaService.add(bean));
//	}

	@RequestMapping(value = "/add/temp", method = RequestMethod.POST)
	@ApiOperation(notes = "/add/temp", httpMethod = "POST", value = "添加实例")
	@ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpOrderQuotaDetailBean.class)})
	@ResponseBody
	public ResponseBean addQuotaTemp1(@ApiParam(value = "实例对象") @Valid @RequestBody UpOrderQuotaDetailBean bean, BindingResult bindingResult) {
		return ResponseBean.success(quotaService.add(bean));
	}

//
//    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaListBean.class)})
//    @ResponseBody
//    public ResponseBean addQuota(@ApiParam(value = "实例对象") @Valid @RequestBody UpQuotaListBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpQuotaBean.class)})
//    @ResponseBody
//    public ResponseBean updateQuota(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                    @ApiParam(value = "实例对象") @Valid @RequestBody UpQuotaBean bean,
//                                    BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaListBean.class)})
//    @ResponseBody
//    public ResponseBean updateQuota(@ApiParam(value = "实例对象") @Valid @RequestBody UpQuotaListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteQuota(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteQuota(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }
}
