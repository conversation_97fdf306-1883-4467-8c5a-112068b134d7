package io.aicloudware.portal.api_rest.framework.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "资源操作")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, RestResourceMessageBean.class })
public class RestResourceMessageBean extends BaseRestBean {

	@ApiModelProperty(value = "资源类型")
	private String userId;
	
	@ApiModelProperty(value = "资源类型")
	private String mainAgreementId;
	
	@ApiModelProperty(value = "资源类型")
	private String doorOrderItemId;
	
	@ApiModelProperty(value = "资源类型")
	private String instId;
	
	@ApiModelProperty(value = "资源类型")
	private String instName;
	
	@ApiModelProperty(value = "资源类型")
	private String resourcePoolId;
	
	@ApiModelProperty(value = "资源类型")
	private String vpcId;
	
	@ApiModelProperty(value = "资源类型")
	private String subnetSegment;
	
	@ApiModelProperty(value = "资源类型")
	private String ip;
	
	@ApiModelProperty(value = "资源类型")
	private String operationType;
	
	@ApiModelProperty(value = "资源类型")
	private RestVpcInfoBean vpcInfo;
	
	private String cityId = "851";

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getMainAgreementId() {
		return mainAgreementId;
	}

	public void setMainAgreementId(String mainAgreementId) {
		this.mainAgreementId = mainAgreementId;
	}

	public String getDoorOrderItemId() {
		return doorOrderItemId;
	}

	public void setDoorOrderItemId(String doorOrderItemId) {
		this.doorOrderItemId = doorOrderItemId;
	}

	public String getInstId() {
		return instId;
	}

	public void setInstId(String instId) {
		this.instId = instId;
	}

	public String getInstName() {
		return instName;
	}

	public void setInstName(String instName) {
		this.instName = instName;
	}

	public String getResourcePoolId() {
		return resourcePoolId;
	}

	public void setResourcePoolId(String resourcePoolId) {
		this.resourcePoolId = resourcePoolId;
	}

	public String getVpcId() {
		return vpcId;
	}

	public void setVpcId(String vpcId) {
		this.vpcId = vpcId;
	}

	public String getSubnetSegment() {
		return subnetSegment;
	}

	public void setSubnetSegment(String subnetSegment) {
		this.subnetSegment = subnetSegment;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public RestVpcInfoBean getVpcInfo() {
		return vpcInfo;
	}

	public void setVpcInfo(RestVpcInfoBean vpcInfo) {
		this.vpcInfo = vpcInfo;
	}

	public String getCityId() {
		return cityId;
	}

	public void setCityId(String cityId) {
		this.cityId = cityId;
	}
}
