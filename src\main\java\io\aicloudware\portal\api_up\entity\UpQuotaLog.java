package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaLogBean;
import io.aicloudware.portal.framework.sdk.contants.QuotaLogStatus;
import io.aicloudware.portal.framework.sdk.contants.SpResourceType;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "up_quota_log")
@Access(AccessType.FIELD)
public class UpQuotaLog extends BaseUpEntity<UpQuotaLogBean> {

    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "approver_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser approver;

    @Column(name = "reason")
    private String reason;

    @Column(name = "quota_log_status")
    @Enumerated(EnumType.STRING)
    private QuotaLogStatus quotaLogStatus;

    @Column(name = "approve_comment")
    private String approveComment;

    @Column(name = "service")
    @Enumerated(EnumType.STRING)
    private SpService service;

    @Column(name = "resource_type")
    @Enumerated(EnumType.STRING)
    private SpResourceType resourceType;

    @Column(name = "apply_quota")
    private Integer applyQuota;

    @Column(name = "quota")
    private Integer quota;

    @Column(name = "used_quota")
    private Integer usedQuota;

    @JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg org;

    @EntityProperty(isCopyOnUpdate = false)
    @Column(name = "record_tm", nullable = false)
    private Date recordTm;

    @JoinColumn(name = "parent_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpQuotaLog parent;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "parent")
    @Where(clause = "status!='deleted'")
    @org.hibernate.annotations.OrderBy(clause = "id")
    private List<UpQuotaLog> children;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpService getService() {
        return service;
    }

    public void setService(SpService service) {
        this.service = service;
    }

    public SpResourceType getResourceType() {
        return resourceType;
    }

    public void setResourceType(SpResourceType resourceType) {
        this.resourceType = resourceType;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Integer getUsedQuota() {
        return usedQuota;
    }

    public void setUsedQuota(Integer usedQuota) {
        this.usedQuota = usedQuota;
    }

    public SpOrg getOrg() {
        return org;
    }

    public void setOrg(SpOrg org) {
        this.org = org;
    }

    public UpUser getApprover() {
        return approver;
    }

    public void setApprover(UpUser approver) {
        this.approver = approver;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getApproveComment() {
        return approveComment;
    }

    public void setApproveComment(String approveComment) {
        this.approveComment = approveComment;
    }

    public Integer getApplyQuota() {
        return applyQuota;
    }

    public void setApplyQuota(Integer applyQuota) {
        this.applyQuota = applyQuota;
    }

    public Date getRecordTm() {
        return recordTm;
    }

    public void setRecordTm(Date recordTm) {
        this.recordTm = recordTm;
    }

    public UpQuotaLog getParent() {
        return parent;
    }

    public void setParent(UpQuotaLog parent) {
        this.parent = parent;
    }

    public List<UpQuotaLog> getChildren() {
        return children;
    }

    public void setChildren(List<UpQuotaLog> children) {
        this.children = children;
    }

    public QuotaLogStatus getQuotaLogStatus() {
        return quotaLogStatus;
    }

    public void setQuotaLogStatus(QuotaLogStatus quotaLogStatus) {
        this.quotaLogStatus = quotaLogStatus;
    }
}
