package io.aicloudware.portal.api_vcpp.controller.order;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderRdsService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderVdiService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderVdiBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CatalogType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudServerChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * RDS服务器申请
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/vdi")
public class UpOrderVdiController extends BaseController {

	@Autowired
	private IUpOrderVdiService upOrderVdiService;

	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.VDI, description = "管理员新增订单")
    public ResponseBean save(@RequestBody UpOrderVdiBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		bean.setOwnerId(applyUser.getId());
		return ResponseBean.success(upOrderVdiService.save(bean, applyUser));
	}
}