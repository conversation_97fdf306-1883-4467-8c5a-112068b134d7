package io.aicloudware.portal.api_up.entity;

import java.util.Arrays;
import java.util.Date;
import java.util.Set;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IDataScopeEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationPage;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationStatus;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;
import io.aicloudware.portal.framework.sdk.contants.UpMainMenuType;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOVDC;

@Entity
@Table(name = "up_application")
@Access(AccessType.FIELD)
public class UpApplication extends BaseUpEntity<UpApplicationBean> implements IDataScopeEntity<UpApplicationBean>, IEnvironmentEntity<UpApplicationBean> {

    public UpApplication() {
    }

    public UpApplication(Integer id) {
        super(id);
    }

    @Column(name = "page")
    @Enumerated(EnumType.STRING)
    private UpApplicationPage page;

    @JoinColumn(name = "reservation_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOVDC reservation;

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpApplicationType type;

    @Column(name = "operate_info", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String operateInfo;

    @Column(name = "reason", length = ApiConstants.STRING_MAX_LENGTH)
    private String reason;

    @Column(name = "comment", length = ApiConstants.STRING_MAX_LENGTH)
    private String comment;

    @JoinColumn(name = "task_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpTask task;

    @Column(name = "application_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpApplicationStatus applicationStatus;

    @Column(name = "oa_id")
    private String oaId;

    @Column(name = "accept_tm")
    private Date acceptTm;

    @Column(name = "deploy_start_tm")
    private Date deployStartTm;

    @Column(name = "redeploy_start_tm")
    private Date reDeployStartTm;

    @Column(name = "deploy_end_tm")
    private Date deployEndTm;

    @Column(name = "close_tm")
    private Date closeTm;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpApplicationBean> searchBean, Set<String> aliasSet) {
        UpApplicationSearchBean bean = (UpApplicationSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getApplicationStatusList())) {
            DaoUtil.addInValues(criteria, "applicationStatus", Arrays.asList(bean.getApplicationStatusList()));
        }
        if (UpMainMenuType.my_cloud.equals(searchBean.getMainMenuType())) {
            criteria.add(Restrictions.eq("owner.id", ThreadCache.getUserId()));
            DaoUtil.addDataScope(null, criteria, aliasSet);
        } else {
            DaoUtil.addDataScope(searchBean.getMainMenuType(), criteria, aliasSet);
        }
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }

    public UpApplicationPage getPage() {
        return page;
    }

    public void setPage(UpApplicationPage page) {
        this.page = page;
    }

    public SpOVDC getReservation() {
        return reservation;
    }

    public void setReservation(SpOVDC reservation) {
        this.reservation = reservation;
    }

    @Override
    public UpUser getOwner() {
        return owner;
    }

    @Override
    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public UpApplicationType getType() {
        return type;
    }

    public void setType(UpApplicationType type) {
        this.type = type;
    }

    public String getOperateInfo() {
        return operateInfo;
    }

    public void setOperateInfo(String operateInfo) {
        this.operateInfo = operateInfo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public UpTask getTask() {
        return task;
    }

    public void setTask(UpTask task) {
        this.task = task;
    }

    public UpApplicationStatus getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(UpApplicationStatus applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public String getOaId() {
        return oaId;
    }

    public void setOaId(String oaId) {
        this.oaId = oaId;
    }

    public Date getAcceptTm() {
        return acceptTm;
    }

    public void setAcceptTm(Date acceptTm) {
        this.acceptTm = acceptTm;
    }

    public Date getDeployStartTm() {
        return deployStartTm;
    }

    public void setDeployStartTm(Date deployStartTm) {
        this.deployStartTm = deployStartTm;
    }

    public Date getReDeployStartTm() {
        return reDeployStartTm;
    }

    public void setReDeployStartTm(Date reDeployStartTm) {
        this.reDeployStartTm = reDeployStartTm;
    }

    public Date getDeployEndTm() {
        return deployEndTm;
    }

    public void setDeployEndTm(Date deployEndTm) {
        this.deployEndTm = deployEndTm;
    }

    public Date getCloseTm() {
        return closeTm;
    }

    public void setCloseTm(Date closeTm) {
        this.closeTm = closeTm;
    }

}
