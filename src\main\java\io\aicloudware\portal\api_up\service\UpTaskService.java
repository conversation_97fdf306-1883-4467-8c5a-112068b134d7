package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.common.SdkConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.dao.IQueryDao;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpTaskBean;
import io.aicloudware.portal.framework.sdk.bean.UpTaskSearchBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.service.IServiceTaskRunnable;
import io.aicloudware.portal.framework.service.ITaskRunnable;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.service.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class UpTaskService extends BaseService implements IUpTaskService {

	@Autowired
	protected IUpSystemConfigService upSystemConfigService;

	@Autowired
	protected IUpApplicationService upApplicationService;

	@Autowired
	protected ISpServerConnectionService spServerConnectionService;

	@Autowired
	protected IUpTenantService upTenantService;

	@Autowired
	protected IUpQuotaService upQuotaService;

	@Autowired
	protected IUpUserService upUserService;

	@Autowired
	protected ISpK8sClusterService spK8sClusterService;

//    @Autowired
//    private ISpDeploymentService spDeploymentService;

	@Autowired
	private ISpVappService spVappService;

	@Autowired
	private ISpElasticIpService spElasticIpService;

	@Autowired
	private ISpVappTemplateService vappTemplateService;

	@Autowired
	private ISpLoadBalancerService spLoadBalancerService;

	@Autowired
	private ISpVmService spVmService;

	@Autowired
	private IUpEmailService upEmailService;

	@Autowired
	private ISpFileStorageService spFileStorageService;

	@Autowired
	private ICommonService commonService;

	@Autowired
	private IQueryDao queryDao;
	
	@Autowired
	private ISpObjectStorageBucketService spObjectStorageBucketService;

    @Autowired
    private ISpSnapshotService spSnapshotService;

	@Override
	public UpTaskBean redeploy(Integer taskId) {
		UpTask task = dao.load(UpTask.class, taskId);
		if (task.getOrder() != null && UpTaskStatus.error.equals(task.getTaskStatus())
				&& OrderStatus.deploy_failed.equals(task.getOrder().getOrderStatus())) {
			refreshChildRequestStatus(Collections.singletonList(task), true, null);
			task.getOrder().setOrderStatus(OrderStatus.pending_deploy);
			// task.getOrder().setReDeployStartTm(new Date());
			dao.update(task.getOrder());
			task.setTaskStatus(UpTaskStatus.start);
			dao.update(task);
		}
		return BeanCopyUtil.copy(task, UpTaskBean.class);
	}

	private void refreshChildRequestStatus(List<UpTask> taskList, boolean redeploy, BeanWrapper<Integer> runningCount) {
		if (Utility.isNotEmpty(taskList)) {
			for (UpTask task : taskList) {
				if (Utility.isNotEmpty(task.getRequestId())) {
					if (UpTaskStatus.running.equals(task.getRequestStatus())) {
						UpTaskStatus oldRequestStatus = task.getRequestStatus();
						String oldStatusMessage = task.getStatusMessage();
						String requestId = ListUtil
								.last(Arrays.asList(task.getRequestId().split(ApiConstants.REQUEST_DELIMITER)));
						try {
							BeanWrapper<String> message = new BeanWrapper<>();
							UpTaskStatus currentStatus = UpTaskStatus.running;
							if (StringUtils.equals(requestId, ApiConstants.REQUEST_ID_SUCCEED)) {
								currentStatus = UpTaskStatus.finish;
							} else {
								logger.debug("[task requestId]:" + requestId + ", task: " + task.getId()
										+ ", orderType: " + task.getOrderType());
								if (task.getOrder() != null && (task.getOrderType() == OrderType.security_tenant_add
										|| task.getOrderType() == OrderType.security_active)) {
									currentStatus = cloudService.getServiceRequestStatus(task, requestId, message);
								} else if (task.getOrder() != null && (task.getOrderType() == OrderType.new_k8s_cluster
									|| task.getOrderType() == OrderType.new_k8s_cluster_node_pool))  {
									currentStatus = spK8sClusterService.checkJobStatus(task, requestId, message);
								}else {
									//currentStatus = cloudService.getRequestStatus(task.getSpOrg(), requestId, message);
									if (task.getOrder() != null) {
										if (task.getOrderType() == OrderType.vm_snapshot_create) {
											String snapshotUuid = spSnapshotService.checkSnapshot(task.getTargetId(), task.getRequestId());
											if (snapshotUuid != null) {
												currentStatus = UpTaskStatus.finish;
											}
										} else if (task.getOrderType() == OrderType.new_cloud_server) {
											String jobStatus = spVmService.checkJobStatus(task, requestId, message);
											if ("SUCCESS".equals(jobStatus)) {
												currentStatus = UpTaskStatus.finish;
											} else if ("FAIL".equals(jobStatus)) {
												currentStatus = UpTaskStatus.error;
											}
										}
									}

									//currentStatus = cloudService.getRequestStatus(task.getSpOrg(), requestId, message);
								}
							}
							task.setRequestStatus(currentStatus);
							if (task.getParent() != null) {
								task.setTaskStatus(task.getRequestStatus());
							}
							if (Utility.isNotEmpty(message.getValue())) {
								task.setStatusMessage(message.getValue());
							}
							if (currentStatus == UpTaskStatus.finish) {
								if (task.getParent() != null) {
									ITaskRunnable taskRunnable = getTaskRunnable(task);
									if (taskRunnable != null) {
										taskRunnable.doSubTaskFinish(task);
									}

									if (task.getOrder().getType().name().startsWith("service_")) {
										IServiceTaskRunnable serviceTaskRunnable = commonService
												.getServiceTaskRunnable(task);
										if (serviceTaskRunnable != null) {
											serviceTaskRunnable.doSubServiceTaskFinish(task);
										}
									}
								}
							}
						} catch (Exception e) {
							// 网络连接错误不要抛出异常
							logger.error("RequestId : " + requestId, e);
							task.setStatusMessage(Utility.getStackTraceContent(e));
						}

						if (Utility.notEquals(oldRequestStatus, task.getRequestStatus())
								|| Utility.notEquals(oldStatusMessage, task.getStatusMessage())) {
							dao.update(task);
						}
						if (runningCount != null && UpTaskStatus.running.equals(task.getRequestStatus())) {
							runningCount.setValue(runningCount.getValue() + 1);
						}
					}
					if (redeploy && UpTaskStatus.error.equals(task.getRequestStatus())) {
						task.setRequestId(null);
						task.setRequestNumber(null);
						task.setRequestStatus(UpTaskStatus.start);
					}
				}
				refreshChildRequestStatus(task.getChildList(), redeploy, runningCount);
			}
		}
	}

	@Override
	public void runTask(String refreshJob) {
		dao.flush();
		dao.clear();
		UpTaskSearchBean searchBean = new UpTaskSearchBean();
		searchBean.setTaskStatusList(new UpTaskStatus[] { UpTaskStatus.start, UpTaskStatus.running });
		UpSystemConfigBean systemConfigBean = upSystemConfigService.get(UpSystemConfigKey.log_task);
		if (Utility.isEmpty(systemConfigBean.getValue())) {
			systemConfigBean.setValue(Boolean.TRUE.toString());
		}
		Logger.setState(Boolean.parseBoolean(systemConfigBean.getValue()));
		searchBean.setOrderName1("id");
		searchBean.setOrderBy1(Boolean.TRUE);
		searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
		List<UpTask> taskList = dao.query(searchBean, new UpTask());
		// Logger.setState(true);
		for (int i = taskList.size() - 1; i >= 0; i--) {
			UpTask parentTask = taskList.get(i).getParent();
			if (parentTask != null) {
				if (!taskList.contains(parentTask)) {
					taskList.add(parentTask);
				}
				taskList.remove(i);
			}
		}
		BeanWrapper<Integer> runningCount = new BeanWrapper<>(0);
		refreshChildRequestStatus(taskList, false, runningCount);
		UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.deploy_task_threshold);
		if (Utility.isEmpty(bean.getValue())) {
			bean.setValue("20");
		}
		ThreadCache.deployingThresholdCountLocal
				.set(new Integer[] { Integer.parseInt(bean.getValue()), runningCount.getValue() });
		for (UpTask task : taskList) {
			if ("all".equals(refreshJob)) {
				// run all
			} else if ("yes".equals(refreshJob)) {
				// run refresh only
				if (!task.getType().name().endsWith("_refresh")) {
					continue;
				}
			} else {
				// run refresh never
				if (task.getType().name().endsWith("_refresh")) {
					continue;
				}
			}
			if (!UpTaskStatus.finish.equals(task.getTaskStatus()) && !UpTaskStatus.error.equals(task.getTaskStatus())) {
				ITaskRunnable taskRunnable = getTaskRunnable(task);
				if (taskRunnable != null) {
					UpTaskStatus oldStatus = task.getTaskStatus();
					try {
						taskRunnable.runTask(task.getId());
					} catch (Exception e) {
						logger.error("TaskID:" + task.getId(), e);
						handleTaskError(taskRunnable, task.getId(), oldStatus, Utility.getStackTraceContent(e));
					}
				}
			}
		}
	}

	@Override
	public UpTaskBean getRefreshTask(boolean isTenant) {
		List<UpTask> taskList = null;
		if (isTenant) {
			String hql = "select t from UpTask t where (t.taskStatus=:status1 or t.taskStatus=:status2) and (t.type=:type1 or t.type=:type2 and t.targetId=:targetId) order by t.id desc";
			Map<String, Object> paramMap = MapUtil.of("status1", UpTaskStatus.running, "status2", UpTaskStatus.finish,
					"type1", UpTaskType.server_connection_refresh, "type2", UpTaskType.up_tenant_refresh);
			paramMap.put(ApiConstants.QUERY_FIRST_RESULT, 1);
			paramMap.put(ApiConstants.QUERY_MAX_RESULTS, 1);
			taskList = queryDao.queryHql(hql, paramMap);
		} else {
			String hql = "select t from UpTask t where (t.taskStatus=:status1 or t.taskStatus=:status2) and t.type=:type1 order by t.id desc";
			Map<String, Object> paramMap = MapUtil.of("status1", UpTaskStatus.running, "status2", UpTaskStatus.finish,
					"type1", UpTaskType.server_connection_refresh);
			paramMap.put(ApiConstants.QUERY_FIRST_RESULT, 1);
			paramMap.put(ApiConstants.QUERY_MAX_RESULTS, 1);
			taskList = queryDao.queryHql(hql, paramMap);
		}
		if (Utility.isEmpty(taskList)) {
			return null;
		}
		return BeanCopyUtil.copy(ListUtil.first(taskList), UpTaskBean.class);
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void handleTaskError(ITaskRunnable taskRunnable, Integer taskId, UpTaskStatus oldStatus,
			String errorMessage) {
		taskRunnable.handleTaskError(taskId);
		UpTask task = dao.load(UpTask.class, taskId);
		task.setTaskStatus(UpTaskStatus.error);
		task.setStatusMessage(errorMessage);
		UpOrder order = task.getOrder();
		if (order != null) {
			order.setOrderStatus(OrderStatus.deploy_failed);
		}
		if (!task.getTaskStatus().equals(oldStatus)) {
			if (order != null) {
				upApplicationService.handleApplicationProcess(order);
				dao.update(order);
			}
			dao.update(task);
		}

		// TODO
		/*
		 * if (task.getOrderType() == OrderType.new_cloud_server || task.getOrderType()
		 * == OrderType.new_rds) { UpOrderCloudServer cloudServer = new
		 * UpOrderCloudServer(); cloudServer.setSpOrg(task.getSpOrg());
		 * cloudServer.setOrder(task.getOrder()); UpOrderCloudServerSearchBean
		 * searchBean = new UpOrderCloudServerSearchBean();
		 * searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
		 * List<UpOrderCloudServer> orderServers = dao.query(searchBean, cloudServer);
		 * if (Utility.isNotEmpty(orderServers)) { for (UpOrderCloudServer orderServer :
		 * orderServers) { SpVm vm =
		 * BeanFactory.getCloudDao().load(SpVm.class,orderServer.getVm().getId());
		 * vm.setPowerStatus(SpVmPowerStatus.power_off);
		 * vm.setDeployStatus(SpDeployStatus.ERROR); dao.update(vm); SpVapp spVapp =
		 * vm.getSpVapp(); spVapp.setDeployStatus(SpDeployStatus.ERROR);
		 * dao.update(spVapp); } } }
		 */
	}

	private ITaskRunnable getTaskRunnable(UpTask task) {
		ITaskRunnable taskRunnable = null;
		if (UpTaskType.server_connection_refresh.equals(task.getType())) {
			taskRunnable = spServerConnectionService;
		} else if (UpTaskType.up_tenant_refresh.equals(task.getType())) {
			taskRunnable = upTenantService;
		} else if (UpTaskType.endpoint_refresh.equals(task.getType())) {
			throw new RuntimeException("endpoint refresh not support");
		} else if (UpTaskType.quota_refresh.equals(task.getType())) {
			taskRunnable = upQuotaService;
		} else if (checkType(task, OrderType.new_cloud_disk, OrderType.new_cloud_server, OrderType.new_redis,
				OrderType.new_rds_mysql, OrderType.new_rds_oracle, OrderType.new_kafka, OrderType.new_k8s,
				OrderType.new_harbor, OrderType.new_autoscaling, OrderType.new_esk, OrderType.vm_backup_manual_add,
				OrderType.vm_backup_auto_add, OrderType.cloud_server_delete, OrderType.cloud_disk_delete,
				OrderType.vm_power_on, OrderType.vm_power_off, OrderType.vm_power_reset, OrderType.vm_power_reboot,
				OrderType.vm_power_suspend, OrderType.vm_snapshot_create, OrderType.vm_snapshot_restore,
				OrderType.vm_snapshot_delete, OrderType.vm_backup_auto_delete, OrderType.vm_backup_manual_delete,
				OrderType.vm_backup_restore)) {
			taskRunnable = spVappService;
		} else if (checkType(task, OrderType.cloud_server_update, OrderType.private_image)) {
			taskRunnable = spVmService;
		} else if (UpTaskType.up_application.equals(task.getType())) {
			// 给IOT增加新云主机类型专用
			taskRunnable = spVappService;
		} else if (checkType(task, OrderType.new_elasticIp)) {
			taskRunnable = spVappService;
		} else if (checkType(task, OrderType.new_load_balance)) {
			taskRunnable = spVappService;
//        } else if (checkType(task, UpApplicationType.vm_update,
//                UpApplicationType.vm_power_on, UpApplicationType.vm_power_off, UpApplicationType.vm_power_reset, UpApplicationType.vm_power_suspend,
//                UpApplicationType.vm_snapshot_create, UpApplicationType.vm_snapshot_restore, UpApplicationType.vm_snapshot_delete,
//                UpApplicationType.vm_clone)) {
//            taskRunnable = spVmService;
		} else if (checkType(task, OrderType.file_storage)) {
			taskRunnable = spFileStorageService;
		} else if (checkType(task, OrderType.object_storage_bucket, OrderType.object_storage_bucket_change, OrderType.object_storage_bucket_delete)) {
			taskRunnable = spObjectStorageBucketService;
		} else if (UpTaskType.statics_refresh.equals(task.getType())) {
			throw new RuntimeException("statics refresh not support");
		} else if (UpTaskType.ldap_user_sync.equals(task.getType())) {
			taskRunnable = upUserService;
		} else if (UpTaskType.send_mail_pending_approve.equals(task.getType())
				|| UpTaskType.send_mail_deploy_success.equals(task.getType())
				|| UpTaskType.send_mail_auto_archive.equals(task.getType())) {
			taskRunnable = upEmailService;
		} else if (UpTaskType.vra_auto_scale.equals(task.getType())) {
			taskRunnable = spServerConnectionService;
		} else if (checkType(task, OrderType.private_image_delete)) {
			taskRunnable = vappTemplateService;
		} else if (checkType(task, OrderType.new_k8s_cluster, OrderType.k8s_cluster_delete, OrderType.k8s_cluster_update,
				OrderType.new_k8s_cluster_node_pool, OrderType.k8s_cluster_node_pool_delete, OrderType.k8s_cluster_node_pool_update)) {
			taskRunnable = spK8sClusterService;
		}
		else {
			AssertUtil.check(false, "未处理的任务类型：" + task.getId() + "->" + task.getType());
		}
		return taskRunnable;
	}

	private boolean checkType(UpTask task, OrderType... types) {
		return UpTaskType.up_application.equals(task.getType()) && Arrays.asList(types).contains(task.getOrderType());
	}

	@Override
	public String getStatus(Integer orgId, Integer orderId) {
		String hql = "from UpTask where parent is null and status=:status and spOrg.id=:orgId and order.id=:orderId";
		List<UpTask> list = queryDao.queryHql(hql,
				MapUtil.of("orgId", orgId, "status", RecordStatus.active, "orderId", orderId));
		if (list != null && list.size() > 0) {
			return list.get(0).getTaskStatus().toString();
		}
		return "";
	}

	@Override
	public Map<Integer, String> getIncompletedTask(Integer orgId, OrderType orderType) {
		String hql = "from UpTask where parent is null and status=:status and order.spOrg.id=:orgId and order.type=:orderType and taskStatus<>:taskStatus";
		List<UpTask> list = queryDao.queryHql(hql, MapUtil.of("orgId", orgId, "status", RecordStatus.active,
				"orderType", orderType, "taskStatus", UpTaskStatus.finish));
		Map<Integer, String> statusMap = new HashMap<>();
		if (list != null && list.size() > 0) {
			for (UpTask task : list) {
				statusMap.put(task.getOrder().getId(), task.getTaskStatus().toString());
			}
		}
		return statusMap;
	}

}
