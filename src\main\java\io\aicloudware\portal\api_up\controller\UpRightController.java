package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpRight;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpRightBean;
import io.aicloudware.portal.framework.sdk.bean.UpRightResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/right")
@Api(value = "/right", description = "权限", position = 540)
public class UpRightController extends BaseUpController<UpRight, UpRightBean, UpRightResultBean> {

//    @Autowired
//    private IUpRightService upRightService;
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRightResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryRight(@ApiParam(value = "查询条件") @RequestBody UpRightSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpRightBean.class)})
//    @ResponseBody
//    public ResponseBean getRight(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return ResponseBean.success(upRightService.getRight(id));
//    }
//
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpRightBean.class)})
//    @ResponseBody
//    public ResponseBean addRight(@ApiParam(value = "实例对象") @RequestBody UpRightBean bean) {
//        UpRightListBean listBean = new UpRightListBean();
//        listBean.setDataList(new UpRightBean[]{bean});
//        upRightService.createRight(listBean);
//        return ResponseBean.success(bean);
//    }
//
//    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRightListBean.class)})
//    @ResponseBody
//    public ResponseBean addRight(@ApiParam(value = "实例对象") @RequestBody UpRightListBean bean) {
//        upRightService.createRight(bean);
//        return ResponseBean.success(bean);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteRight(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        upRightService.deleteRight(Arrays.asList(id));
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteRight(@ApiParam(value = "对象ID列表") @RequestBody SpSimpleOperateBean bean) {
//        upRightService.deleteRight(Arrays.asList(bean.getIdList()));
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    // =================================================================================================================
//
//    @RequestMapping(value = "/user_right_list", method = RequestMethod.GET)
//    @ApiOperation(notes = "/user_right_list", httpMethod = "GET", value = "查询当前用户权限列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRightListBean.class)})
//    @ResponseBody
//    public ResponseBean user_right_list() {
//        return convertRightList(upRightService.findUserRightList());
//    }
//
//    @RequestMapping(value = "/tenant_right_list", method = RequestMethod.GET)
//    @ApiOperation(notes = "/tenant_right_list", httpMethod = "GET", value = "查询当前租户权限列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpRightListBean.class)})
//    @ResponseBody
//    public ResponseBean tenant_right_list() {
//        return convertRightList(upRightService.findTenantRightList());
//    }
//
//    private ResponseBean convertRightList(List<UpRightBean> rightList) {
//        UpRightListBean bean = new UpRightListBean();
//        bean.setDataList(rightList.toArray(new UpRightBean[rightList.size()]));
//        return ResponseBean.success(bean);
//    }
//
//    @RequestMapping(value = "/find_role_list/{userId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/find_role_list/{userId}", httpMethod = "GET", value = "查询当前用户的角色列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回角色对象列表", response = UpRoleListBean.class)})
//    @ResponseBody
//    public ResponseBean find_role_list(@ApiParam(value = "用户ID") @PathVariable Integer userId) {
//        Map<Integer, String> roleMap = upRightService.findRoleMap(userId);
//        Map<Integer, UpRoleBean> roleBeanMap = commonService.map(UpRole.class, UpRoleBean.class, roleMap.keySet());
//        UpRoleListBean roleListBean = new UpRoleListBean();
//        roleListBean.setDataList(roleBeanMap.values().toArray(new UpRoleBean[roleBeanMap.size()]));
//        return ResponseBean.success(roleListBean);
//    }
//
//    @RequestMapping(value = "/find_user_list/{roleId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/find_user_list/{roleId}", httpMethod = "GET", value = "查询当前角色的用户列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回用户对象列表", response = UpUserListBean.class)})
//    @ResponseBody
//    public ResponseBean find_user_list(@ApiParam(value = "角色ID") @PathVariable Integer roleId) {
//        Map<Integer, String> userMap = upRightService.findUserMap(roleId);
//        Map<Integer, UpUserBean> userBeanMap = commonService.map(UpUser.class, UpUserBean.class, userMap.keySet());
//        UpUserListBean userListBean = new UpUserListBean();
//        userListBean.setDataList(userBeanMap.values().toArray(new UpUserBean[userBeanMap.size()]));
//        return ResponseBean.success(userListBean);
//    }
}
