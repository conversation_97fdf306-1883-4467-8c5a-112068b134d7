package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.*;
import io.aicloudware.portal.api_vcpp.entity.UpProductVmSet;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.service.BaseTaskService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudEcsOperation;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudEvsOperation;
import io.aicloudware.portal.platform_jointecloud.operation.JointEcloudVpcOperation;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpProject;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.entity.SpVapp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class UpQuotaService extends BaseTaskService implements IUpQuotaService {

    @Autowired
    protected IUpApplicationService upApplicationService;

    @Override
    public void notifyRefreshUsage() {
        ThreadCache.setSystemAdminLogin();
        upApplicationService.createSimpleOperateTask(UpTaskType.quota_refresh, "配额使用刷新", null);
    }

    @Override
    protected void doTaskRunning(UpTask task) {
        if (UpTaskType.quota_refresh.equals(task.getType())) {
            refreshUsage();
        }
        task.setTaskStatus(UpTaskStatus.finish);
        task.setStatusMessage(null);
    }

    private void refreshUsage() {
        List<SpVapp> deploymentList = dao.list(SpVapp.class);
        List<UpQuota> quotaList = dao.list(UpQuota.class);
        Map<String, UpQuota> quotaMap = ListUtil.map(quotaList, new ListUtil.Convert<UpQuota, String, UpQuota>() {
            @Override
            public String getKey(UpQuota quota) {
                return ""+getEntityId(quota.getOwner());
            }

            @Override
            public UpQuota getValue(UpQuota quota) {
//                quota.setVmUsedNum(0);
//                quota.setCpuUsedNum(0);
//                quota.setMemoryUsedGB(0);
//                quota.setDiskUsedGB(0);
//                quota.setIpUsedNum(0);
                return quota;
            }
        });

        for (SpVapp deployment : deploymentList) {
            UpUser user = deployment.getOwner();
            if (user == null ) {
                continue;
            }
//            calculateResource(quotaMap, getEntityId(tenant) + ":0:0:0", deployment);
//            calculateResource(quotaMap, getEntityId(tenant) + ":0:" + getEntityId(user) + ":0", deployment);
//            for (Integer groupId : groupService.getUserParentGroupIdSet(user.getId())) {
//                calculateResource(quotaMap, getEntityId(tenant) + ":" + groupId + ":0:0", deployment);
//            }
//            calculateResource(quotaMap, getEntityId(tenant) + ":0:0:" + getEntityId(appSystem), deployment);
        }
        for (UpQuota quota : quotaMap.values()) {
            dao.update(quota);
        }
    }

    private Integer getEntityId(IEntity entity) {
        return entity == null ? Utility.ZERO : Utility.toZero(entity.getId());
    }

    @Override
    public void sync(Integer orgId, SpRegionEntity region) {
        SpOrg org = dao.load(SpOrg.class, orgId);
        if (CloudType.ecloud.equals(region.getType())) {
            throw new RuntimeException("暂不支持");
        } else if (CloudType.jointecloud.equals(region.getType()) ) {
            Optional<SpProject> spProject = dao.list(SpProject.class, MapUtil.of("region", region, "status", RecordStatus.active)).stream().findFirst();
            AssertUtil.check(spProject.isPresent(), "未找到对应的项目");
            List<UpQuota> serverDataList = JointEcloudEvsOperation.getInstance(org.getUsername(), org.getPassword(), region).listQuota(spProject.get());
            serverDataList.addAll(JointEcloudEcsOperation.getInstance(org.getUsername(), org.getPassword(), region).listQuota(spProject.get()));
            serverDataList.addAll(JointEcloudVpcOperation.getInstance(org.getUsername(), org.getPassword(), region).listQuota(spProject.get()));

            serverDataList.forEach(o -> {
                o.setOrg(org);
                o.setRegion(region);
                o.setStatus(RecordStatus.active);
                o.setIsAdminQuota(true);
            });

            Comparator<UpQuota> comparator = Comparator
                    .comparing(UpQuota::getResourceType) // 先按 resourceType 比较
                    .thenComparing(UpQuota::getService);   // 再按 service 比较
            List<UpQuota> tableDataList = dao.list(UpQuota.class, MapUtil.of("region", region, "org", org, "status", RecordStatus.active, "isAdminQuota", true));

            List<UpQuota> dataToAdd = ListUtil.getDifference(serverDataList, tableDataList, comparator);
            List<UpQuota> dataToDel = ListUtil.getDifference(tableDataList, serverDataList, comparator);
            List<UpQuota> dataToUpdate = ListUtil.getIntersection(tableDataList, serverDataList, comparator,
                    (o1, o2) -> {
                        o1.setQuota(o2.getQuota());
                        o1.setUsedQuota(o2.getUsedQuota());
                    });
            dataToAdd.forEach( o -> {
                dao.insert(o);
            } );
            dataToDel.forEach( o -> {
                dao.delete(UpProductVmSet.class, o.getId());
            } );
            dataToUpdate.forEach( o -> {
                dao.update(o);
            } );

        }
    }

    @Override
    public UpQuotaBean[] queryTemplate(UpQuotaSearchBean searchBean) {
        //List<UpQuota> dbUserTemplates = dao.list(UpQuota.class, Map.of("isUserTemplate", true, "org.id", ThreadCache.getOrgId(), "region", ThreadCache.getRegion()));
        List<UpQuota> dbUserTemplates = queryDao.queryHql("from UpQuota where isUserTemplate = true and org.id = :orgId and region = :region order by service, resourceType", MapUtil.of("orgId", ThreadCache.getOrgId(), "region", ThreadCache.getRegion()));

        List<String> dbExsitResourceTypes = dbUserTemplates.stream().map(UpQuota::getResourceType).map(SpResourceType::name).distinct().collect(Collectors.toList());
        List<String> currentResourceTypes = Arrays.asList(SpResourceType.values()).stream().map(SpResourceType::name).collect(Collectors.toList());
        currentResourceTypes.removeAll(dbExsitResourceTypes);
        List<UpQuota> entitys = currentResourceTypes.stream().map(type -> {
            UpQuota upQuota = new UpQuota();
            upQuota.setResourceType(SpResourceType.valueOf(type));
            upQuota.setName(type);
            upQuota.setQuota(0);
            upQuota.setIsUserTemplate(true);
            upQuota.setRegion(ThreadCache.getRegion());
            upQuota.setOrg(new SpOrg(ThreadCache.getOrgId()));
            upQuota.setService(this.getService(SpResourceType.valueOf(type)));
            return upQuota;
        }).collect(Collectors.toList());
        if(!entitys.isEmpty()){
            dao.insert(entitys);
            dao.flush();
        }

        if(searchBean != null && searchBean.getBean() != null && searchBean.getBean().getResourceType() != null){
            //dbUserTemplates = dao.list(UpQuota.class, Map.of("isUserTemplate", true, "org.id", ThreadCache.getOrgId(), "region", ThreadCache.getRegion(),"resourceType", searchBean.getBean().getResourceType()));
            dbUserTemplates = queryDao.queryHql("from UpQuota where isUserTemplate = true and org.id = :orgId and region = :region and resourceType = :resourceType order by service, resourceType", MapUtil.of("orgId", ThreadCache.getOrgId(), "region", ThreadCache.getRegion(), "resourceType", searchBean.getBean().getResourceType()));
        }else{
            //dbUserTemplates = dao.list(UpQuota.class, Map.of("isUserTemplate", true, "org.id", ThreadCache.getOrgId(), "region", ThreadCache.getRegion()));
            dbUserTemplates = queryDao.queryHql("from UpQuota where isUserTemplate = true and org.id = :orgId and region = :region order by service, resourceType", MapUtil.of("orgId", ThreadCache.getOrgId(), "region", ThreadCache.getRegion()));
        }
        return BeanCopyUtil.copy2BeanList(dbUserTemplates, UpQuotaBean.class);
    }

    @Override
    public void saveTemplate(UpQuotaBean bean) {
        UpQuota entity = dao.load(UpQuota.class, bean.getId());
        AssertUtil.check(entity != null && entity.getOrg().getId().equals(ThreadCache.getOrgId()) && entity.getRegion().equals(ThreadCache.getRegion()) && entity.getIsUserTemplate(), "无操作权限！");
        entity.setQuota(bean.getQuota());
        dao.update(entity, "quota");
    }

    @Override
    public UpQuotaBean adminDetail(Integer id) {
        return overviewQuota(id).get(0);
    }

    @Override
    public void userQuotaSave(UpQuotaListBean listBean) {
        if(listBean.getDataList().length == 1 && listBean.getDataList()[0].getId() != null){
            UpQuota entity = dao.load(UpQuota.class, listBean.getDataList()[0].getId());
            entity.setQuota(listBean.getDataList()[0].getQuota());
            dao.update(entity, "quota");
        }else{
            List<UpQuota> entitys = Arrays.stream(listBean.getDataList()).map(bean -> {
                UpAppSystem appSystem = dao.load(UpAppSystem.class, bean.getAppSystemId());
                UpQuota entity = new UpQuota();
                entity.setOrg(new SpOrg(ThreadCache.getOrgId()));
                entity.setService(getService(bean.getResourceType()));
                entity.setResourceType(bean.getResourceType());
                entity.setQuota(bean.getQuota());
                entity.setName(appSystem.getName() + "-" + bean.getResourceType());
                entity.setOwner(appSystem.getOwner());
                entity.setAppSystem(appSystem);
                entity.setAdminQuota(false);
                entity.setOrg(new SpOrg(ThreadCache.getOrgId()));
                entity.setRegion(ThreadCache.getRegion());
                entity.setIsUserTemplate(false);
                //todo 缺少同步usedQuota
                entity.setUsedQuota(0);
                return entity;
            }).collect(Collectors.toList());
            dao.insert(entitys);
        }
    }

    @Override
    public List<UpQuotaBean> overviewQuota(Integer id) {
        // 用户quota列表
//        String sql =
//                "WITH \n" +
//                        "user_count AS ( SELECT COUNT ( 1 ) AS user_count FROM up_user WHERE is_system_admin = FALSE and status = 'active'),\n" +
//                        "tpl AS ( SELECT quota, resource_type AS resource_type FROM up_quota q WHERE is_user_template = TRUE and region = :region and sp_org_id = :orgId),\n" +
//                        "quota AS ( SELECT COUNT ( DISTINCT owner_id ) as assigend_user_count, SUM ( quota ) AS assigend_quota, resource_type FROM up_quota q WHERE is_admin_quota = FALSE and region = :region and sp_org_id = :orgId GROUP BY resource_type ) \n" +
//                        "SELECT t.service,t.resource_type, t.used_quota as usedQuota\n" +
//                        ",(c.user_count-(case when q.assigend_user_count is null then 0 else assigend_user_count end)) * tpl.quota + (case when q.assigend_quota is null then 0 else q.assigend_quota end) as assigendQuota, " +
//                        "t.quota, t.id\n" +
//                        "FROM\n" +
//                        "up_quota t \n" +
//                        "LEFT JOIN user_count C ON 1 = 1 \n" +
//                        "left join tpl tpl on t.resource_type = tpl.resource_type\n" +
//                        "left join quota q on t.resource_type = q.resource_type\n" +
//                        "WHERE\n" +
//                        "is_admin_quota = TRUE and region = :region and sp_org_id = :orgId " +
//                        (id == null ? "": " and id = " + id) +
//                        " order by service, resource_type";

        // appsystem
        String sql =
                "WITH \n" +
                        "app_system_count AS ( SELECT COUNT ( 1 ) AS app_system_count FROM up_app_system WHERE status = 'active'),\n" +
                        "tpl AS ( SELECT quota, resource_type AS resource_type FROM up_quota q WHERE is_user_template = TRUE and region_id = :region and sp_org_id = :orgId),\n" +
                        "quota AS ( SELECT COUNT ( DISTINCT app_system_id ) as assigend_app_system_count, SUM ( quota ) AS assigend_quota, resource_type FROM up_quota q WHERE is_admin_quota = FALSE and region_id = :region and sp_org_id = :orgId GROUP BY resource_type ) \n" +
                        "SELECT t.service,t.resource_type, t.used_quota as usedQuota\n" +
                        ",(c.app_system_count-(case when q.assigend_app_system_count is null then 0 else assigend_app_system_count end)) * tpl.quota + (case when q.assigend_quota is null then 0 else q.assigend_quota end) as assigendQuota, " +
                        "t.quota, t.id\n" +
                        "FROM\n" +
                        "up_quota t \n" +
                        "LEFT JOIN app_system_count C ON 1 = 1 \n" +
                        "left join tpl tpl on t.resource_type = tpl.resource_type\n" +
                        "left join quota q on t.resource_type = q.resource_type\n" +
                        "WHERE\n" +
                        "is_admin_quota = TRUE and region_id = :region and sp_org_id = :orgId " +
                        (id == null ? "": " and id = " + id) +
                        " order by service, resource_type";
        List<Object[]> result = queryDao.querySql(sql, MapUtil.of("region",ThreadCache.getRegion().getId(), "orgId", ThreadCache.getOrgId()));
//        Object obj = queryDao.querySql(sql, null);

        return result.stream().map(item -> {
            UpQuotaBean bean = new UpQuotaBean();
            bean.setService(SpService.valueOf((String) item[0]));
            bean.setResourceType(SpResourceType.valueOf((String) item[1]));
            bean.setUsedQuota((Integer) item[2]);
            if (item[3] == null){
                bean.setAssignedQuota(0);
            } else {
                bean.setAssignedQuota(((BigInteger) item[3]).intValue());
            }
            bean.setQuota((Integer) item[4]);
            bean.setId((Integer) item[5]);
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public void checkQuota(SpService service, SpResourceType resourceType, Integer appSystemId, Integer orgId, Integer quotaToAdd, Integer usedQuota)  {
        UpQuota upQuota = syncQuotaUsage(service, resourceType, appSystemId, orgId, usedQuota);
        AssertUtil.check(upQuota.getQuota() >= (usedQuota + quotaToAdd), "配额不足。当前 "+resourceType.getTitle()+" 配额："+upQuota.getQuota() + " 还可申请： " + (upQuota.getQuota() - usedQuota));
        upQuota.setUsedQuota(upQuota.getUsedQuota() + quotaToAdd);
        dao.update(upQuota);
    }

    @Override
    public UpQuota






    syncQuotaUsage(SpService service, SpResourceType resourceType, Integer appSystemId, Integer orgId, Integer usedQuota) {
        UpAppSystem appSystem = dao.load(UpAppSystem.class, appSystemId);
        List<UpQuota> dbUpQuotaList = dao.list(UpQuota.class, MapUtil.of("service", service, "resourceType", resourceType, "appSystem.id", appSystemId, "org.id", orgId));
        UpQuota upQuota = null;
        if (dbUpQuotaList == null || dbUpQuotaList.isEmpty()){
            List<UpQuota> templateUpQuotaList = dao.list(UpQuota.class, MapUtil.of("service", service, "resourceType", resourceType, "org.id", orgId, "isUserTemplate", true));
            AssertUtil.check(templateUpQuotaList != null && !templateUpQuotaList.isEmpty(), "未配置配额模板：" + service.name() + "-" + resourceType.name());
            UpQuota templateQuota = templateUpQuotaList.get(0);
            upQuota = new UpQuota();
            upQuota.setName(service.name()+"-"+resourceType.name()+"-"+appSystemId);
            upQuota.setUsedQuota(usedQuota);
            upQuota.setIsAdminQuota(false);
            upQuota.setIsAdminQuota(false);
            upQuota.setOrg(new SpOrg(orgId));
            upQuota.setRegion(ThreadCache.getRegion());
            upQuota.setService(service);
            upQuota.setResourceType(resourceType);
            upQuota.setOwner(appSystem.getOwner());
            upQuota.setAppSystem(appSystem);
            upQuota.setQuota(templateQuota.getQuota());
            upQuota = dao.insert(upQuota);
        } else {
            upQuota = dbUpQuotaList.get(0);
        }
        return upQuota;
    }

    @Override
    public UpQuotaBean userDetail(String resourceType) {
        UpQuotaBean bean = new UpQuotaBean();
        List<UpQuota> entitys = dao.list(UpQuota.class, MapUtil.of("isUserTemplate", false,
                "org.id", ThreadCache.getOrgId(),"isAdminQuota", false,
                "region", ThreadCache.getRegion(), "owner", ThreadCache.getUser(),
                "resourceType", SpResourceType.valueOf(resourceType)));
        if(entitys.isEmpty()){
            entitys = dao.list(UpQuota.class, MapUtil.of("isUserTemplate", true,
                    "org.id", ThreadCache.getOrgId(),"region", ThreadCache.getRegion(),
                    "resourceType", SpResourceType.valueOf(resourceType)));
        }

        bean = BeanCopyUtil.copy2Bean(entitys.get(0), UpQuotaBean.class);
        if(bean.getIsUserTemplate()){
            bean.setUsedQuota(0);
        }
        return bean;
    }

    @Override
    public SpService getService(SpResourceType type){
        if( type == SpResourceType.instances ||  type == SpResourceType.ram || type == SpResourceType.cores){
            return SpService.ecs;
        }
        if(type == SpResourceType.volumes || type == SpResourceType.snapshots || type == SpResourceType.gigabytes ){
            return SpService.evs;
        }
        if(type == SpResourceType.securityGroup || type == SpResourceType.securityGroupRule || type == SpResourceType.publicIp || type == SpResourceType.vpc || type == SpResourceType.subnet){
            return SpService.vpc;
        }
        return null;
    }

    @Override
    public List<UpQuotaBean> appSystemQuota(Integer appSystemId) {
        UpAppSystem appSystem = dao.load(UpAppSystem.class, appSystemId);
        AssertUtil.check(appSystem.getOwner().getId().compareTo(ThreadCache.getUserId()) == 0, "无操作权限！");
        String sql =
                "with data as (\n" +
                "   select * from up_quota where is_user_template = true\n" +
                "   union \n" +
                "   select * from up_quota where app_system_id = " + appSystemId +
                ")\n" +
                "select service,resource_type,used_quota,quota\n" +
                "   from up_quota " +
                "   where id in (\n" +
                "       select max(id) as maxid from data group by resource_type\n" +
                "     )\n" +
                "   order by service,resource_type";
        List<Object[]> result = queryDao.querySql(sql, null);
        return result.stream().map(item -> {
            UpQuotaBean bean = new UpQuotaBean();
            bean.setService(SpService.valueOf((String) item[0]));
            bean.setServiceText(bean.getService().getTitle());
            bean.setResourceType(SpResourceType.valueOf((String) item[1]));
            bean.setResourceTypeText(bean.getResourceType().getTitle());
            bean.setUsedQuota(item[2] == null ? 0 : (Integer) item[2]);
            bean.setQuota(item[3] == null ? 0 : (Integer) item[3]);
            return bean;
        }).collect(Collectors.toList());
    }
}
