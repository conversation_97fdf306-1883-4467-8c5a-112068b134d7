package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.sdk.bean.SpDeploymentBean;
import io.aicloudware.portal.framework.sdk.bean.SpDeploymentSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpOperateType;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.utility.Utility;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "req_deployment")
@Access(AccessType.FIELD)
public class ReqDeployment extends CmDeployment<ReqDeployment, ReqVm> implements IRequestEntity<SpDeploymentBean> {

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @Column(name = "uuid")
    private String uuid;

    @Column(name = "orig_id")
    private Integer origId;

    @Column(name = "operate_type")
    @Enumerated(EnumType.STRING)
    private UpOperateType operateType;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "reqDeployment")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<ReqVm> vmList;

/*
    // todo : 暂不支持属性组直接绑定到蓝图，必须要绑定到蓝图机器
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "reqDeployment")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<ReqPropertyGroup> propertyGroupList;
*/

    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getOrigId() {
        return origId;
    }

    public void setOrigId(Integer origId) {
        this.origId = origId;
    }

    public UpOperateType getOperateType() {
        return operateType;
    }

    public void setOperateType(UpOperateType operateType) {
        this.operateType = operateType;
    }

    public List<ReqVm> getVmList() {
        return vmList;
    }

    public void setVmList(List<ReqVm> vmList) {
        this.vmList = vmList;
    }

/*
    public List<ReqPropertyGroup> getPropertyGroupList() {
        return propertyGroupList;
    }

    public void setPropertyGroupList(List<ReqPropertyGroup> propertyGroupList) {
        this.propertyGroupList = propertyGroupList;
    }
*/

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<SpDeploymentBean> searchBean, Set<String> aliasSet) {
        SpDeploymentSearchBean bean = (SpDeploymentSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getDeploymentIdList())) {
            criteria.add(Restrictions.in("origId", bean.getDeploymentIdList()));
        }
        if (Utility.isNotEmpty(bean.getApplicationIdList())) {
            criteria.add(Restrictions.in("application.id", bean.getApplicationIdList()));
        }
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }
}
