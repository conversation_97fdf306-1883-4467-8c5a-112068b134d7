package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderK8sClusterBean;
import io.aicloudware.portal.framework.sdk.contants.SpK8sClusterContainerNetworkMode;
import io.aicloudware.portal.platform_vcd.entity.SpK8sCluster;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

import javax.persistence.*;

@Entity
@Table(name = "up_order_k8s_cluster")
@Access(AccessType.FIELD)
public class UpOrderK8sCluster extends UpOrderProduct<UpOrderK8sClusterBean> implements IOrderEntity {

    @Column(name = "flavor")
    private String flavor;

    @Column(name = "host_network_vpc_id")
    private Integer hostNetworkVpcId;

    @Column(name = "host_network_subnet_id")
    private Integer hostNetworkSubnetId;

    @Column(name = "host_network_security_group_id")
    private Integer hostNetworkSecurityGroupId;

    @Column(name = "container_network_mode")
    @Enumerated(EnumType.STRING)
    private SpK8sClusterContainerNetworkMode containerNetworkMode;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "sp_org_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    @JoinColumn(name = "cluster_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpK8sCluster cluster;

    public String getFlavor() {
        return flavor;
    }

    public void setFlavor(String flavor) {
        this.flavor = flavor;
    }

    public Integer getHostNetworkVpcId() {
        return hostNetworkVpcId;
    }

    public void setHostNetworkVpcId(Integer hostNetworkVpcId) {
        this.hostNetworkVpcId = hostNetworkVpcId;
    }

    public Integer getHostNetworkSubnetId() {
        return hostNetworkSubnetId;
    }

    public void setHostNetworkSubnetId(Integer hostNetworkSubnetId) {
        this.hostNetworkSubnetId = hostNetworkSubnetId;
    }

    public Integer getHostNetworkSecurityGroupId() {
        return hostNetworkSecurityGroupId;
    }

    public void setHostNetworkSecurityGroupId(Integer hostNetworkSecurityGroupId) {
        this.hostNetworkSecurityGroupId = hostNetworkSecurityGroupId;
    }

    public SpK8sClusterContainerNetworkMode getContainerNetworkMode() {
        return containerNetworkMode;
    }

    public void setContainerNetworkMode(SpK8sClusterContainerNetworkMode containerNetworkMode) {
        this.containerNetworkMode = containerNetworkMode;
    }

    public SpK8sCluster getCluster() {
        return cluster;
    }

    public void setCluster(SpK8sCluster cluster) {
        this.cluster = cluster;
    }

    @Override
    public UpOrder getOrder() {
        return order;
    }

    @Override
    public void setOrder(UpOrder order) {
        this.order = order;
    }

    @Override
    public SpOrg getSpOrg() {
        return spOrg;
    }

    @Override
    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }
}
