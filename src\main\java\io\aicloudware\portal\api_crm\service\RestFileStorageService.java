package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderFileStorage;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductDiskSetBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderFileStorageBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpFileStorage;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
@Transactional
public class RestFileStorageService extends BaseService implements IRestFileStorageService {

	@Autowired
	private IUpOrderService orderService;

	@Autowired
	private IUpOrderQuotaService upOrderQuotaService;

	@Autowired
	private IUpProductService upProductService;

	@Override
	public Integer save(UpOrderFileStorageBean bean) {
		UpOrderQuotaDetail quotaDetail = upOrderQuotaService.getQuotaDetail(new SpOrg(ThreadCache.getOrgId()), ThreadCache.getRegion(), bean.getDoorOrderItemId());
		UpOrderQuota quota = quotaDetail.getQuota();
		AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
				&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId())
				&& quota.getCatalog() == UpProductSystemEnums.QuotaCatalog.NEW
				&& quotaDetail.getType() == UpProductSystemEnums.ProductType.FILE_STORAGE
				,"非法操作，订单状态异常！");
		AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
			bean.setSizeG(quotaDetail.getDiskG().longValue());
		}else{
			UpProductDiskSetBean diskSetBean = upProductService.getDiskSet(quotaDetail.getProductCode());
			AssertUtil.check(diskSetBean, "产品编码异常");
			bean.setSizeG(diskSetBean.getUnit().longValue());
		}

		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user, "所选用户不存在！");
		
		AssertUtil.check(bean.getSizeG(), "请输入容量！");
		
		UpOrderFileStorage entity = BeanCopyUtil.copy(bean, UpOrderFileStorage.class);

		AssertUtil.check(orderService.queryActiveOrder(OrderType.file_storage, user.getId()) == 0, "您有未完成的文件存储申请！");
		
		entity.setName(bean.getName()+ "_" + String.format("%04d", (int) (Math.random() * 1000)));

		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.file_storage);
		order.setName("[" + OrderType.file_storage + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(user);
		order.setSystemDiskNum(0);
		order.setDiskNum(entity.getSizeG().intValue());
		order.setDiskPrice(BigDecimal.ZERO);
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quota);
		this.dao.insert(order);

		entity.setOwner(user);
		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
		upOrderQuotaService.deployQuotaDetail(quotaDetail);
		return order.getId();
    }

	@Override
	public Integer change(UpOrderFileStorageBean bean) {
		UpOrderQuotaDetail quotaDetail = upOrderQuotaService.getQuotaDetail(new SpOrg(ThreadCache.getOrgId()), ThreadCache.getRegion(), bean.getDoorOrderItemId());
		UpOrderQuota quota = quotaDetail.getQuota();
		AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
				&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId())
				&& quota.getCatalog() == UpProductSystemEnums.QuotaCatalog.UPDATE
				&& quotaDetail.getType() == UpProductSystemEnums.ProductType.FILE_STORAGE
				,"非法操作，订单状态异常！");
		AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");
		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
			bean.setSizeG(quotaDetail.getDiskG().longValue());
		}else{
			UpProductDiskSetBean diskSetBean = upProductService.getDiskSet(quotaDetail.getProductCode());
			AssertUtil.check(diskSetBean, "产品编码异常");
			bean.setSizeG(diskSetBean.getUnit().longValue());
		}

		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user, "所选用户不存在！");
		AssertUtil.check(bean.getSpFileStorageId(), "请选择文件存储！");
		
		SpFileStorage fileStorage = dao.load(SpFileStorage.class, bean.getSpFileStorageId());
		AssertUtil.check(user.getOrg().getId().equals(fileStorage.getSpOrg().getId()), "文件存储所属租户信息异常！");
		
		
		AssertUtil.check(bean.getSizeG(), "请输入容量！");
		
		AssertUtil.check(orderService.queryActiveOrder(OrderType.file_storage_change, user.getId()) == 0, "您有未完成的文件存储变更申请！");

		UpOrder order = new UpOrder();
		order.setRegion(fileStorage.getRegion());
		order.setType(OrderType.file_storage_change);
		order.setName("[" + OrderType.file_storage_change + "]" + fileStorage.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(user);
		order.setSystemDiskNum(0);
		order.setDiskNum(bean.getSizeG().intValue());
		order.setNumber(1);
		order.setSpOrg(user.getOrg());
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quota);
		this.dao.insert(order);

		UpOrderFileStorage entity = new UpOrderFileStorage();
		entity.setName(fileStorage.getName());
		entity.setSizeG(bean.getSizeG());
		entity.setOwner(user);
		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);
		upOrderQuotaService.deployQuotaDetail(quotaDetail);
		return order.getId();
	}

}
