package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.entity.IDataScopeEntity;
import io.aicloudware.portal.framework.sdk.bean.SpVappBean;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import java.util.Date;

@MappedSuperclass
public abstract class CmVapp<D extends CmVapp, V extends CmVm> extends BaseSpEntity<SpVappBean> implements IDataScopeEntity<SpVappBean> {

    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @Column(name = "resource_region")
    private String resourceRegion;

    @JoinColumn(name = "vapp_template_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVappTemplate vappTemplate;

    @Column(name = "create_dt")
    private Date createDt;

    @Column(name = "expire_dt")
    private Date expireDt;

    @Column(name = "expire_months")
    private Integer expireMonths;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpVappTemplate getVappTemplate() {
        return vappTemplate;
    }

    public void setVappTemplate(SpVappTemplate vappTemplate) {
        this.vappTemplate = vappTemplate;
    }

    public Date getCreateDt() {
        return createDt;
    }

    public void setCreateDt(Date createDt) {
        this.createDt = createDt;
    }

    public Date getExpireDt() {
        return expireDt;
    }

    public void setExpireDt(Date expireDt) {
        this.expireDt = expireDt;
    }

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }
}
