package io.aicloudware.portal.platform_vcd.service;

import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Service
@Transactional
public interface ISpRegionService {

    /**
     * 初始化区域服务
     */
    public void init();

    /**
     * 获取所有区域列表
     * @return 区域Bean数组
     */
    public SpRegionBean[] list();

    /**
     * 获取区域映射表
     * @return 区域名称到区域实体的映射
     */
    Map<String, SpRegionEntity> getRegionMap();

    /**
     * 刷新区域映射表
     */
    void refreshRegionMap();

    SpRegionEntity getRegionByName(String name);

    SpRegionEntity getRegionByCode(String code);

    SpRegionBean CIDCRP35();

    SpRegionBean CIDCRP11();

    SpRegionBean CIDCRP12();

    boolean isEdgeSite(SpRegionEntity region);
}
