package io.aicloudware.portal.api_rest.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.BaseBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "Rest请求结果信息")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class RestResponseBean extends BaseBean {

    public static RestResponseBean success(Object data) {
    	return new RestResponseBean(0, "操作成功", "Successful").setData(data);
    }
    
    public static RestResponseBean success() {
    	return new RestResponseBean(0, "操作成功", "Successful");
    }

    public static RestResponseBean error(int resultCode, String errorMsg, String resultMsgCode) {
        return new RestResponseBean(resultCode, errorMsg, resultMsgCode).setData("");
    }

    @ApiModelProperty(value = "错误代码")
    private Integer resultCode;
    @ApiModelProperty(value = "错误消息")
    private String resultMsg;
    @ApiModelProperty(value = "返回结果数据")
    private Object resultData;
    @ApiModelProperty(value = "响应消息编码")
    private String resultMsgCode;

    private RestResponseBean(Integer resultCode, String resultMsg, String resultMsgCode) {
       this.resultCode = resultCode;
       this.resultMsg = resultMsg;
       this.resultMsgCode = resultMsgCode;
    }
    
    public String getResultMsg() {
		return resultMsg;
	}

	public Object getResultData() {
		return resultData;
	}

	public String getResultMsgCode() {
		return resultMsgCode;
	}

	public <T> T getData(Class<T> type) {
        if (type == null) {
            return null;
        }
        if (String.class.equals(type)) {
            return (T) resultData;
        }
        return (T) resultData;
    }

    public RestResponseBean setData(Object data) {
        if (data != null) {
            if (data instanceof String) {
                this.resultData = data.toString();
            } else {
                this.resultData = data;
            }
        }
        return this;
    }

	public Integer getResultCode() {
		return resultCode;
	}

    @Override
    public String toString(){
        String str = "{resultCode:"+resultCode+",resultMsg:"+resultMsg+",resultData:"+(resultData == null ? "" : resultData)+", resultMsgCode: "+resultMsgCode+"}";
        return str;
//        return "{resultCode:"+resultCode+",resultMsg:"+resultMsg+",resultData:"+resultData == null ? "" : resultData+", resultMsgCode: "+resultMsgCode+"}";
    }
}
