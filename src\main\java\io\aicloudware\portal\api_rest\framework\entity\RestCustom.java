package io.aicloudware.portal.api_rest.framework.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.contants.SpOrgRuntimeStatus;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "rest_custom")
@Access(AccessType.FIELD)
public class RestCustom extends BaseEntity {

    public RestCustom(String customNo){
        this.customNo = customNo;
    }
    public RestCustom(){}
    // 必传 集团编码(即客户唯一标识)O
    @Column(name = "custom_no")
    private String customNo;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "custom")
    @Where(clause = "status!='deleted'")
    @org.hibernate.annotations.OrderBy(clause = "id")
    private List<UpUser> userList;

    @EntityProperty(isNullCopy = false)
    @Column(name = "runtime_status")
    @Enumerated(EnumType.STRING)
    private SpOrgRuntimeStatus runtimeStatus;

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public List<UpUser> getUserList() {
        return userList;
    }

    public void setUserList(List<UpUser> userList) {
        this.userList = userList;
    }

    public SpOrgRuntimeStatus getRuntimeStatus() {
        return runtimeStatus;
    }

    public void setRuntimeStatus(SpOrgRuntimeStatus runtimeStatus) {
        this.runtimeStatus = runtimeStatus;
    }
}
