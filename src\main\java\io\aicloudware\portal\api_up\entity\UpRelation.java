package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpRelationBean;
import io.aicloudware.portal.framework.sdk.contants.UpRelationType;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "up_relation")
@Access(AccessType.FIELD)
public final class UpRelation extends BaseUpEntity<UpRelationBean> {

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpRelationType type;

    @JoinColumn(name = "user_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser user;

    @JoinColumn(name = "role_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpRole role;

    @JoinColumn(name = "sub_role_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpRole subRole;

    @JoinColumn(name = "right_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpRight right;

    public UpRelationType getType() {
        return type;
    }

    public void setType(UpRelationType type) {
        this.type = type;
    }

    public UpUser getUser() {
        return user;
    }

    public void setUser(UpUser user) {
        this.user = user;
    }

    public UpRole getRole() {
        return role;
    }

    public void setRole(UpRole role) {
        this.role = role;
    }

    public UpRole getSubRole() {
        return subRole;
    }

    public void setSubRole(UpRole subRole) {
        this.subRole = subRole;
    }

    public UpRight getRight() {
        return right;
    }

    public void setRight(UpRight right) {
        this.right = right;
    }
}
