package io.aicloudware.portal.api_vcpp.controller.profile;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_vcpp.entity.UpWorkSheet;
import io.aicloudware.portal.api_vcpp.service.profile.IUpWorkSheetService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetDetailBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetResultBean;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetChannel;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetStatus;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetType;

/**
 * 工单
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/workSheet")
public class UpWorkSheetController extends BaseUpController<UpWorkSheet, UpWorkSheetBean, UpWorkSheetResultBean> {

	@Autowired
	private IUpWorkSheetService workSheetService;

	/**
	 * 配置
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/init")
	@ResponseBody
	public ResponseBean init(HttpServletRequest request) {
		Map<String, Map<String, ?>> datas = new HashMap<>();
		// 工单状态
		Map<String, String> workSheetStatus = new LinkedHashMap<String, String>();
		for (UpWorkSheetStatus type : UpWorkSheetStatus.values()) {
			workSheetStatus.put(type.toString(), type.getTitle());
		}
		datas.put("workSheetStatus", workSheetStatus);

		// 工单类型
		Map<String, String> workSheetType = new LinkedHashMap<String, String>();
		for (UpWorkSheetType type : UpWorkSheetType.values()) {
			workSheetType.put(type.toString(), type.getTitle());
		}
		datas.put("workSheetType", workSheetType);

		// 工单类型
		Map<String, String> workSheetChannel = new LinkedHashMap<String, String>();
		for (UpWorkSheetChannel type : UpWorkSheetChannel.values()) {
			workSheetChannel.put(type.toString(), type.getTitle());
		}
		datas.put("workSheetChannel", workSheetChannel);

		return ResponseBean.success(datas);
	}

	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/query", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean query(@RequestBody UpWorkSheetSearchBean searchBean, HttpServletRequest request) {
		UpWorkSheetResultBean result = workSheetService.query(searchBean,ThreadCache.getUserId());
		return ResponseBean.success(result);
	}

	/**
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/detail", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean detail(@RequestBody Integer workSheetId, HttpServletRequest request) {
		Integer userId = ThreadCache.getUserId();
		return ResponseBean.success(workSheetService.detail(workSheetId, userId));
	}

	/**
	 * 保存
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean save(@RequestBody UpWorkSheetDetailBean bean, HttpServletRequest request) {
		Integer userId = ThreadCache.getUserId();
		return ResponseBean.success(workSheetService.save(bean, userId));
	}

	/**
	 * 更新
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean save(@RequestBody UpWorkSheetBean bean, HttpServletRequest request) {
		Integer userId = ThreadCache.getUserId();
		return ResponseBean.success(workSheetService.update(bean, userId));
	}

}