package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpAuditLogBean;
import io.aicloudware.portal.framework.sdk.bean.SpAuditLogResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpAuditLogSearchBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmResultBean;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpAuditLog;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/api/v2/auditlog")
@Api(value = "/api/v2/auditlog", description = "审计日志", position = 140)
public class RestApiV2AuditLogController extends BaseEntityController<SpAuditLog, SpAuditLogBean, SpAuditLogResultBean> {

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVmResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpAuditLogSearchBean searchBean) {
    	SpAuditLog entity = BeanCopyUtil.copy(searchBean.getBean(), SpAuditLog.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        if (StringUtils.isNotEmpty(entity.getName())) {
        	SpAuditLogBean fuzzyBean = new SpAuditLogBean();
//            fuzzyBean.setName(entity.getName());
            fuzzyBean.setDescription(entity.getName());
            fuzzyBean.setTargetName(entity.getName());
            fuzzyBean.setTargetUuid(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        try {
        	SpAuditLogBean[] entityList = commonService.query(searchBean, entity, SpAuditLogBean.class);
        	SpAuditLogResultBean result = new SpAuditLogResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBean.error(1, "", e.getMessage());
        }
    }
    

}
