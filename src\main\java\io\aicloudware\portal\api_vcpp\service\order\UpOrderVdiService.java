package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderVdi;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderVdiBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class UpOrderVdiService extends BaseService implements IUpOrderVdiService {

    @Override
    public Integer save(UpOrderVdiBean bean, UpUser applyUser) {
        UpUser user = dao.load(UpUser.class, bean.getOwnerId());
        AssertUtil.check(user, "所选用户不存在！");

        AssertUtil.check(bean.getServicePlanId(), "请选择配置！");
        AssertUtil.check(bean.getImageId(), "请选择镜像！");
        AssertUtil.check(bean.getVpcId(), "请选择专有网络！");
//		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
//		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在！");
//		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC不存在！");
        AssertUtil.check(bean.getNetworkId(), "请选择子网！");
//		SpOVDCNetwork ovdcNetwork = this.dao.load(SpOVDCNetwork.class, bean.getNetworkId());
//		AssertUtil.check(ovdcNetwork != null && RecordStatus.active.equals(ovdcNetwork.getStatus()), "子网不存在！");
//		AssertUtil.check(ovdcNetwork.getSpOrg().getId().equals(user.getOrg().getId()), "子网不存在！");

        AssertUtil.check(bean.getName(), "请输入桌面名！");


        SpVappTemplate template = this.dao.load(SpVappTemplate.class, bean.getImageId());

        UpOrder order = new UpOrder();
        order.setRegion(ThreadCache.getRegion());
        order.setType(OrderType.new_vdi);
        order.setName("[" + OrderType.new_cloud_server + "]" + bean.getName());
        order.setOrderStatus(OrderStatus.pending_deploy);
        order.setOwner(user);
        order.setApplyUser(applyUser);
//		order.setPaymentType(bean.getPaymentType());
        order.setSpOrg(user.getOrg());
        order.setNumber(1);
        this.dao.insert(order);

        String random = System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000));
        UpOrderVdi vdiOrder = new UpOrderVdi();
        vdiOrder.setOwner(user);
        vdiOrder.setRegion(order.getRegion());
        vdiOrder.setName(bean.getName() + "-" + random);
        vdiOrder.setOrder(order);
        vdiOrder.setSpOrg(user.getOrg());
        vdiOrder.setRegion(order.getRegion());
        vdiOrder.setCpu(bean.getCpu());
        vdiOrder.setMemory(bean.getMemory());
        vdiOrder.setServicePlanId(bean.getServicePlanId());
        vdiOrder.setDisk(bean.getDisk());
        vdiOrder.setImageId(bean.getImageId());
        vdiOrder.setVpcId(bean.getVpcId());
        vdiOrder.setNetworkId(bean.getNetworkId());
        this.dao.insert(vdiOrder);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(user.getOrg());
        task.setRegion(order.getRegion());
        dao.insert(task);
        return order.getId();
    }

}
