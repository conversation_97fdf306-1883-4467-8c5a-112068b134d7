package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.MappedSuperclass;

import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.sdk.contants.UpOperateType;

@MappedSuperclass
public abstract class UpOrderProduct<B extends RecordBean> extends BaseUpEntity<B> implements IRequestEntity<B>{

    @Override
    public Integer getOrigId() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void setOrigId(Integer origId) {
        // TODO Auto-generated method stub
        
    }

    @Override
    public UpOperateType getOperateType() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void setOperateType(UpOperateType operateType) {
        // TODO Auto-generated method stub
        
    }

}
