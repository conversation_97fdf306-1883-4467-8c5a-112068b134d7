package io.aicloudware.portal.platform_vcd.entity;

import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.framework.sdk.contants.CloudType;
import io.aicloudware.portal.framework.sdk.contants.SpSecurityVersion;

import javax.persistence.*;

@Entity
@Table(name = "sp_region")
@Access(AccessType.FIELD)
public class SpRegionEntity extends BaseEntity<SpRegionBean> implements IEntity<SpRegionBean> {
    public SpRegionEntity() {
    }

    public SpRegionEntity(Integer id) {
        super(id);
    }

    @Column(name = "title")
    private String title;

    @Column(name = "code")
    private String code;

    @Column(name = "security_url_port")
    private String securityUrlPort;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private CloudType type;

    @Column(name = "version")
    @Enumerated(EnumType.STRING)
    private SpSecurityVersion version;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSecurityUrlPort() {
        return securityUrlPort;
    }

    public void setSecurityUrlPort(String securityUrlPort) {
        this.securityUrlPort = securityUrlPort;
    }

    public CloudType getType() {
        return type;
    }

    public void setType(CloudType type) {
        this.type = type;
    }

    public SpSecurityVersion getVersion() {
        return version;
    }

    public void setVersion(SpSecurityVersion version) {
        this.version = version;
    }

    public String name(){
        return super.getName();
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof SpRegionEntity){
            return ((SpRegionEntity)obj).getName().equals(getName());
        }
        return super.equals(obj);
    }
}
