package io.aicloudware.portal.api_crm.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "云服务器")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, RestV2CloudDiskBean.class})
public class RestV2CloudDiskBean extends UpOrderCloudDiskBean {
//
//	@ApiModelProperty(value = "Quota code")
//	private String doorOrderItemId;
//
//	public String getDoorOrderItemId() {
//		return doorOrderItemId;
//	}
//
//	public void setDoorOrderItemId(String doorOrderItemId) {
//		this.doorOrderItemId = doorOrderItemId;
//	}
}
