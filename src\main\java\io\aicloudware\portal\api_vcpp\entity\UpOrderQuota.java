package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_rest.framework.entity.RestCustom;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaBean;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaStatus;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "up_order_quota")
@Access(AccessType.FIELD)
public class UpOrderQuota extends BaseUpEntity<UpOrderQuotaBean> {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7314673339082820226L;

	public UpOrderQuota() {}
	
	public UpOrderQuota(SpRegionEntity region, QuotaCatalog catalog, String code, String name, UpUser owner, SpOrg spOrg, ProductType type, String customNo) {
		super.setRegion(region);
		this.code = code;
		this.setName(name);
		this.owner = owner;
		this.quotaStatus = QuotaStatus.unfinish;
		this.spOrg = spOrg;
		this.type = type;
		this.catalog = catalog;
		this.customNo = customNo;
	}
	
	@Column(name = "code")
	private String code;
	
	@Column(name = "child_code")
	private String childCode;
	
	@JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;
	
	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private ProductType type;
	
	@Column(name = "catalog")
	@Enumerated(EnumType.STRING)
	private QuotaCatalog catalog;
	
    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;
    
    @Column(name = "quota_status")
    @Enumerated(EnumType.STRING)
    private QuotaStatus quotaStatus;

	@Column(name = "custom_no")
    private String customNo;
    
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "quota")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<UpOrderQuotaDetail> quotaDetailList;

	@JoinColumn(name = "rest_custom_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private RestCustom restCustom;

	@Column(name = "source_region")
	private String oldSourceRegion;

	@EntityProperty(isNullCopy = false)
	@JoinColumn(name = "source_region_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpRegionEntity sourceRegion;

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public ProductType getType() {
		return type;
	}

	public void setType(ProductType type) {
		this.type = type;
	}

	public QuotaStatus getQuotaStatus() {
		return quotaStatus;
	}

	public void setQuotaStatus(QuotaStatus quotaStatus) {
		this.quotaStatus = quotaStatus;
	}

	public List<UpOrderQuotaDetail> getQuotaDetailList() {
		return quotaDetailList;
	}

	public void setQuotaDetailList(List<UpOrderQuotaDetail> quotaDetailList) {
		this.quotaDetailList = quotaDetailList;
	}

	public String getChildCode() {
		return childCode;
	}

	public void setChildCode(String childCode) {
		this.childCode = childCode;
	}

	public QuotaCatalog getCatalog() {
		return catalog;
	}

	public void setCatalog(QuotaCatalog catalog) {
		this.catalog = catalog;
	}

	public String getCustomNo() {
		return customNo;
	}

	public void setCustomNo(String customNo) {
		this.customNo = customNo;
	}

	public RestCustom getRestCustom() {
		return restCustom;
	}

	public void setRestCustom(RestCustom restCustom) {
		this.restCustom = restCustom;
	}

	public String getOldSourceRegion() {
		return oldSourceRegion;
	}

	public void setOldSourceRegion(String oldSourceRegion) {
		this.oldSourceRegion = oldSourceRegion;
	}

	public SpRegionEntity getSourceRegion() {
		return sourceRegion;
	}

	public void setSourceRegion(SpRegionEntity sourceRegion) {
		this.sourceRegion = sourceRegion;
	}
}
