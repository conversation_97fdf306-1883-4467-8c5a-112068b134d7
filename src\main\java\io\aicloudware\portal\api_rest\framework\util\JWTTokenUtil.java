package io.aicloudware.portal.api_rest.framework.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.twmacinta.util.MD5;
import io.aicloudware.portal.api_rest.framework.bean.GenTokenParams;

import net.sf.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLDecoder;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

//@Component  // 需要添加Component注释才会被springboot管理
public class JWTTokenUtil {

    // springboot不允许/不支持把值注入到静态变量中
    // springboot支持通过set方法实现注入，我们可以利用非静态set方法注入静态变量
//    @Value("${jwt.secret}")
    private static String secret = "zfc7ovvXX4rEvyIPyFO48A=="; //密钥

    //    @Value("${jwt.expiresAt}")
    private static String expiresAt = "120"; //token有效时间 （分钟）

    // *核心：通过非静态set方法实现注入*
//    @Value("${jwt.secret}")
    public void setSECRET(String secret) {
        JWTTokenUtil.secret = secret;
    }

//    @Value("${jwt.expiresAt}")
    public void setExpiresAt(String expiresAt) {
        JWTTokenUtil.expiresAt = expiresAt;
    }

    /**
     * 根据key从token获取值
     * @param claims
     * @param key
     * @return
     */
    private static String getClaimAsString(Map<String, Claim> claims, String key) {
        if (claims.containsKey(key)) {
            return claims.get(key).asString();
        }
        return null;
    }

    /**
     * 根据key从token获取值
     * @param claims
     * @param key
     * @return
     */
    private static Integer getClaimAsInt(Map<String, Claim> claims, String key) {
        if (claims.containsKey(key)) {
            return claims.get(key).asInt();
        }
        return null;
    }

    /**
     * 从token中获取 参数不做验证
     * @param token
     * @return
     * @throws IllegalAccessException 
     * @throws IllegalArgumentException 
     * @throws UserNotLoginException
     * @throws UnsupportedEncodingException
     */
    public static GenTokenParams getParamsByToken2(String token) throws IllegalArgumentException, IllegalAccessException {
        GenTokenParams genTokenParams = new GenTokenParams();
        Map<String, Claim> claims = JWT.decode(token).getClaims();
        Field[] fields = genTokenParams.getClass().getDeclaredFields();
        StringBuilder hashStringBuilder = new StringBuilder();
        if (fields != null && fields.length > 0) {
            // 是否是第一个字段
            boolean first = true;
            for (Field field : fields) {
                field.setAccessible(true);
                String key = field.getName();
                String val = getClaimAsString(claims, key);
                    field.set(genTokenParams, val);
                    if (first) {
                        first = false;
                    }
                    else {
                        hashStringBuilder.append("&");
                    }
                    hashStringBuilder.append(key).append("=").append(val);
            }
        }
        return genTokenParams;
    }

    /**
     * 从token中获取 参数
     * @param token
     * @return
     * @throws IllegalAccessException 
     * @throws IllegalArgumentException 
     * @throws UserNotLoginException
     * @throws UnsupportedEncodingException
     */
    public static GenTokenParams getParamsByToken(String token) throws IllegalArgumentException, IllegalAccessException, UnsupportedEncodingException{
        GenTokenParams genTokenParams = new GenTokenParams();
        Map<String, Claim> claims = JWT.decode(token).getClaims();
        Field[] fields = genTokenParams.getClass().getDeclaredFields();
        StringBuilder hashStringBuilder = new StringBuilder();
        if (fields != null && fields.length > 0) {
            // 是否是第一个字段
            boolean first = true;
            for (Field field : fields) {
                field.setAccessible(true);
                String key = field.getName();
                String val = getClaimAsString(claims, key);
                    field.set(genTokenParams, val);
                    if (first) {
                        first = false;
                    }
                    else {
                        hashStringBuilder.append("&");
                    }
                    hashStringBuilder.append(key).append("=").append(val);
            }
        }
        String selfToken = getClaimAsString(claims, "selfToken");

        int exp = getClaimAsInt(claims, "exp");
        int iat = getClaimAsInt(claims, "iat");
        hashStringBuilder.append("&secret=").append(secret)
                .append("&exp=").append(String.valueOf(exp))
                .append("&iat=").append(String.valueOf(iat));
        MD5 md5 = new MD5();
        md5.Update(hashStringBuilder.toString(), null);
        if (!selfToken.equals(md5.asHex())) {
            throw new RuntimeException("校验不通过，无效token，请重新登录");
        }
        return genTokenParams;
    }

    /**
     * 校验token
     *
     * @param token
     * @throws UserNotLoginException
     */
    public static void validToken(String token)  {
        // 验证 token
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(secret)).build();
        try {
            jwtVerifier.verify(token);
        } catch (JWTVerificationException e) {
        	throw new RuntimeException("token失效，请重新登录");
        }
    }

    /**
     * 生成token
     * @param params
     * @return
     * @throws IllegalAccessException 
     * @throws IllegalArgumentException 
     * @throws UnsupportedEncodingException 
     */
    public static String createToken(GenTokenParams params) throws IllegalArgumentException, IllegalAccessException, UnsupportedEncodingException {
        Date iat = new Date();
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE, Integer.parseInt(expiresAt));
        Date exp = nowTime.getTime();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("alg", "HS256");
        map.put("typ", "JWT");
        String token = "";
            JWTCreator.Builder builder = JWT.create().withHeader(map);
            StringBuilder hashStringBuilder = new StringBuilder();
            Field[] fields = params.getClass().getDeclaredFields();
            if ((fields != null) && (fields.length > 0)) {
                // 第一个 字段
                boolean first = true;
                for (Field field : fields) {
                    // 设置字段可以访问
                    field.setAccessible(true);
                    String val = null;
                        String key = field.getName();
                        val = (String) field.get(params);
                        builder.withClaim(key, val);
                        // 第一个字段不用加&
                        if (first) {
                            first = false;
                        }
                        else {
                            hashStringBuilder.append("&");
                        }
                        hashStringBuilder.append(key).append("=").append(val);
                }
                hashStringBuilder.append("&secret=").append(secret)
                        .append("&exp=").append(String.valueOf(exp.getTime() / 1000))
                        .append("&iat=").append(String.valueOf(iat.getTime() / 1000));
            }
            MD5 md5 = new MD5();
            md5.Update(hashStringBuilder.toString(), null);
            token = builder.withClaim("selfToken", md5.asHex())
                    .withExpiresAt(exp)
                    .withIssuedAt(iat)
                    .sign(Algorithm.HMAC256(secret));
        return token;
    }
    
    public static void main(String[] args) throws IllegalArgumentException, IllegalAccessException, UnsupportedEncodingException {
    	String str = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwYXJlbnRNZW1iZXJJZCI6IjEwMDAxIiwiY2hpbGREZWZGbGFnIjoiMCIsInNlbGZUb2tlbiI6IjcxZmI1ZTVhYWY2MzA2OWFlNzVkNWQxMjQ4NzkwYzI4IiwiaWQiOiIxMzIzMTkyMTk1MjIxNjM1MDc0Iiwic2Vzc2lvbklkIjoiMjRFNDI0NTQxNkEzNDE0NjUxNzY0NzQ5ODhGMUJFRTciLCJ0eXBlIjoiTUVNQkVSIiwiZXhwIjoxNjA3MDA3ODE4LCJpYXQiOjE2MDcwMDA2MTh9.zs8Znxpu2TqSBrPWXjltHXxjhDNK5xGXFTbuI3dNEFY";
		GenTokenParams params = getParamsByToken(URLDecoder.decode(str));
		System.out.println(JSONObject.fromObject(params));
	}
}
