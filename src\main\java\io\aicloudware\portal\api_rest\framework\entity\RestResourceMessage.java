package io.aicloudware.portal.api_rest.framework.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_rest.framework.bean.RestResourceMessageBean;
import io.aicloudware.portal.framework.entity.BaseUpEntity;

@Entity
@Table(name = "rest_resource_message")
@Access(AccessType.FIELD)
public class RestResourceMessage extends BaseUpEntity<RestResourceMessageBean> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -979250011379708129L;

	@Column(name = "user_id")
	private String userId;
	
	@Column(name = "main_agreement_id")
	private String mainAgreementId;
	
	@Column(name = "door_order_item_id")
	private String doorOrderItemId;
	
	@Column(name = "inst_id")
	private String instId;
	
	@Column(name = "inst_name")
	private String instName;
	
	@Column(name = "resource_pool_id")
	private String resourcePoolId;
	
	@Column(name = "vpc_id")
	private String vpcId;
	
	@Column(name = "subnet_segment")
	private String subnetSegment;
	
	@Column(name = "ip")
	private String ip;
	
	@Column(name = "operation_type")
	private String operationType;
	
	@JoinColumn(name = "vpc_info_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private RestVpcInfo vpcInfo;
	
	@Column(name = "city_id")
	private String cityId;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getMainAgreementId() {
		return mainAgreementId;
	}

	public void setMainAgreementId(String mainAgreementId) {
		this.mainAgreementId = mainAgreementId;
	}

	public String getDoorOrderItemId() {
		return doorOrderItemId;
	}

	public void setDoorOrderItemId(String doorOrderItemId) {
		this.doorOrderItemId = doorOrderItemId;
	}

	public String getInstId() {
		return instId;
	}

	public void setInstId(String instId) {
		this.instId = instId;
	}

	public String getInstName() {
		return instName;
	}

	public void setInstName(String instName) {
		this.instName = instName;
	}

	public String getResourcePoolId() {
		return resourcePoolId;
	}

	public void setResourcePoolId(String resourcePoolId) {
		this.resourcePoolId = resourcePoolId;
	}

	public String getVpcId() {
		return vpcId;
	}

	public void setVpcId(String vpcId) {
		this.vpcId = vpcId;
	}

	public String getSubnetSegment() {
		return subnetSegment;
	}

	public void setSubnetSegment(String subnetSegment) {
		this.subnetSegment = subnetSegment;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public RestVpcInfo getVpcInfo() {
		return vpcInfo;
	}

	public void setVpcInfo(RestVpcInfo vpcInfo) {
		this.vpcInfo = vpcInfo;
	}

	public String getCityId() {
		return cityId;
	}

	public void setCityId(String cityId) {
		this.cityId = cityId;
	}
}
	