package io.aicloudware.portal.framework.sdk.contants;

public enum SpServiceCatalog {
    compute("计算", new SpService[]{SpService.ecs, SpService.evs}),
    vdi("云桌面", new SpService[]{SpService.vdi}),
    network("网络与安全", new SpService[]{SpService.vpc, SpService.securityGroup, SpService.elasticip}),
    storage("存储", new SpService[]{SpService.fileStorage, SpService.objectStorage}),
    k8s("容器", new SpService[]{SpService.k8s}),
    database("云数据库", new SpService[]{SpService.redis, SpService.rds}),
    message("中间件", new SpService[]{SpService.kafka, SpService.rabbitmq}),;

    private final String title;

    private final SpService[] services;

    private SpServiceCatalog(String title, SpService[] services) {
        this.title = title;
        this.services = services;
    }

    public String getTitle() {
        return title;
    }

    public SpService[] getServices() {
        return services;
    }
}
