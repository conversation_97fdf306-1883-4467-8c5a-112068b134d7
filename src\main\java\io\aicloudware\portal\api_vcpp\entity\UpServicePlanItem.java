package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpServicePlanItemBean;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemSubType;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemType;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;

import javax.persistence.*;

@Entity
@Table(name = "up_service_plan_item")
@Access(AccessType.FIELD)
public final class UpServicePlanItem extends BaseEntity<UpServicePlanItemBean> {

	@Column(name = "service_plan_item_type")
	@Enumerated(EnumType.STRING)
	private UpServicePlanItemType servicePlanItemType;

	@Column(name = "service_plan_item_sub_type")
	@Enumerated(EnumType.STRING)
	private UpServicePlanItemSubType servicePlanItemSubType;

	@Column(name = "amount")
	private Integer amount;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "vapp_template_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpVappTemplate image;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "service_plan_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpServicePlan servicePlan;

	public UpServicePlanItemType getServicePlanItemType() {
		return servicePlanItemType;
	}

	public void setServicePlanItemType(UpServicePlanItemType servicePlanItemType) {
		this.servicePlanItemType = servicePlanItemType;
	}

	public UpServicePlanItemSubType getServicePlanItemSubType() {
		return servicePlanItemSubType;
	}

	public void setServicePlanItemSubType(UpServicePlanItemSubType servicePlanItemSubType) {
		this.servicePlanItemSubType = servicePlanItemSubType;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public UpServicePlan getServicePlan() {
		return servicePlan;
	}

	public void setServicePlan(UpServicePlan servicePlan) {
		this.servicePlan = servicePlan;
	}

	public SpVappTemplate getImage() {
		return image;
	}

	public void setImage(SpVappTemplate image) {
		this.image = image;
	}
}
