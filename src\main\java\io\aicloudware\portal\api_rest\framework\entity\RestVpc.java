package io.aicloudware.portal.api_rest.framework.entity;

import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import io.aicloudware.portal.api_rest.framework.bean.RestVpcBean;
import io.aicloudware.portal.framework.entity.BaseUpEntity;

@Entity
@Table(name = "rest_vpc")
@Access(AccessType.FIELD)
public class RestVpc extends BaseUpEntity<RestVpcBean> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 299480164511113178L;

	@Column(name = "vpc_id")
	private String vpcId;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "vpc")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<RestSubnet> subnets;
	
	@JoinColumn(name = "cloud_wlan_object_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private RestCloudWlanObject cloudWlanObject;
	
	@Column(name = "vlan_id")
	private String vlanId;
	
	@Column(name = "resource_pool_id")
	private String resourcePoolId;

	public String getVpcId() {
		return vpcId;
	}

	public void setVpcId(String vpcId) {
		this.vpcId = vpcId;
	}

	public List<RestSubnet> getSubnets() {
		return subnets;
	}

	public void setSubnets(List<RestSubnet> subnets) {
		this.subnets = subnets;
	}

	public String getVlanId() {
		return vlanId;
	}

	public void setVlanId(String vlanId) {
		this.vlanId = vlanId;
	}

	public RestCloudWlanObject getCloudWlanObject() {
		return cloudWlanObject;
	}

	public void setCloudWlanObject(RestCloudWlanObject cloudWlanObject) {
		this.cloudWlanObject = cloudWlanObject;
	}

	public String getResourcePoolId() {
		return resourcePoolId;
	}

	public void setResourcePoolId(String resourcePoolId) {
		this.resourcePoolId = resourcePoolId;
	}
	
}
	