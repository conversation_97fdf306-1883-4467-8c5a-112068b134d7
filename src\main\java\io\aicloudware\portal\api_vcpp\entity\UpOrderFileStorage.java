package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderFileStorageBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.FileStorageType;
import io.aicloudware.portal.platform_vcd.entity.SpFileStorage;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_order_file_storage")
@Access(AccessType.FIELD)
public class UpOrderFileStorage extends UpOrderProduct<UpOrderFileStorageBean> implements IOrderEntity{
	
	@JoinColumn(name = "quota_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderQuota quota;
	
	@JoinColumn(name = "quota_detail_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderQuotaDetail quotaDetail;
	
	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private FileStorageType type;
	
	@JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;
	
	@JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;
	
	@JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;
	
	@Column(name = "size_g", nullable = false)
    private Long sizeG;
	
	@Column(name = "disk_config_id")
	private Integer diskConfigId;
	
	@JoinColumn(name = "file_storage_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpFileStorage spFileStorage;

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public Long getSizeG() {
		return sizeG;
	}

	public void setSizeG(Long sizeG) {
		this.sizeG = sizeG;
	}

	public Integer getDiskConfigId() {
		return diskConfigId;
	}

	public void setDiskConfigId(Integer diskConfigId) {
		this.diskConfigId = diskConfigId;
	}

	public SpFileStorage getSpFileStorage() {
		return spFileStorage;
	}

	public void setSpFileStorage(SpFileStorage spFileStorage) {
		this.spFileStorage = spFileStorage;
	}

	public UpOrderQuota getQuota() {
		return quota;
	}

	public void setQuota(UpOrderQuota quota) {
		this.quota = quota;
	}

	public UpOrderQuotaDetail getQuotaDetail() {
		return quotaDetail;
	}

	public void setQuotaDetail(UpOrderQuotaDetail quotaDetail) {
		this.quotaDetail = quotaDetail;
	}

	public FileStorageType getType() {
		return type;
	}

	public void setType(FileStorageType type) {
		this.type = type;
	}
	
	
}
