package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;
import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductItemBean;
import io.aicloudware.portal.framework.sdk.contants.ProductEnums.ProductItemType;

@Entity
@Table(name = "up_product_item")
@Access(AccessType.FIELD)
public class UpProductItem extends BaseUpEntity<UpProductItemBean> {

	private static final long serialVersionUID = 3257860816268293662L;

	@Column(name = "type", nullable = false)
	@Enumerated(EnumType.STRING)
	private ProductItemType type;

	@Column(name = "sub_type")
	private String subType;

	@Column(name = "unit")
	private Integer unit;

	@Column(name = "price")
	private BigDecimal price;
	
	@Column(name = "max_value")
	private Integer maxValue;
	
	@Column(name = "min_value")
	private Integer minValue;
	
	@Column(name = "step")
	private Integer step;
	
	@Column(name = "seq")
	private Integer seq;

	@Column(name = "enabled")
	private Boolean enabled;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "cpuProductItem")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<UpProductVmSet> cpuSetList;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "memoryProductItem")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<UpProductVmSet> memorySetList;
	
	public ProductItemType getType() {
		return type;
	}

	public void setType(ProductItemType type) {
		this.type = type;
	}

	public String getSubType() {
		return subType;
	}

	public void setSubType(String subType) {
		this.subType = subType;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public List<UpProductVmSet> getCpuSetList() {
		return cpuSetList;
	}

	public void setCpuSetList(List<UpProductVmSet> cpuSetList) {
		this.cpuSetList = cpuSetList;
	}

	public List<UpProductVmSet> getMemorySetList() {
		return memorySetList;
	}

	public void setMemorySetList(List<UpProductVmSet> memorySetList) {
		this.memorySetList = memorySetList;
	}

	public Integer getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(Integer maxValue) {
		this.maxValue = maxValue;
	}

	public Integer getMinValue() {
		return minValue;
	}

	public void setMinValue(Integer minValue) {
		this.minValue = minValue;
	}

	public Integer getStep() {
		return step;
	}

	public void setStep(Integer step) {
		this.step = step;
	}
	
}
