package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderVdiPoolBean;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;

import javax.persistence.*;

@Entity
@Table(name = "up_order_vdi_pool")
@Access(AccessType.FIELD)
public class UpOrderVdiPool extends UpOrderProduct<UpOrderVdiPoolBean> implements IOrderEntity {

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "service_plan_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpServicePlan servicePlan;

	@Column(name = "cpu")
	private Integer cpu;

	@Column(name = "memory")
	private Integer memory;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "image_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpVappTemplate image;

	@Column(name = "vpc_id")
	private Integer vpcId;

	@Column(name = "network_id")
	private Integer networkId;

	@Override
	public UpOrder getOrder() {
		return order;
	}

	@Override
	public void setOrder(UpOrder order) {
		this.order = order;
	}

	@Override
	public SpOrg getSpOrg() {
		return spOrg;
	}

	@Override
	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public UpServicePlan getServicePlan() {
		return servicePlan;
	}

	public void setServicePlan(UpServicePlan servicePlan) {
		this.servicePlan = servicePlan;
	}

	public Integer getCpu() {
		return cpu;
	}

	public void setCpu(Integer cpu) {
		this.cpu = cpu;
	}

	public Integer getMemory() {
		return memory;
	}

	public void setMemory(Integer memory) {
		this.memory = memory;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Integer networkId) {
		this.networkId = networkId;
	}

	public SpVappTemplate getImage() {
		return image;
	}

	public void setImage(SpVappTemplate image) {
		this.image = image;
	}
}
