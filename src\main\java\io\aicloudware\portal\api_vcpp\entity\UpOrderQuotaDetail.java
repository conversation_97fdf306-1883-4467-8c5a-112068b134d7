package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderQuotaDetailBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;

import javax.persistence.*;

@Entity
@Table(name = "up_order_quota_detail")
@Access(AccessType.FIELD)
public class UpOrderQuotaDetail extends BaseUpEntity<UpOrderQuotaDetailBean> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6990777389126008876L;

	public UpOrderQuotaDetail() {}
	
	public UpOrderQuotaDetail(SpRegionEntity region, QuotaCatalog catalog, String name, String productCode, SpOrg spOrg, String subCode, ProductType type) {
		super.setRegion(region);
		this.catalog = catalog;
		super.setName(name);
		this.productCode = productCode;
		this.spOrg = spOrg;
		this.subCode = subCode;
		this.type = type;
		this.quotaDetailStatus = QuotaDetailStatus.start;
	}
	
	// 记录更新实例ID
	@Column(name = "target_id")
	private Integer targetId;
	
	@Column(name = "sub_code")
	private String subCode;
	
	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private ProductType type;
	
	@Column(name = "catalog")
	@Enumerated(EnumType.STRING)
	private QuotaCatalog catalog;
	
	@JoinColumn(name = "sp_org_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;
	
	@Column(name = "product_code")
	private String productCode;
	
	@JoinColumn(name = "order_quota_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderQuota quota;
	
	@Column(name = "quota_detail_status")
    @Enumerated(EnumType.STRING)
    private QuotaDetailStatus quotaDetailStatus;
	
	@Column(name = "value")
	private String value;
	
	@Column(name = "is_update_success")
	private Boolean isUpdateSuccess;

	@Column(name = "source_region")
	private String oldSourceRegion;

	@EntityProperty(isNullCopy = false)
	@JoinColumn(name = "source_region_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpRegionEntity sourceRegion;


	@Column(name = "cpu")
	private Integer cpu;

	@Column(name = "memory_g")
	private Integer memoryG;

	@Column(name = "disk_g")
	private Integer diskG;

	@Column(name = "disk_type")
	private Integer diskType;

	@Column(name = "is_custom")
	private Boolean isCustom;

	@Column(name = "channel")
	@Enumerated(EnumType.STRING)
	private UpProductSystemEnums.QuotaDetailChannel channel;
	
	public String getSubCode() {
		return subCode;
	}

	public void setSubCode(String subCode) {
		this.subCode = subCode;
	}

	public ProductType getType() {
		return type;
	}

	public void setType(ProductType type) {
		this.type = type;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public UpOrderQuota getQuota() {
		return quota;
	}

	public void setQuota(UpOrderQuota quota) {
		this.quota = quota;
	}

	public QuotaDetailStatus getQuotaDetailStatus() {
		return quotaDetailStatus;
	}

	public void setQuotaDetailStatus(QuotaDetailStatus quotaDetailStatus) {
		this.quotaDetailStatus = quotaDetailStatus;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public QuotaCatalog getCatalog() {
		return catalog;
	}

	public void setCatalog(QuotaCatalog catalog) {
		this.catalog = catalog;
	}

	public Integer getTargetId() {
		return targetId;
	}

	public void setTargetId(Integer targetId) {
		this.targetId = targetId;
	}

	public Boolean getIsUpdateSuccess() {
		return isUpdateSuccess;
	}

	public void setIsUpdateSuccess(Boolean isUpdateSuccess) {
		this.isUpdateSuccess = isUpdateSuccess;
	}

	public String getOldSourceRegion() {
		return oldSourceRegion;
	}

	public void setOldSourceRegion(String oldSourceRegion) {
		this.oldSourceRegion = oldSourceRegion;
	}

	public SpRegionEntity getSourceRegion() {
		return sourceRegion;
	}

	public void setSourceRegion(SpRegionEntity sourceRegion) {
		this.sourceRegion = sourceRegion;
	}

	public Boolean getUpdateSuccess() {
		return isUpdateSuccess;
	}

	public void setUpdateSuccess(Boolean updateSuccess) {
		isUpdateSuccess = updateSuccess;
	}

	public Integer getCpu() {
		return cpu;
	}

	public void setCpu(Integer cpu) {
		this.cpu = cpu;
	}

	public Integer getMemoryG() {
		return memoryG;
	}

	public void setMemoryG(Integer memoryG) {
		this.memoryG = memoryG;
	}

	public Integer getDiskG() {
		return diskG;
	}

	public void setDiskG(Integer diskG) {
		this.diskG = diskG;
	}

	public Integer getDiskType() {
		return diskType;
	}

	public void setDiskType(Integer diskType) {
		this.diskType = diskType;
	}

	public Boolean getIsCustom() {
		return isCustom;
	}

	public void setIsCustom(Boolean isCustom) {
		this.isCustom = isCustom;
	}


	public UpProductSystemEnums.QuotaDetailChannel getChannel() {
		return channel;
	}

	public void setChannel(UpProductSystemEnums.QuotaDetailChannel channel) {
		this.channel = channel;
	}
}
