package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_rest.framework.bean.RestTokenBean;
import io.aicloudware.portal.api_rest.service.IRestMessageService;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpOperationLogService;
import io.aicloudware.portal.api_up.service.IUpUserService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.BeanFactory;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.redis.RedisService;
import io.aicloudware.portal.framework.sdk.bean.SpVmResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpOperationType;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.EncryptUtil;
import io.aicloudware.portal.framework.web.SSOService;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Controller
@RequestMapping("/user")
@Api(value = "/user", description = "用户", position = 510)
public class UpUserController extends BaseUpController<UpUser, UpUserBean, UpUserResultBean> {

    @Autowired
    private IUpUserService upUserService;

    @Autowired
    private IUpOperationLogService upOperationLogService;
    
    @Autowired
    private SSOService ssoService;
    
    @Autowired
    private RedisService redisManager;
    
    @Autowired
    private IRestMessageService messageService;

    @Override
    protected UpUserBean[] doQuery(SearchBean<UpUserBean> search, UpUser entity) {
        UpUserBean[] userList = super.doQuery(search, entity);
        EncryptUtil.convertPassword(Arrays.asList(userList), false);
        return userList;
    }

    @Override
    protected UpUserBean doLoad(Integer id) {
        UpUserBean userBean = super.doLoad(id);
        EncryptUtil.convertPassword(Arrays.asList(userBean), false);
        return userBean;
    }

    @RequestMapping(value = "/getUserType", method = RequestMethod.GET)
    @ApiOperation(notes = "/getUserType", httpMethod = "GET", value = "获取用户类型")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取用户类型", response = UpUserBean.class)})
    @ResponseBody
    public ResponseBean getUserType() {
    	return ResponseBean.success(upUserService.getUserType());
    }
    
    @RequestMapping(value = "/loginConfig", method = RequestMethod.GET)
    @ApiOperation(notes = "/loginConfig", httpMethod = "GET", value = "登录认证配置")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回认证SSO配置", response = Map.class)})
    @ResponseBody
    public ResponseBean loginConfig() {
    	Map<String, String> map = new HashMap<>();
//    	map.put("appid", ssoService.getAppid());
    	map.put("uri", ssoService.getUri());
        return ResponseBean.success(map);
    }
    
//    @RequestMapping(value = "/login", method = RequestMethod.GET)
//    @ApiOperation(notes = "/login", httpMethod = "GET", value = "登录认证")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回认证Token", response = String.class)})
//    @ResponseBody
//    public ResponseBean login(@ApiParam(value = "认证详细信息") String openid) {
//        ThreadCache.operationLogLocal.get().setOperationType(UpOperationType.user_login);
//        UpUserBean bean = ssoService.loadUserInfoByOpenid(openid);
//        Map<String,String> map = upUserService.login(bean);
//        return ResponseBean.success(map);
//    }
    
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean login(@RequestBody UpUserBean bean) {
        ThreadCache.operationLogLocal.get().setOperationType(UpOperationType.user_login);
        bean.setPassword(new String(Base64.getDecoder().decode(bean.getPassword())));
        EncryptUtil.convertPassword(Arrays.asList(bean), true);
//        UpUserBean bean = ssoService.loadUserInfoByOpenid(name);
        Map<String,Object> map = upUserService.login(bean, false);
//        Map<String,String> map = new HashMap<>();
//        map.put("token", token);
        map.put("username", bean.getName());
        return ResponseBean.success(map);
    }
    
    @RequestMapping(value = "/changeRegion", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean changeRegion(@RequestBody UpUserBean bean) {
        Map<String,Object> map = upUserService.buildTokenMap(ThreadCache.getUserId(), bean.getRegion());
        return ResponseBean.success(map);
    }
    
    @RequestMapping(value = "/ssoLogin", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean ssoLogin(@RequestBody RestTokenBean params) {
        return ResponseBean.success(upUserService.ssoLogin(params));
    }
    
    @RequestMapping(value = "/logout", method = RequestMethod.GET)
    @ApiOperation(notes = "/logout", httpMethod = "GET", value = "用户登出")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回登出结果", response = Boolean.class)})
    @ResponseBody
    public ResponseBean logout(HttpServletRequest request) {
//    	String token = request.getHeader("Authorization");
    	upUserService.recordLogout();
//    	redisManager.logoutTokenCache(token);
        if (null != ThreadCache.operationLogLocal.get()) {
            UpUser user = BeanFactory.getCloudDao().load(UpUser.class, ThreadCache.getUserId());
            upOperationLogService.saveOperationLog(UpOperationType.user_logout.getTitle(), user, UpOperationType.user_logout, UpUser.class, user.getId(), user.getName(),  null);
        }
        return ResponseBean.success(Boolean.TRUE);
    }

//    @RequestMapping(value = "/query", method = RequestMethod.GET)
//    @ApiOperation(notes = "/query", httpMethod = "GET", value = "用户列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "用户列表", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean query(HttpServletRequest request) {
//        return ResponseBean.success(upUserService.query());
//    }
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVmResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody UpUserSearchBean searchBean) {
        if(ThreadCache.getOrgId()==null) {
            return ResponseBean.success(new ArrayList<>());
        }
        UpUser entity = BeanCopyUtil.copy(searchBean.getBean(), UpUser.class);
//        entity.setRegion(ThreadCache.getRegion());
        SpOrg org = commonService.load(SpOrg.class, ThreadCache.getOrgId());
//        SpOrg org = commonService.load(SpOrg.class,399);

        entity.setOrg(org);

        if (StringUtils.isNotEmpty(entity.getName())) {
            UpUserBean fuzzyBean = new UpUserBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        try {
            UpUserBean[] entityList = upUserService.query(searchBean, entity);
            UpUserResultBean result = new UpUserResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/check", method = RequestMethod.GET)
    @ApiOperation(notes = "/check", httpMethod = "GET", value = "账户初始化完成结果")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "账户初始化完成结果", response = Boolean.class)})
    @ResponseBody
    public ResponseBean check() {
//    	try {
//    		ThreadCache.getOrgId();
//    	}catch(Exception e) {
//    		return ResponseBean.success(Boolean.FALSE);
//    	}
        return ResponseBean.success(Boolean.TRUE);
    }

    @RequestMapping(value = "/addOrUpdate", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean addOrUpdate(@RequestBody UpUserBean bean) {
        upUserService.addAndUpdate(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    public ResponseBean addOrUpdate(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        upUserService.delete(id);
        return ResponseBean.success(true);
    }

}
