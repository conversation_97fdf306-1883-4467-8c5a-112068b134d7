package io.aicloudware.portal.api_crm.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "VPC")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, RestV2RdsBean.class})
public class RestV2RdsBean extends UpOrderCloudServerBean {
	private Integer diskG;

	public Integer getDiskG() {
		return diskG;
	}

	public void setDiskG(Integer diskG) {
		this.diskG = diskG;
	}
}
