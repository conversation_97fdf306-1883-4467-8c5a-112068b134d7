package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.entity.IUpEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.SpVmDiskBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class CmVmDisk<V extends CmVm> extends BaseSpEntity<SpVmDiskBean> implements IUpEntity<SpVmDiskBean> {

    @JoinColumn(name = "vm_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private V vm;

    /* 磁盘序号 */
    @Column(name = "disk_number")
    private Integer diskNumber;

    /* 磁盘大小 */
    @Column(name = "disk_gb", nullable = false)
    private Integer diskGB;

    /* 磁盘标签 */
    @Column(name = "disk_label")
    @EntityProperty(isCopyOnUpdate = false)
    private String diskLabel;

    /* 驱动器盘符/挂载路径 */
    @Column(name = "disk_path")
    private String diskPath;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    @EntityProperty(isCopyOnUpdate = false)
    private SpVmDiskType type;

    @Column(name = "server_id")
    private String serverId;

    @Column(name = "resource_region")
    private String resourceRegion;

    /* 置备模式 */
    @Column(name = "thin_provisioned")
    private Boolean thinProvisioned;
    
    @EntityProperty(isCopyOnUpdate = false)
    @JoinColumn(name = "order_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;
    
    public V getVm() {
        return vm;
    }

    public void setVm(V vm) {
        this.vm = vm;
    }

    public Integer getDiskNumber() {
        return diskNumber;
    }

    public void setDiskNumber(Integer diskNumber) {
        this.diskNumber = diskNumber;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public String getDiskLabel() {
        return diskLabel;
    }

    public void setDiskLabel(String diskLabel) {
        this.diskLabel = diskLabel;
    }

    public String getDiskPath() {
        return diskPath;
    }

    public void setDiskPath(String diskPath) {
        this.diskPath = diskPath;
    }

    public SpVmDiskType getType() {
        return type;
    }

    public void setType(SpVmDiskType type) {
        this.type = type;
    }

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

    public Boolean getThinProvisioned() {
        return thinProvisioned;
    }

    public void setThinProvisioned(Boolean thinProvisioned) {
        this.thinProvisioned = thinProvisioned;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }
}
