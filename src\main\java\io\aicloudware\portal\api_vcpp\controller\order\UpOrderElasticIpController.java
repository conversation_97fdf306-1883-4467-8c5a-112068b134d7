package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBandwidthSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderElasticIpService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpLoadBalancerVirtualServerBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderElasticIpBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ChargePeriod;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ElasticIpChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ResourceType;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * 弹性公网IP
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/elasticIp")
public class UpOrderElasticIpController extends BaseController {

	@Autowired
	private IUpOrderElasticIpService elasticIpService;
	
	@Autowired
	private IUpProductService productService;
	
	@Autowired
	private IUpOrderService orderService;

	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 弹性公网IP-付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 弹性公网IP-计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (ElasticIpChargeType type : ElasticIpChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
        // 弹性公网IP-计费周期
        Map<String, String> chargePeriod = new LinkedHashMap<String, String>();
        for (ChargePeriod type : ChargePeriod.values()) {
        	chargePeriod.put(type.toString(), type.getTitle());
        }
        datas.put("chargePeriod", chargePeriod);
        
        // 弹性公网IP-资源类型
        Map<String, Map<String,Object>> resourceType = new HashMap<>();
        for (ResourceType type : ResourceType.values()) {
        	// TODO 暂时屏蔽绑定负载均衡
        	if(type.equals(ResourceType.loadBalance)) {
        		continue;
        	}
        	
        	Map<String,Object> item = new HashMap<>();
        	item.put("name", type.getTitle());
        	if(ResourceType.cloudServer.equals(type)) {
        		List<SpVmBean> cloudServers = orderService.queryUnbindSpVm(id);
    			item.put("dataList", cloudServers);
    		}else if(ResourceType.loadBalance.equals(type)) {
    			List<SpLoadBalancerVirtualServerBean> loadBalances = orderService.queryUnbindLoadbalance(id);
    			item.put("dataList", loadBalances);
    		}
        	resourceType.put(type.toString(), item);
        }
        datas.put("resourceType", resourceType);
        
        // 配置
        datas.put("bandwidthConfig", productService.getBandwidthSetByOrg(id));
//        datas.put("loadBalanceConfig", productService.getLoadBalanceSetByOrg(id));
        
		return ResponseBean.success(datas);
	}
	
    @RequestMapping(value = "/availableips", method = RequestMethod.GET)
    @ApiOperation(notes = "/availableips", httpMethod = "GET", value = "获取IP清单")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取IP清单", response = List.class)})
    @ResponseBody
    public ResponseBean availableips() {
    	Integer orgId = ThreadCache.getOrgId();
    	SpOrg spOrg = new SpOrg();
    	spOrg.setId(orgId);
    	List<String> ips = elasticIpService.availableIps(spOrg);
        return ResponseBean.success(ips);
    }
	
	@RequestMapping(value = "/quota/init/{code}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable String code) {
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 弹性公网IP-付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 弹性公网IP-计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (ElasticIpChargeType type : ElasticIpChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
        // 弹性公网IP-计费周期
        Map<String, String> chargePeriod = new LinkedHashMap<String, String>();
        for (ChargePeriod type : ChargePeriod.values()) {
        	chargePeriod.put(type.toString(), type.getTitle());
        }
        datas.put("chargePeriod", chargePeriod);
        
        // 弹性公网IP-资源类型
        Map<String, Map<String,Object>> resourceType = new HashMap<>();
        datas.put("resourceType", resourceType);
        
        // 配置
		UpOrderQuotaDetail quotaDetail = commonService.load(UpOrderQuotaDetail.class, Integer.valueOf(code));
		UpProductBandwidthSetBean bandwidthSetBean = null;
		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
			bandwidthSetBean = new UpProductBandwidthSetBean();
			bandwidthSetBean.setEnabled(true);
			bandwidthSetBean.setUnit(Integer.valueOf(quotaDetail.getValue()));
			bandwidthSetBean.setMinValue(1);
			bandwidthSetBean.setMaxValue(1000);
			bandwidthSetBean.setNumber(new Integer[] {1,2,3,4,5,7,8,9,10,15,20,25,30,40,50,60,70,80,90,100,150,200,300,400,500,600,700,800,900,1000});
		}else{
			bandwidthSetBean = productService.getBandwidthSet(quotaDetail.getProductCode());
		}
		datas.put("bandwidthConfig", bandwidthSetBean);
		datas.put("loadBalanceConfig", productService.getLoadBalanceSetByOrg(ThreadCache.getOrgId()));
		return ResponseBean.success(datas);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.ELASTIC_IP, description = "用户新增订单")
	public ResponseBean save(@RequestBody UpOrderElasticIpBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(elasticIpService.save(bean, applyUser));
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.ELASTIC_IP, description = "用户新增订单")
	public ResponseBean quotaSave(@RequestBody UpOrderElasticIpBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(elasticIpService.save(bean, ThreadCache.getUser()));
	}
	
}
