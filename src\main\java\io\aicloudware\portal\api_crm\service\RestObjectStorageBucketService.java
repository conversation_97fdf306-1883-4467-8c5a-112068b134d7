package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_rest.service.IRestApiService;
import io.aicloudware.portal.api_vcpp.entity.UpOrderObjectStorageBucket;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpOrgCephUserBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpS3BucketBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpS3ObjectBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_S3.CephS3Common;
import io.aicloudware.portal.platform_vcd.entity.SpObjectStorageBucket;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpOrgCephUser;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.twonote.rgwadmin4j.RgwAdmin;
import org.twonote.rgwadmin4j.model.BucketInfo;
import org.twonote.rgwadmin4j.model.BucketInfo.Usage.RgwMain;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.internal.http.loader.DefaultSdkHttpClientBuilder;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.utils.AttributeMap;
import software.amazon.awssdk.utils.IoUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

@SuppressWarnings("rawtypes")
@Service
@Transactional
public class RestObjectStorageBucketService extends BaseService implements IRestObjectStorageBucketService {

	protected final static Logger logger = Logger.getLogger(RestObjectStorageBucketService.class);
	
	@Value("#{${s3_operation}}")
	private Map<String, Map<String, String>> s3Configs;
	
	@Autowired
	private IRestApiService restApiService;

//	@Override
//	public Integer quotaAdd(UpOrderQuotaDetailBean bean) {
//		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
//		SpRegion sourceRegion = ThreadCache.getRegion();
//
//		SpRegion region = restApiService.transformGY_GY3(user.getOrg(), sourceRegion);
//
//		final String name = "CRM-CUSTOM-"+System.currentTimeMillis();
//		UpOrderQuotaDetail quotaDetail = new UpOrderQuotaDetail(region, QuotaCatalog.NEW, name, null, user.getOrg(), name, ProductType.OBJECT_STORAGE);
//		quotaDetail.setSourceRegion(sourceRegion);
//		quotaDetail.setDiskG(bean.getDiskG());
//		quotaDetail.setIsCustom(true);
//
//		UpOrderQuota quota = new UpOrderQuota(region, QuotaCatalog.NEW, name, name, user, user.getOrg(), ProductType.OBJECT_STORAGE, user.getOrg().getCustomNo());
//		quota.setSourceRegion(sourceRegion);
//		quota.setCatalog(QuotaCatalog.NEW);
//		quota.setQuotaDetailList(Collections.singletonList(quotaDetail));
//		dao.insert(quota);
//		initS3OrgCephUser();
//		return quota.getId();
//	}

//	@Override
//	public void quotaDelete(Integer id) {
//		UpOrderQuota quota = dao.load(UpOrderQuota.class, id);
//		AssertUtil.check(quota != null && !quota.getQuotaDetailList().isEmpty() && quota.getSpOrg().getId().equals(ThreadCache.getOrgId()), "异常操作!");
//		UpOrderQuotaDetail detail = quota.getQuotaDetailList().get(0);
//		AssertUtil.check(detail.getIsCustom(), "异常操作!");
//		detail.setQuotaDetailStatus(QuotaDetailStatus.finish);
//		quota.setQuotaStatus(UpProductSystemEnums.QuotaStatus.finish);
//		dao.update(detail);
//		dao.update(quota);
//		initS3OrgCephUser();
//	}

//	@Override
//	public List<UpOrderQuotaDetailBean> quotalist() {
//		List<UpOrderQuotaDetail> quotaDetailList = dao.list(UpOrderQuotaDetail.class, MapUtil.of("type", ProductType.OBJECT_STORAGE, "quotaDetailStatus", QuotaDetailStatus.start, "spOrg", new SpOrg(ThreadCache.getOrgId())));
//		return quotaDetailList.stream().map(entity -> {
//			UpOrderQuotaDetailBean bean = BeanCopyUtil.copy(entity, UpOrderQuotaDetailBean.class);
//			bean.setCatalog(null);
//			bean.setSubCode(null);
//			bean.setType(null);
//			bean.setSpOrgId(null);
//			bean.setCustom(bean.getCustom() == null ? false : bean.getCustom());
//
//			return bean;
//		}).collect(Collectors.toList());
//	}

	@Override
	public SpOrgCephUserBean initS3OrgCephUser() {
    	Long maxSizeKB = 0l;

		List<UpOrderQuotaDetail> customQuotaDetailList = dao.list(UpOrderQuotaDetail.class,
				MapUtil.of("type", ProductType.OBJECT_STORAGE, "isCustom", true, "spOrg", new SpOrg(ThreadCache.getOrgId()),
						"region", ThreadCache.getRegion(), "quotaDetailStatus", QuotaDetailStatus.start));
		logger.info("ThreadCache.getOrgId():"+ThreadCache.getOrgId() + " ThreadCache.getRegion():"+ThreadCache.getRegion().getName());
		if(!customQuotaDetailList.isEmpty()){
			maxSizeKB = customQuotaDetailList.stream().map(UpOrderQuotaDetail::getDiskG).mapToLong(Long::valueOf).sum() * 1024l * 1024l;
		}
		logger.info("maxSizeKB1:"+maxSizeKB);
		String sql = "select sum(a.disk_g) from ( " +
				" select sum(s.unit) as disk_g from up_product_disk_set s left join up_order_quota_detail d on d.product_code = s.product_code and d.product_code <> 'oss_standard' "
				+ " where s.status = 'active' and d.status = 'active' and d.sp_org_id =:spOrgId and d.catalog =:catalog "
				+ " and d.type =:type and d.region =:region and d.quota_detail_status =:quotaDetailStatus " +
				" union " +
				" select d.disk_g from up_order_quota_detail d where d.product_code='oss_standard' and d.status = 'active' and d.sp_org_id =:spOrgId and d.catalog =:catalog " +
				" and d.type =:type and d.region =:region and d.quota_detail_status =:quotaDetailStatus " +
				" ) as a";

		List<BigDecimal> obj = queryDao.querySql(sql, MapUtil.of("spOrgId", ThreadCache.getOrgId(), "catalog", QuotaCatalog.NEW.name(),
				"type", ProductType.OBJECT_STORAGE.name(), "region", ThreadCache.getRegion(), "quotaDetailStatus", QuotaDetailStatus.start.name()));

		if(!obj.isEmpty() && obj.get(0) != null) {
			maxSizeKB = obj.get(0).longValue() * 1024 * 1024l;
		}

		SpOrgCephUserBean s3CephConfigBean = getS3Operation();
		CephS3Common common;
		RgwAdmin rgwAdmin = null;
		SpOrgCephUser ceph = null;
		logger.info("maxSizeKB2:"+maxSizeKB);
		SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
		List<SpOrgCephUser> cephs = dao.list(SpOrgCephUser.class, MapUtil.of("spOrg", new SpOrg(ThreadCache.getOrgId()), "region", ThreadCache.getRegion()));
		if(!cephs.isEmpty()) {
			ceph = cephs.get(0);
			logger.info("ceph.getOs_private_access_url():"+ceph.getOs_private_access_url()+" s3CephConfigBean.getAccessKey():"+s3CephConfigBean.getAccessKey());
			common = CephS3Common.getInstance(ceph.getOs_private_access_url(), s3CephConfigBean.getAccessKey(), s3CephConfigBean.getSecretKey());
			rgwAdmin = common.createRgwAdmin();
			rgwAdmin.setUserQuota(ceph.getSpOrg().getName(), -1l, maxSizeKB);
			if(ceph.getTotalSizeKb().compareTo(maxSizeKB)!=0 ) {
				AssertUtil.check(maxSizeKB != 0l, "请先开通对象存储服务");
				ceph.setTotalSizeKb(maxSizeKB);
				this.dao.update(ceph, "totalSizeKb");
			}
		}else {
			AssertUtil.check(maxSizeKB != 0l, "请先开通对象存储服务");
			common = CephS3Common.getInstance(s3CephConfigBean.getOs_private_access_url(), s3CephConfigBean.getAccessKey(), s3CephConfigBean.getSecretKey());
			try {
				JSONObject item = common.createCephAdminClient().put(org.getName()).getJSONArray("keys").getJSONObject(0);
				rgwAdmin = common.createRgwAdmin();
				rgwAdmin.setUserQuota(org.getName(), -1l, maxSizeKB);
				
				ceph = new SpOrgCephUser();
				ceph.setAccessKey(item.getString("access_key"));
				ceph.setSecretKey(item.getString("secret_key"));
				ceph.setOs_private_access_url(s3CephConfigBean.getOs_private_access_url());
				ceph.setSpOrg(new SpOrg(ThreadCache.getOrgId()));
				ceph.setRegion(ThreadCache.getRegion());
				ceph.setName(org.getName() + "-" + ThreadCache.getRegion().name());
				ceph.setTotalSizeKb(maxSizeKB);
				dao.insert(ceph);
				
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
				AssertUtil.check(false, "对象存储账户初始化失败，请联系管理员");
			}
		}
		Long totalUsagedSizeBytes = 0l;
		List<BucketInfo> bucketInfoList = rgwAdmin.listBucketInfo(org.getName());
		if(bucketInfoList != null) {
			for(BucketInfo info : bucketInfoList){
				if(info != null && info.getUsage() != null && info.getUsage().getRgwMain() != null) {
					totalUsagedSizeBytes += info.getUsage().getRgwMain().getSize();
				}
			}
		}

		SpOrgCephUserBean bean = new SpOrgCephUserBean();
		bean.setAccessKey(ceph.getAccessKey());
		bean.setSecretKey(ceph.getSecretKey());
		bean.setTotalSizeKb(ceph.getTotalSizeKb());
		bean.setOs_private_access_url(ceph.getOs_private_access_url());
		bean.setOs_public_access_url(s3CephConfigBean.getOs_public_access_url());
		bean.setTotalUsagedSizeBytes(totalUsagedSizeBytes);
		return bean;
	}

    @Override
	public List<UpS3BucketBean> listBuckets() {
		S3Client s3 = buildS3Client();
		try {
			ListBucketsResponse listBucketsResponse = s3.listBuckets(ListBucketsRequest.builder().build());
			return ListUtil.toList(listBucketsResponse.buckets(), (l, bucket) -> {
				l.add(new UpS3BucketBean(bucket.name()));
			});
		}catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException("获取Bucket列表失败！");
		}finally {
			if(s3 != null) {
				s3.close();
			}
		}
	}


	@Override
	public List<UpS3ObjectBean> bucketDetails(String bucket) {
//		SpObjectStorageBucket entity = dao.load(SpObjectStorageBucket.class, id);
		AssertUtil.check(bucket, "桶信息异常");
		List<UpS3ObjectBean> beans = new ArrayList<>();
		S3Client s3 = buildS3Client();
		List<String> folderList = new ArrayList<>();
		try {
    		ZoneOffset zoneOffset = OffsetDateTime.now().getOffset();
    		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            for (S3Object object : s3.listObjects(ListObjectsRequest.builder().bucket(bucket).build()).contents()) {
                int i = object.key().lastIndexOf("/");
                UpS3ObjectBean bean = new UpS3ObjectBean();
                bean.setId(object.key());
                bean.setLastModified(object.lastModified().atOffset(zoneOffset).format(formatter));
                if(i < 0) {
                	bean.setType("file");
                	bean.setParentId("");
                	bean.setName(object.key());
                	bean.setSize(object.size()/1024);
                }else if(i == (object.key().length() - 1)) {
                	bean.setType("folder");
                	String[] names = object.key().split("/");
                	bean.setName(names[names.length -1]);
                	bean.setParentId(object.key().substring(0, object.key().lastIndexOf(bean.getName() + "/")));
                	folderList.add(bean.getId());
                }else {
                	bean.setType("file");
                	String[] names = object.key().split("/");
                	bean.setName(names[names.length-1]);
                	bean.setParentId(object.key().substring(0, object.key().lastIndexOf(bean.getName())));
                	bean.setSize(object.size()/1024);
                }
                repairFolder(folderList,beans,bean.getParentId(),bean.getLastModified());
                beans.add(bean);
            }
		}catch (Exception e) {
			logger.error(e.getMessage(), e);
			AssertUtil.check(false, "获取桶详情失败！");
		}finally {
			if(s3 != null) {
				s3.close();
			}
		}
		return beans;
	}
	
	@Override
	public UpS3BucketBean bucketInfo(String bucket) {
		AssertUtil.check(bucket, "桶信息异常");
		UpS3BucketBean bean = new UpS3BucketBean(bucket);
		bean.setName(bucket);
		SpOrgCephUser cephEntity = dao.list(SpOrgCephUser.class, MapUtil.of("spOrg", new SpOrg(ThreadCache.getOrgId()), "region", ThreadCache.getRegion())).get(0);
		SpOrgCephUserBean s3CephConfigBean = getS3Operation();
		Optional<BucketInfo> infoOpt = CephS3Common.getInstance(cephEntity.getOs_private_access_url(), s3CephConfigBean.getAccessKey(), s3CephConfigBean.getSecretKey()).createRgwAdmin().getBucketInfo(bucket);
		BucketInfo info = infoOpt.get();
		AssertUtil.check(dao.load(SpOrg.class, ThreadCache.getOrgId()).getName().equals(info.getOwner()), "获取桶信息异常！");
		RgwMain main = info.getUsage().getRgwMain();
		if(main!=null) {
			bean.setSizekbUtilized(info.getUsage().getRgwMain().getSize_kb_utilized());
		}else {
			bean.setSizekbUtilized(0l);
		}
		bean.setCreateTime(info.getMtime().split("\\.")[0]);
		return bean;
	}

	@Override
	public String addBucket(String name) {
		AssertUtil.check(org.apache.commons.lang.StringUtils.isNotEmpty(name), "请输入桶名称");
		name += "-"+System.currentTimeMillis();
		S3Client s3 = buildS3Client();
		try {
			s3.createBucket(CreateBucketRequest.builder().bucket(name).build());
			s3.waiter().waitUntilBucketExists(HeadBucketRequest.builder().bucket(name).build());
			
//			SpObjectStorageBucket entity = new SpObjectStorageBucket();
//			entity.setName(name);
//			entity.setOwner(ThreadCache.getUser());
//			entity.setRegion(ThreadCache.getRegion());
//			entity.setSpOrg(new SpOrg(ThreadCache.getOrgId()));
//			dao.insert(entity);
			logger.info("SpOrg: " + ThreadCache.getOrgId() + " Create bucket " + name + " success.");
		} catch (Exception e) {
			logger.error("SpOrg: " + ThreadCache.getOrgId() + " Create bucket " + name + " failed. " + e.getMessage(), e);
			AssertUtil.check(false, "创建桶失败！");
		} finally {
			if(s3 != null) {
				s3.close();
			}
		}
		return name;
	}

	@Deprecated
	@Override
	public String deleteBucket(UpOrderObjectStorageBucket entity) {
		S3Client s3 = buildS3Client();
		try {
			DeleteBucketRequest deleteBucketRequest = DeleteBucketRequest.builder().bucket(entity.getName()).build();
			s3.deleteBucket(deleteBucketRequest);
			dao.delete(SpObjectStorageBucket.class, entity.getSpObjectStorageBucket().getId());
			logger.info("SpOrg: " + entity.getSpOrg().getName() + " Delete bucket " + entity.getName() + " success.");
			return ApiConstants.REQUEST_ID_SUCCEED;
		} catch (Exception e) {
			logger.error("SpOrg: " + entity.getSpOrg().getName() + " Delete bucket " + entity.getName() + " failed. " + e.getMessage(), e);
			throw new RuntimeException(e.getMessage(), e);
		} finally {
			if(s3 != null) {
				s3.close();
			}
		}
	}
	
	@Override
	public void deleteBucket(String bucket) {
		AssertUtil.check(bucket, "桶信息异常");
		S3Client s3 = buildS3Client();
		try {
			DeleteBucketRequest deleteBucketRequest = DeleteBucketRequest.builder().bucket(bucket).build();
			s3.deleteBucket(deleteBucketRequest);
			logger.info("Delete bucket " + bucket + " success.");
//			dao.delete(SpObjectStorageBucket.class, id);
		} catch (Exception e) {
			logger.error("Delete bucket " + bucket + " failed. " + e.getMessage(), e);
			throw new RuntimeException(e.getMessage(), e);
		} finally {
			if(s3 != null) {
				s3.close();
			}
		}
	}

	@Override
	public void uploadBucketFile(UpS3ObjectBean bean, MultipartFile file) {
		AssertUtil.check(file.getSize() <= 104857600l, "上传文件大小必须小于100MB");
		
		AssertUtil.check(bean.getId() != null , "对象ID异常！");
		AssertUtil.check(bean.getBucketId() != null , "桶ID异常！");
		S3Client s3 = buildS3Client();
		InputStream inputStream = null;
		byte[] bytesArray = null;
		try {
//			byte[] bytesArray = new byte[(int) file.getSize()];
			inputStream = file.getInputStream();
			
			bytesArray = IoUtils.toByteArray(inputStream);
//			inputStream.read(bytesArray);;
//			PutObjectRequest putOb = PutObjectRequest.builder().bucket(bucket.getName()).key(bean.getId() + file.getOriginalFilename()).acl(ObjectCannedACL.PUBLIC_READ).build();
//			s3.putObject(putOb, RequestBody.fromBytes(bytesArray));
			UploadPartRequest upload = UploadPartRequest.builder().bucket(bean.getBucketId()).key(bean.getId() + file.getOriginalFilename()).build();
			s3.uploadPart(upload, RequestBody.fromBytes(bytesArray));
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException("文件上传失败！");
		}finally {
			bytesArray = null;
			if(s3 != null) {
				s3.close();
			}
			if (inputStream != null) {
                try {
                	inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
		}
	}
	
	@Override
	public void createBucketFolder(UpS3ObjectBean bean) {
		AssertUtil.check(bean.getId() != null , "对象ID异常！");
		AssertUtil.check(bean.getBucketId() != null , "桶ID异常！");
		AssertUtil.check(org.apache.commons.lang.StringUtils.isNotEmpty(bean.getName()) , "请输入文件夹名称");
		AssertUtil.check(bean.getName().indexOf("/") , "输入的文件夹格式有误，请重新输入");
		S3Client s3 = buildS3Client();
		try {
			PutObjectRequest putOb1 = PutObjectRequest.builder().bucket(bean.getBucketId()).key(bean.getId() + bean.getName() + "/").acl(ObjectCannedACL.PUBLIC_READ).build();
            s3.putObject(putOb1, RequestBody.empty());
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException("创建文件夹失败！");
		}finally {
			if(s3 != null) {
				s3.close();
			}
		}
    }
	
	@Override
	public void deleteObject(UpS3ObjectBean bean) {
		AssertUtil.check(bean.getId() != null , "对象ID异常！");
		AssertUtil.check(bean.getBucketId() != null , "桶ID异常！");
		S3Client s3 = buildS3Client();
		try {
			ListObjectsRequest listObjectsRequest = ListObjectsRequest.builder().bucket(bean.getBucketId()).prefix(bean.getId()).build();
			ListObjectsResponse objectsResponse = s3.listObjects(listObjectsRequest);
			List<ObjectIdentifier> objects = new ArrayList<>();
			while (true) {
				objectsResponse.contents().forEach(s3Object -> objects.add(ObjectIdentifier.builder().key(s3Object.key()).build()));
			    if (objectsResponse.isTruncated()) {
			        objectsResponse = s3.listObjects(listObjectsRequest);
			        continue;
			    }
			    break;
			};
			s3.deleteObjects(DeleteObjectsRequest.builder().bucket(bean.getBucketId()).delete(Delete.builder().objects(objects).build()).build());
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException("删除对象失败！");
		}finally {
			if(s3 != null) {
				s3.close();
			}
		}
    }

	@Override
	public void downloadObject(UpS3ObjectBean bean, HttpServletRequest request, HttpServletResponse response) {
		AssertUtil.check(bean.getId() != null , "对象ID异常！");
		AssertUtil.check(bean.getBucketId() != null , "桶ID异常！");
		ServletOutputStream out = null;
		ResponseInputStream<GetObjectResponse> ris = null;
		S3Client s3 = buildS3Client();
		try {
			GetObjectRequest getObject = GetObjectRequest.builder().bucket(bean.getBucketId()).key(bean.getId()).build();
			ris = s3.getObject(getObject);
			
//			ris = new FileInputStream(new File(realPath + "WEB-INF/classes/config/test.txt"));
			String mimeType = request.getSession().getServletContext().getMimeType(bean.getId());
			if(StringUtils.isEmpty(mimeType)) {
				mimeType = "application/octet-stream";
			}
			
			response.reset();
			
			String[] names = bean.getId().split("/");
			
			response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(names[names.length -1], "utf-8"));
//			response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("test.txt", "utf-8"));
			response.setContentType(mimeType + "; charset=utf-8");
			out = response.getOutputStream();
			
			//读取文件流
	        int len = 0;
	        byte[] buffer = new byte[102410];
	        while ((len = ris.read(buffer)) != -1) {
	            out.write(buffer, 0, len);
	        }
	        out.flush();
	        response.flushBuffer();
		}catch(Exception e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException("文件下载失败！");
		}finally {
			if(s3 != null) {
				s3.close();
			}
			try {
				if(out != null) out.close();
				if(ris != null) ris.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			
		}
		
	}


	private S3Client buildS3Client() {
		SpOrgCephUserBean ceph = this.initS3OrgCephUser();
		try {
			final AttributeMap attributeMap = AttributeMap.builder()
					.put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, true)
					.build();
			final SdkHttpClient sdkHttpClient = new DefaultSdkHttpClientBuilder().buildWithDefaults(attributeMap);
			return S3Client.builder().httpClient(sdkHttpClient).region(Region.US_EAST_1).endpointOverride(new URI(CephS3Common.scheme + "://" + ceph.getOs_private_access_url())).credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(ceph.getAccessKey(), ceph.getSecretKey()))).build();
		} catch (URISyntaxException e) {
			logger.error(e.getMessage(), e);
			AssertUtil.check(false, "对象存储服务异常");
			return null;
		}
    }

	private SpOrgCephUserBean getS3Operation() {
		AssertUtil.check(s3Configs.containsKey(ThreadCache.getRegion().name()), ThreadCache.getRegion().getTitle() + "不支持对象存储实施，请选择其他地区");
		Map<String,String> datas = s3Configs.get(ThreadCache.getRegion().name());

		String[] privateAccessUrlDatas = datas.get("private_access_url").split("-");
//		logger.info("=========="+privateAccessUrlDatas[0] + " | " + privateAccessUrlDatas[1] );
		Integer ipStartNumber = Integer.valueOf(privateAccessUrlDatas[0].split("\\.")[3].split(":")[0]);
		Integer ipZoneNumber = Integer.valueOf(privateAccessUrlDatas[1].split("\\.")[3].split(":")[0]) - ipStartNumber + 1;

//		String privateAccessUrl = privateAccessUrlDatas[0].substring(0, privateAccessUrlDatas[0].lastIndexOf(".")) + (ipStartNumber + new Random().nextInt(ipZoneNumber));
		String privateAccessUrl = privateAccessUrlDatas[0].substring(0, privateAccessUrlDatas[0].lastIndexOf(".") + 1) + (ipStartNumber + new Random().nextInt(ipZoneNumber)) + ":" + privateAccessUrlDatas[0].split(":")[1];

		SpOrgCephUserBean cephUserBean = new SpOrgCephUserBean();
		cephUserBean.setOs_private_access_url(privateAccessUrl);
		cephUserBean.setOs_public_access_url(datas.get("public_access_url"));
		cephUserBean.setAccessKey(datas.get("access_key"));
		cephUserBean.setSecretKey(datas.get("secret_key"));
		return cephUserBean;
	}

	private void repairFolder(List<String> folderList, List<UpS3ObjectBean> objects, String folderId, String time) {
		if(folderId.equals("")) {
			return;
		}
		if(folderList.indexOf(folderId)<0) {
			UpS3ObjectBean bean = new UpS3ObjectBean();
			bean.setType("folder");
        	String[] names = folderId.split("/");
        	bean.setName(names[names.length -1]);
        	String parentFolderId = folderId.substring(0, folderId.lastIndexOf(bean.getName() + "/"));
        	repairFolder(folderList, objects, parentFolderId, time);
        	bean.setParentId(parentFolderId);
            bean.setId(folderId);
            bean.setLastModified(time);
            objects.add(bean);
            folderList.add(bean.getId());
		}
	}
}
