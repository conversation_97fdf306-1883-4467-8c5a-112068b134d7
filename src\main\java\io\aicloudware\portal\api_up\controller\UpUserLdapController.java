package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpUserLdap;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpUserLdapBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserLdapResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/ldap_user")
@Api(value = "/ldap_user", description = "LDAP用户", position = 511)
public class UpUserLdapController extends BaseUpController<UpUserLdap, UpUserLdapBean, UpUserLdapResultBean> {

//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpUserLdapResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryUser(@ApiParam(value = "查询条件") @RequestBody UpUserLdapSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpUserLdapBean.class)})
//    @ResponseBody
//    public ResponseBean getUser(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpUserBean.class)})
//    @ResponseBody
//    public ResponseBean updateUser(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                   @ApiParam(value = "实例对象") @Valid @RequestBody UpUserLdapBean bean,
//                                   BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }

}
