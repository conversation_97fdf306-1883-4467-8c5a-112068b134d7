package io.aicloudware.portal.api_rest.framework.web;

import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

@Configuration
public class RestApiWebConfig extends WebMvcConfigurationSupport {
	@Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(resolverBean());
    }

    @Bean
    public RestApiDecodeMethodResolver resolverBean() {
        return new RestApiDecodeMethodResolver();
    }
}
