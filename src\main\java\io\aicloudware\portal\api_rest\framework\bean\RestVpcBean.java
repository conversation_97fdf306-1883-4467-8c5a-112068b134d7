package io.aicloudware.portal.api_rest.framework.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "VPC信息")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, RestVpcBean.class })
public class RestVpcBean extends BaseRestBean {

	@ApiModelProperty(value = "用户ID")
	private String vpcId;
	
	@ApiModelProperty(value = "用户ID")
	private RestSubnetBean[] subnets;

	@ApiModelProperty(value = "vlanid")
	private String vlanId;
	
	@ApiModelProperty(value = "资源类型")
	private String resourcePoolId;

	public String getVpcId() {
		return vpcId;
	}

	public void setVpcId(String vpcId) {
		this.vpcId = vpcId;
	}

	public RestSubnetBean[] getSubnets() {
		return subnets;
	}

	public void setSubnets(RestSubnetBean[] subnets) {
		this.subnets = subnets;
	}

	public String getVlanId() {
		return vlanId;
	}

	public void setVlanId(String vlanId) {
		this.vlanId = vlanId;
	}

	public String getResourcePoolId() {
		return resourcePoolId;
	}

	public void setResourcePoolId(String resourcePoolId) {
		this.resourcePoolId = resourcePoolId;
	}
}
