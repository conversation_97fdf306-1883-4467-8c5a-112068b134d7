package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpScriptBean;
import io.aicloudware.portal.framework.sdk.contants.UpScriptCategory;
import io.aicloudware.portal.framework.sdk.contants.UpScriptType;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name = "up_script")
@Access(AccessType.FIELD)
public class UpScript extends BaseUpEntity<UpScriptBean> {

    @Column(name = "category", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpScriptCategory category;

    @Column(name = "main_script", nullable = false)
    private String mainScript;

    @Column(name = "config_file")
    private String configFile;

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpScriptType type;

    @Column(name = "platform")
    private String platform;

    @Column(name = "upload_config", nullable = false)
    private Boolean uploadConfig;

    @Column(name = "params")
    private String params;

    @Column(name = "username")
    private String username;

    @Column(name = "password", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String password;

    @Column(name = "timeout")
    private Integer timeout;

    @Column(name = "output_path")
    private String outputPath;

    @Column(name = "result_file")
    private String resultFile;

    public UpScript() {
    }

    public UpScript(Integer id) {
        super(id);
    }

    public UpScriptCategory getCategory() {
        return category;
    }

    public void setCategory(UpScriptCategory category) {
        this.category = category;
    }

    public String getMainScript() {
        return mainScript;
    }

    public void setMainScript(String mainScript) {
        this.mainScript = mainScript;
    }

    public String getConfigFile() {
        return configFile;
    }

    public void setConfigFile(String configFile) {
        this.configFile = configFile;
    }

    public UpScriptType getType() {
        return type;
    }

    public void setType(UpScriptType type) {
        this.type = type;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Boolean getUploadConfig() {
        return uploadConfig;
    }

    public void setUploadConfig(Boolean uploadConfig) {
        this.uploadConfig = uploadConfig;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getOutputPath() {
        return outputPath;
    }

    public void setOutputPath(String outputPath) {
        this.outputPath = outputPath;
    }

    public String getResultFile() {
        return resultFile;
    }

    public void setResultFile(String resultFile) {
        this.resultFile = resultFile;
    }
}
