package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpRoleBean;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "up_role")
@Access(AccessType.FIELD)
public final class UpRole extends BaseUpEntity<UpRoleBean> {

    @Column(name = "description", length = ApiConstants.STRING_MAX_LENGTH)
    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
