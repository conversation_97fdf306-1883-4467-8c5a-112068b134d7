package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.entity.IResourceEntity;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.sdk.bean.UpTaskBean;
import io.aicloudware.portal.framework.sdk.contants.UpApplicationType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SpSimpleOperateBean;
import io.aicloudware.portal.framework.bean.UpSimpleOperateBean;
import io.aicloudware.portal.framework.executor.IExecutorA;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public interface IUpApplicationService {

    public <E extends IResourceEntity<B>, R extends IRequestEntity<B>, B extends RecordBean> UpApplicationBean createAddApplication(UpApplicationType type, List<R> entityList, UpApplicationBean bean, Class<E> entityClazz);

    public <E extends IResourceEntity<B>, R extends IRequestEntity<B>, B extends RecordBean> UpApplicationBean createUpdateApplication(UpApplicationType type, List<R> entityList, UpApplicationBean bean, Class<E> entityClazz);

    public <E extends IResourceEntity<B>, R extends IRequestEntity<B>, B extends RecordBean> UpApplicationBean createSimpleOperateApplication(Class<E> entityClazz, Class<R> requestClazz, SpSimpleOperateBean bean, IExecutorA<R> callback);

    public UpApplicationBean createAppSystemApplication(SpSimpleOperateBean bean);

    public UpTaskBean createSimpleOperateTask(UpTaskType type, String name, Integer targetId);

    public void handleApplicationProcess(UpOrder order);

    public void checkMutexApplication(List<Integer> deploymentIdList, List<Integer> vmIdList);

    public void resubmit(UpSimpleOperateBean bean);

    public void deploy(UpSimpleOperateBean bean);

    public void redeploy(UpSimpleOperateBean bean);

    public void deployReject(UpSimpleOperateBean bean);

    public void audit(UpSimpleOperateBean bean);

    public void close(UpSimpleOperateBean bean);

}
