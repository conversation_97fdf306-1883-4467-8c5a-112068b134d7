package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistoryBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalHistorySearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpApprovalStatus;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.utility.Utility;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Arrays;
import java.util.Set;

@Entity
@Table(name = "up_approval_history")
@Access(AccessType.FIELD)
public class UpApprovalHistory extends BaseUpEntity<UpApprovalHistoryBean> implements IEnvironmentEntity<UpApprovalHistoryBean> {

    @Column(name = "process_id", nullable = false)
    private Integer processId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "application_id", nullable = false)
    private UpApplication application;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "node_id", nullable = false)
    private UpApprovalProcessNode node;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pre_approver_id")
    private UpUser preApprover;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pending_approver_id", nullable = false)
    private UpUser pendingApprover;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "actual_approver_id")
    private UpUser actualApprover;

    @Column(name = "approval_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpApprovalStatus approvalStatus;

    @Column(name = "opinion", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String opinion;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpApprovalHistoryBean> searchBean, Set<String> aliasSet) {
        UpApprovalHistorySearchBean bean = (UpApprovalHistorySearchBean) searchBean;
        if (Utility.isNotZero(ThreadCache.getUserId()) && !ThreadCache.isSystemAdminLogin()) {
            Criterion criterion = Restrictions.eq("pendingApprover.id", ThreadCache.getUserId());
            criteria.add(criterion);
        }
        if (Utility.isNotEmpty(bean.getProcessIdList())) {
            DaoUtil.addInValues(criteria, "processId", Arrays.asList(bean.getProcessIdList()));
        }
        if (Utility.isNotEmpty(bean.getApprovalStatusList())) {
            DaoUtil.addInValues(criteria, "approvalStatus", Arrays.asList(bean.getApprovalStatusList()));
        }
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }

    public UpApplication getApplication() {
        return application;
    }

    public void setApplication(UpApplication application) {
        this.application = application;
    }

    public UpApprovalProcessNode getNode() {
        return node;
    }

    public void setNode(UpApprovalProcessNode node) {
        this.node = node;
    }

    public UpUser getPreApprover() {
        return preApprover;
    }

    public void setPreApprover(UpUser preApprover) {
        this.preApprover = preApprover;
    }

    public UpUser getPendingApprover() {
        return pendingApprover;
    }

    public void setPendingApprover(UpUser pendingApprover) {
        this.pendingApprover = pendingApprover;
    }

    public UpUser getActualApprover() {
        return actualApprover;
    }

    public void setActualApprover(UpUser actualApprover) {
        this.actualApprover = actualApprover;
    }

    public UpApprovalStatus getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(UpApprovalStatus approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public Integer getProcessId() {
        return processId;
    }

    public void setProcessId(Integer processId) {
        this.processId = processId;
    }

}
