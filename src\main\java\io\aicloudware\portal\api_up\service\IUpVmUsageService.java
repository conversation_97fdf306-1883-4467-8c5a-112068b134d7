package io.aicloudware.portal.api_up.service;


import io.aicloudware.portal.framework.sdk.bean.UpVmUsageResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmUsageSearchBean;
import org.springframework.transaction.annotation.Transactional;

@Transactional
public interface IUpVmUsageService {

    public UpVmUsageResultBean getStaticsDate(UpVmUsageSearchBean searchBean);

    public UpVmUsageResultBean getStaticsGroup(UpVmUsageSearchBean searchBean);

    public UpVmUsageResultBean getStaticsAppSystem(UpVmUsageSearchBean searchBean);

    public UpVmUsageResultBean getStaticsVm(UpVmUsageSearchBean searchBean);

}
