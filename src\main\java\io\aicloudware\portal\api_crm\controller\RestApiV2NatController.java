package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpElasticIp;
import io.aicloudware.portal.platform_vcd.entity.SpIpBinding;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.service.ISpElasticIpService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Controller
@RequestMapping("/api/v2/nat")
@Api(value = "/api/v2/nat", description = "nat", position = 47)
public class RestApiV2NatController extends BaseEntityController<SpElasticIp, SpElasticIpBean, SpElasticIpResultBean> {
	@Autowired
	private ISpElasticIpService spElasticIpService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "NAT网关规则创建")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpIpBindingBean.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.ELASTIC_IP, description = "NAT网关规则创建")
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody SpIpBindingBean bean, BindingResult bindingResult) {
        Integer orgId = ThreadCache.getOrgId();
        SpOrg spOrg = new SpOrg();
        spOrg.setId(orgId);
        if (bean.getPublicPort() != null) {
            bean.setPublic_port(bean.getPublicPort());
        }
        if (bean.getVmPort() != null) {
            bean.setVm_port(bean.getVmPort());
        }
        logger.info("SpIpBindingBean:"+ bean);


        SpIpBinding ipBinding = BeanCopyUtil.copy(bean, SpIpBinding.class);
        if (ipBinding.getVm()!=null && ipBinding.getVm().getId() !=null) {
            commonService.load(SpVm.class, SpVmBean.class, ipBinding.getVm().getId(), ThreadCache.getOrgId());
        }
        if (ipBinding.getElasticIp()!=null && ipBinding.getElasticIp().getId() != null) {
            commonService.load(SpElasticIp.class, SpElasticIpBean.class, ipBinding.getElasticIp().getId(), ThreadCache.getOrgId());
        }
        SpIpBinding outBinding = spElasticIpService.bind(spOrg, ipBinding);
        return ResponseBean.success(BeanCopyUtil.copy2Bean(outBinding, SpIpBindingBean.class));
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "解绑定")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.ELASTIC_IP, description = "云主机解绑公网IP")
    public ResponseBean unbind(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        //check org
        commonService.load(SpIpBinding.class, SpIpBindingBean.class, id, ThreadCache.getOrgId());
        Integer orgId = ThreadCache.getOrgId();
        SpOrg spOrg = new SpOrg();
        spOrg.setId(orgId);

        SpIpBinding outBinding = spElasticIpService.unbind(spOrg, id);
        return ResponseBean.success(true);
    }

	@RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpIpBindingSearchBean searchBean) {
		SpIpBinding entity = BeanCopyUtil.copy(searchBean.getBean(), SpIpBinding.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        searchBean.setOrderBy1(true);
        searchBean.setOrderName1("elasticIp.id");
        searchBean.setOrderBy2(true);
        searchBean.setOrderName2("public_port");
        return ResponseBean.success(commonService.query(searchBean, entity, SpIpBindingBean.class));
    }

}
