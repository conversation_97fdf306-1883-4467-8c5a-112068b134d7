package io.aicloudware.portal.api_vcpp.service.finance;

import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceBalanceBean;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceBillResultBean;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceBillSearchBean;
import io.aicloudware.portal.framework.sdk.bean.finance.UpFinanceRechargeOrderBean;

public interface IUpFinanceRechargeService {

	public UpFinanceRechargeOrderBean order(Integer userId, UpFinanceRechargeOrderBean bean);

	public UpFinanceRechargeOrderBean check(Integer userId, UpFinanceRechargeOrderBean bean);

	public UpFinanceBalanceBean balance(Integer userId);

	public UpFinanceBillResultBean list(Integer userId,UpFinanceBillSearchBean bean);

	public Boolean checkBalance(Integer userId);

}
