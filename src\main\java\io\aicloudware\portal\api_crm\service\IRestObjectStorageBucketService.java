package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_vcpp.entity.UpOrderObjectStorageBucket;
import io.aicloudware.portal.framework.sdk.bean.SpOrgCephUserBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpS3BucketBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpS3ObjectBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Service
@Transactional
public interface IRestObjectStorageBucketService {

	String addBucket(String name);

	@Deprecated
	String deleteBucket(UpOrderObjectStorageBucket entity);
	
	void deleteBucket(String bucket);
	
	List<UpS3ObjectBean> bucketDetails(String bucket);

	void uploadBucketFile(UpS3ObjectBean bean, MultipartFile file);

	void createBucketFolder(UpS3ObjectBean bean);

	void deleteObject(UpS3ObjectBean bean);

	void downloadObject(UpS3ObjectBean bean, HttpServletRequest request, HttpServletResponse response);

	UpS3BucketBean bucketInfo(String bucket);

	SpOrgCephUserBean initS3OrgCephUser();

	List<UpS3BucketBean> listBuckets();

//	List<UpOrderQuotaDetailBean> quotalist();

//	Integer quotaAdd(UpOrderQuotaDetailBean bean);
//
//	void quotaDelete(Integer id);

}