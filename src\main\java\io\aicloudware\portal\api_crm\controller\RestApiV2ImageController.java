package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpVappTemplateBean;
import io.aicloudware.portal.framework.sdk.bean.SpVappTemplateResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpVappTemplateSearchBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderPrivateImageBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;
import io.aicloudware.portal.platform_vcd.service.ISpVappTemplateService;
import io.aicloudware.portal.platform_vcd.service.ISpVmService;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/api/v2/vapptemplate")
@Api(value = "/api/v2/vapptemplate", description = "云安全", position = 140)
public class RestApiV2ImageController extends BaseEntityController {

    @Autowired
    private ISpVmService spVmService;

    @Autowired
    private ISpVappTemplateService spBlueprintService;

    @RequestMapping(value = "/cratePrivateImg", method = RequestMethod.POST)
    @ApiOperation(notes = "/cratePrivateImg", httpMethod = "POST", value = "创建私有镜像")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.IMAGE, description = "创建")
    public ResponseBean cratePrivateImg(@ApiParam(value = "私有镜像") @RequestBody UpOrderPrivateImageBean bean) {
        return ResponseBean.success(spVmService.createPrivateImage(bean.getCloudServerUuId(), bean.getName()));
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVappTemplateResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpVappTemplateSearchBean searchBean) {
        SpVappTemplate entity = BeanCopyUtil.copy(searchBean.getBean(), SpVappTemplate.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        if (StringUtils.isNotEmpty(entity.getName())) {
            SpVappTemplateBean fuzzyBean = new SpVappTemplateBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        try {
            SpVappTemplateBean[] entityList = spBlueprintService.query(ThreadCache.getOrgId());
            SpVappTemplateResultBean result = new SpVappTemplateResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除镜像")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.IMAGE, description = "删除")
    public ResponseBean deleteImage(@ApiParam(value = "对象ID") @PathVariable String id) {
        spBlueprintService.deletePrivateImage(id);
        return ResponseBean.success(true);
    }
}
