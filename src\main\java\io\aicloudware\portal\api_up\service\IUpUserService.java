package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_rest.framework.bean.RestTokenBean;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserSearchBean;
import io.aicloudware.portal.framework.service.ITaskRunnable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Service
@Transactional
public interface IUpUserService extends ITaskRunnable {

	public Map<String,Object> login(UpUserBean bean, Boolean isAdminLogin);
	
	public Map<String,Object> ssoLogin(RestTokenBean token);
	
    public void notifySyncLdapUser();

	public void bindUserOrg();
	
	public Integer bindUserOrg(UpUser user);
	
	public Integer getUserOrg(Integer userId);

	public String getUserType();

	public UpUserBean[] query(UpUserSearchBean searchBean, UpUser entity);

	Map<String, Object> buildTokenMap(Integer userId, SpRegionBean region);

	void recordLogout();

	public boolean checkToken();

	UpUserBean addUser(UpUserBean bean);

    void addAndUpdate(UpUserBean bean);

    void delete(Integer id);
}
