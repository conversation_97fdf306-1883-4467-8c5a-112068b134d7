package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MD5Util;
import io.aicloudware.portal.framework.utility.RSAUtil;
import io.aicloudware.portal.framework.sdk.bean.UpLicenseBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.remote.RemoteUtil;
import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.utility.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Date;

@Service
@Transactional
public class UpLicenseService extends BaseService implements IUpLicenseService {

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    private final String SerialNumber = getSerialNumber();
    private final UpLicenseBean LicenseBean = new UpLicenseBean();

    private String getSerialNumber() {
        return MD5Util.encode(getMacAddress(), "vmware");
    }

    private String getMacAddress() {
        try {
            InetAddress ia = InetAddress.getLocalHost();
            byte[] mac = NetworkInterface.getByInetAddress(ia).getHardwareAddress();
            if (mac == null) {
                return "No-Mac-Address";
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < mac.length; i++) {
                if (i != 0) {
                    sb.append("-");
                }
                String s = Integer.toHexString(mac[i] & 0xFF);
                sb.append(s.length() == 1 ? 0 + s : s);
            }
            return sb.toString().toUpperCase();
        } catch (Exception e) {
            logger.error("Cannot get MAC address.", e);
            return "No-Mac-Address";
        }
    }

    public UpLicenseBean getLicenseBean() {
        if (Utility.isEmpty(LicenseBean.getSerialNumber())) {
            LicenseBean.setSerialNumber(SerialNumber);
        }
        return LicenseBean;
    }

    @Override
    public UpLicenseBean createLicense(UpLicenseBean bean) {
        AssertUtil.check(Utility.isNotEmpty(bean.getLicenseSerialNumber()), "License机器码 必须输入");
        AssertUtil.check(bean.getVersion() != null, "License版本 必须输入");
        AssertUtil.check(Utility.isNotEmpty(bean.getExpireDt()), "到期时间 必须输入");
        AssertUtil.check(FormatUtil.formatDate(new Date()).compareTo(bean.getExpireDt()) < 0, "到期时间 不能小于今天");
        AssertUtil.check(Utility.isNotEmpty(bean.getPrivateKey()), "License密钥 必须输入");
        String privateKey = bean.getPrivateKey();
        bean.setPrivateKey(null);
        bean.setSerialNumber(null);
        bean.setLicenseCode(null);
        String jsonValue = RemoteUtil.gson.toJson(bean);
        bean.setLicenseCode(RSAUtil.encrypt(jsonValue, privateKey));
        AssertUtil.check(jsonValue.equals(RSAUtil.decrypt(bean.getLicenseCode(), RSAUtil.pubKeyLicense)), "License密钥不对");
        if (SerialNumber.equals(bean.getLicenseSerialNumber())) {
            saveLicense(bean.getLicenseCode());
        }
        return bean;
    }

    @Override
    public void saveLicense(String licenseCode) {
        String jsonValue = RSAUtil.decrypt(licenseCode, RSAUtil.pubKeyLicense);
        UpLicenseBean bean = RemoteUtil.gson.fromJson(jsonValue, UpLicenseBean.class);
        AssertUtil.check(SerialNumber.equals(bean.getLicenseSerialNumber()), "License机器码不一致");
        AssertUtil.check(FormatUtil.formatDate(new Date()).compareTo(bean.getExpireDt()) < 0, "License已过期");

        UpSystemConfigListBean listBean = new UpSystemConfigListBean();
        UpSystemConfigBean configBean = new UpSystemConfigBean();
        configBean.setKey(UpSystemConfigKey.license);
        configBean.setName(UpSystemConfigKey.license.name());
        configBean.setValue(licenseCode);
        listBean.setDataList(new UpSystemConfigBean[]{configBean});
        upSystemConfigService.save(listBean);

        BeanCopyUtil.copy(bean, LicenseBean);
    }

    @Override
    public void validateLicense() {
        if (Utility.isEmpty(LicenseBean.getExpireDt())) {
            UpSystemConfigBean systemConfigBean = upSystemConfigService.get(UpSystemConfigKey.license);
            if (Utility.isNotEmpty(systemConfigBean.getValue())) {
                String jsonValue = RSAUtil.decrypt(systemConfigBean.getValue(), RSAUtil.pubKeyLicense);
                UpLicenseBean _licenseBean = RemoteUtil.gson.fromJson(jsonValue, UpLicenseBean.class);
                BeanCopyUtil.copy(_licenseBean, LicenseBean);
                getLicenseBean();
            }
        }
        AssertUtil.check(Utility.isNotEmpty(LicenseBean.getSerialNumber()), "License不存在");
        AssertUtil.check(SerialNumber.equals(LicenseBean.getSerialNumber()), "无效的License");
        AssertUtil.check(Utility.isNotEmpty(LicenseBean.getExpireDt()), "License不存在");
        AssertUtil.check(FormatUtil.formatDate(new Date()).compareTo(LicenseBean.getExpireDt()) <= 0, "License已过期");
    }
}
