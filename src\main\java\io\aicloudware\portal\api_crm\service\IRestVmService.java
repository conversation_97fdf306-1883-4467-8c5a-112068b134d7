package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmTicketBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Transactional
public interface IRestVmService {

    public SpVmBean[] query(SearchBean<SpVmBean> searchBean, SpVm spVm);


    public Integer powerOn(String vmId);

    public Integer powerOff(String vmId);
    
    public Integer powerOff(SpVm vm);
    
    public Integer powerReboot(String vmId);

    public Integer powerReset(String vmId);
    
    public Integer deleteVm(String vmId);
    
    public SpVmTicketBean getMksTicket(String vmId);
    
    public String queryVmBindLoadBalancerPool(SpVm vm);


	public void refresh(Integer vmId);

    public Integer refresh(String vmId);

    public List<Map<String,Object>> getTemplatesByCatalog(SpRegionEntity region, Integer orgId, UpOrderSystemEnums.CatalogType... types);
}
