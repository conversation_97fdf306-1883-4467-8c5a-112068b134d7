package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.UpQuotaLog;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaLogBean;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaLogSearchBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public interface IUpQuotaLogService{

    UpQuotaLogBean[] query(UpQuotaLogSearchBean searchBean, UpQuotaLog entity);

    void save(UpQuotaLogBean bean);
}
