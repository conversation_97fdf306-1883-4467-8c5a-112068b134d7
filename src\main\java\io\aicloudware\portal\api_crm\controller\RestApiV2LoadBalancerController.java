package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.service.IRestLoadBalancerService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderLoadBalanceBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.*;
import io.aicloudware.portal.platform_vcd.service.ISpVmService;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

@Controller
@RequestMapping("/api/v2/slb")
@Api(value = "/api/v2/slb", description = "loadbalancer", position = 47)
public class RestApiV2LoadBalancerController extends BaseUpController<SpLoadBalancer, SpLoadBalancerBean, SpLoadBalancerResultBean> {
	@Autowired
	private IRestLoadBalancerService restLoadBalancerService;

    @Autowired
    private ISpVmService spVmService;

    private Logger log = LoggerFactory.getLogger(RestApiV2LoadBalancerController.class);
	
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "新建LB")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = ResponseBean.class)})
    @ResponseBody
	@AuditLogUpOrder(type = UpProductSystemEnums.ProductType.LOAD_BALANCER, description = "新增")
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody UpOrderLoadBalanceBean bean, BindingResult bindingResult) {
        return ResponseBean.success(restLoadBalancerService.save(bean));
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpLoadBalancerResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpLoadBalancerSearchBean searchBean) {
        if (searchBean.getBean() == null) {
            searchBean.setBean(new SpLoadBalancerBean());
        }
        searchBean.getBean().setSpOrgId(ThreadCache.getOrgId());
        SpLoadBalancer entity = BeanCopyUtil.copy(searchBean.getBean(), getEntityType());
        String searchStr = entity.getName();
        entity.setName(null);
        entity.setRegion(ThreadCache.getRegion());
        SpLoadBalancerBean[] entityArray = doQuery(searchBean, entity);
        List<SpLoadBalancerBean> entityList = new ArrayList<>();
        for (SpLoadBalancerBean bean : entityArray) {
            if (bean.getSpOrgId().equals(ThreadCache.getOrgId())) {
                entityList.add(bean);
            }
        }

        log.info("entityList:" + entityList.size());

        SpVmSearchBean vmSearchBean = new SpVmSearchBean();
        vmSearchBean.setPageSize(Integer.MAX_VALUE);
        vmSearchBean.setSearchType(SpVmType.search_all);
        // vmSearchBean.getBean().setSpTenantId(ThreadCache.getOrgId());
        SpVm vm = BeanCopyUtil.copy(vmSearchBean.getBean(), SpVm.class);
        vm.setSpOrg(new SpOrg());
        vm.getSpOrg().setId(ThreadCache.getOrgId());
        // if(StringUtils.isNotEmpty(entity.getName())) {
        // SpLoadBalancerBean fuzzyBean = new SpLoadBalancerBean();
        // fuzzyBean.setName(entity.getName());
        // entity.setName(null);
        // searchBean.setFuzzyBean(fuzzyBean);
        // }
        // entity.getSpOrg().setId(3);
        SpVmBean[] vmEntityList = spVmService.query(vmSearchBean, vm);
        Map<String, String> vmIpToNameMap = new HashMap<>();
        for (SpVmBean vmBean : vmEntityList) {
            vmIpToNameMap.put(vmBean.getIpAddress(), vmBean.getName());
        }

        if (StringUtils.isNotEmpty(searchStr)) {
            for (SpLoadBalancerBean lb : entityList) {

                List<SpLoadBalancerVirtualServerBean> vsBean = new ArrayList<SpLoadBalancerVirtualServerBean>();
                List<SpLoadBalancerPoolBean> poolBean = new ArrayList<SpLoadBalancerPoolBean>();
                HashSet<String> poolNameSet = new HashSet<>();
                if (lb.getLoadBalancerVirutalServerList() != null && lb.getLoadBalancerPoolList() != null) {
                    for (int i = 0; i < lb.getLoadBalancerVirutalServerList().length; i++) {
                        SpLoadBalancerVirtualServerBean lbvsBean = lb.getLoadBalancerVirutalServerList()[i];
                        if (lbvsBean.getIpAddress() != null && lbvsBean.getIpAddress().indexOf(searchStr) > -1) {
                            vsBean.add(lbvsBean);
                        } else if (lbvsBean.getName() != null && lbvsBean.getName().indexOf(searchStr) > -1) {
                            vsBean.add(lbvsBean);
                        }
                        poolNameSet.add(lbvsBean.getDefaultPool());
                    }
                    lb.setLoadBalancerVirutalServerList(vsBean.toArray(new SpLoadBalancerVirtualServerBean[0]));

                    for (int i = 0; i < lb.getLoadBalancerPoolList().length; i++) {
                        SpLoadBalancerPoolBean lbpoolBean = lb.getLoadBalancerPoolList()[i];
                        if (poolNameSet.contains(lbpoolBean.getPoolId())) {
                            poolBean.add(lbpoolBean);
                        }
                    }
                    lb.setLoadBalancerPoolList(poolBean.toArray(new SpLoadBalancerPoolBean[0]));
                }
            }
        }

        for (SpLoadBalancerBean lb : entityList) {
            if (lb.getLoadBalancerPoolList() != null) {
                for (SpLoadBalancerPoolBean pb : lb.getLoadBalancerPoolList()) {
                    if (pb.getLoadBalancerMemberList() != null) {
                        for (SpLoadBalancerMemberBean lbMember : pb.getLoadBalancerMemberList()) {
                            String name = vmIpToNameMap.get(lbMember.getIpAddress());
                            if (name == null) {
                                lbMember.setVmName("");
                            } else {
                                lbMember.setVmName(name);
                            }
                            lbMember.setHealthStatus("UP");

                        }
                    }
                }
            }
        }

        ResultListBean<SpLoadBalancerBean> result = Utility.newInstance(getResultType());
        fillPageInfo(searchBean, result);
        result.setDataList(entityList.toArray(new SpLoadBalancerBean[0]));
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.LOAD_BALANCER, description = "删除")
    public ResponseBean delete(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        // check org
        commonService.load(SpLoadBalancer.class, SpLoadBalancerBean.class, id, ThreadCache.getOrgId());
        return deleteEntity(id);
    }

    @RequestMapping(value = "/availableip", method = RequestMethod.GET)
    @ApiOperation(notes = "/availableip", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpMonitorBean.class)})
    @ResponseBody
    public ResponseBean getAvailableIp() {
        SpLoadBalancerSearchBean searchBean = new SpLoadBalancerSearchBean();
        searchBean.setBean(new SpLoadBalancerBean());
        searchBean.getBean().setSpOrgId(ThreadCache.getOrgId());
        SpLoadBalancer entity = BeanCopyUtil.copy(searchBean.getBean(), getEntityType());
        entity.setName(null);
        entity.setRegion(ThreadCache.getRegion());
        SpLoadBalancerBean[] entityArray = doQuery(searchBean, entity);
        List<String> availableIps = new ArrayList<>();
        for (int i=10;i<=251;i++) {
            availableIps.add("192.168.255."+i);
        }
        if (entityArray != null) {
            for (SpLoadBalancerBean lbBean : entityArray) {
                if (lbBean.getLoadBalancerVirutalServerList() != null) {
                    for (SpLoadBalancerVirtualServerBean vs: lbBean.getLoadBalancerVirutalServerList()) {
                        availableIps.remove(vs.getIpAddress());
                    }
                }
            }
        }
        return ResponseBean.success(availableIps.toArray(new String[0]));
    }

    @RequestMapping(value = "/update_pool/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/update_pool/{id}", httpMethod = "POST", value = "修改池实例")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpLoadBalancerPoolBean.class)})
    @ResponseBody
    public ResponseBean updatePool(@ApiParam(value = "对象ID") @PathVariable Integer id,
                                   @ApiParam(value = "实例对象") @Valid @RequestBody SpLoadBalancerPoolBean bean,
                                   BindingResult bindingResult) {

        SpLoadBalancerPool loadBalancerPool = BeanCopyUtil.copy(bean, SpLoadBalancerPool.class);
        // check org
        SpLoadBalancerPool lbPool = commonService.load(SpLoadBalancerPool.class, loadBalancerPool.getId(), ThreadCache.getOrgId());
        SpOrg spOrg = new SpOrg();
        spOrg.setId(ThreadCache.getOrgId());
        loadBalancerPool.setSpOrg(spOrg);
        restLoadBalancerService.updateLBPool(loadBalancerPool);
        return ResponseBean.success("");
    }

    @RequestMapping(value = "/update_vs/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/update_vs/{id}", httpMethod = "POST", value = "修改VS实例")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpLoadBalancerBean.class)})
    @ResponseBody
    public ResponseBean updateVirtualServer(@ApiParam(value = "对象ID") @PathVariable Integer id,
                                            @ApiParam(value = "实例对象") @Valid @RequestBody SpLoadBalancerBean bean,
                                            BindingResult bindingResult) {
        SpLoadBalancer loadBalancer = BeanCopyUtil.copy(bean, SpLoadBalancer.class);
        SpOrg spOrg = new SpOrg();
        spOrg.setId(ThreadCache.getOrgId());
        loadBalancer.setSpOrg(spOrg);

        //check org
        AssertUtil.check(loadBalancer.getLoadBalancerPoolList() != null && loadBalancer.getLoadBalancerPoolList().size()>0, "负载均衡POOL不存在");
        SpLoadBalancerPool lbPool = commonService.load(SpLoadBalancerPool.class, loadBalancer.getLoadBalancerPoolList().get(0).getId(), ThreadCache.getOrgId());

        AssertUtil.check(loadBalancer.getLoadBalancerVirutalServerList() != null && loadBalancer.getLoadBalancerVirutalServerList().size()>0, "负载均衡虚拟服务器不存在");
        SpLoadBalancerVirtualServer lbvs = commonService.load(SpLoadBalancerVirtualServer.class, loadBalancer.getLoadBalancerVirutalServerList().get(0).getId(), ThreadCache.getOrgId());

        restLoadBalancerService.updateLBPoolAndVs(loadBalancer);
        return ResponseBean.success("");
    }

    @RequestMapping(value = "/{id}/metrics/{peroid}", method = RequestMethod.GET)
    @ApiOperation(notes = "/{id}/metrics/{peroid}", httpMethod = "GET", value = "查询监控数据")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回类型", response = ArrayList.class)})
    @ResponseBody
    public ArrayList<SpMetricsBean> metrics(@ApiParam(value = "对象ID") @PathVariable Integer id, @ApiParam(value = "时间") @PathVariable Integer peroid) {
        commonService.load(SpLoadBalancerVirtualServer.class, SpLoadBalancerVirtualServerBean.class, id,
                ThreadCache.getOrgId());
        ArrayList<SpMetricsBean> list = restLoadBalancerService.queryLBHistoryMetrics(id, new Float(peroid) / 100);
        return list;
    }
}
