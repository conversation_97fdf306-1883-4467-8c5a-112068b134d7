package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderK8sClusterNodePoolBean;
import io.aicloudware.portal.platform_vcd.entity.SpK8sCluster;
import io.aicloudware.portal.platform_vcd.entity.SpK8sClusterNodePool;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

import javax.persistence.*;

@Entity
@Table(name = "up_order_k8s_cluster_node_pool")
@Access(AccessType.FIELD)
public class UpOrderK8sClusterNodePool extends UpOrderProduct<UpOrderK8sClusterNodePoolBean> implements IOrderEntity {

    @Column(name = "flavor")
    private String flavor;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "sp_org_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    @JoinColumn(name = "cluster_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpK8sCluster cluster;

    @Column(name = "resource_region")
    private String resourceRegion;

    @Column(name = "password")
    private String password;

    @Column(name = "root_volume_size")
    private Integer rootVolumeSize;

    @Column(name = "data_volume_size")
    private String dataVolumeSize;

    @JoinColumn(name = "node_pool_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpK8sClusterNodePool nodePool;

    public String getFlavor() {
        return flavor;
    }

    public void setFlavor(String flavor) {
        this.flavor = flavor;
    }

    public SpK8sCluster getCluster() {
        return cluster;
    }

    public void setCluster(SpK8sCluster cluster) {
        this.cluster = cluster;
    }

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getRootVolumeSize() {
        return rootVolumeSize;
    }

    public void setRootVolumeSize(Integer rootVolumeSize) {
        this.rootVolumeSize = rootVolumeSize;
    }

    public String getDataVolumeSize() {
        return dataVolumeSize;
    }

    public void setDataVolumeSize(String dataVolumeSize) {
        this.dataVolumeSize = dataVolumeSize;
    }

    public SpK8sClusterNodePool getNodePool() {
        return nodePool;
    }

    public void setNodePool(SpK8sClusterNodePool nodePool) {
        this.nodePool = nodePool;
    }

    @Override
    public UpOrder getOrder() {
        return order;
    }

    @Override
    public void setOrder(UpOrder order) {
        this.order = order;
    }

    @Override
    public SpOrg getSpOrg() {
        return spOrg;
    }

    @Override
    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }
}
