package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpApplication;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationBean;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/application")
@Api(value = "/application", description = "申请单", position = 600)
public class UpApplicationController extends BaseUpController<UpApplication, UpApplicationBean, UpApplicationResultBean> {

//    @Autowired
//    private IUpApplicationService upApplicationService;
//
//    @Override
//    protected UpApplicationBean[] doUpdate(List<UpApplicationBean> beanList) {
//        return commonService.update(getEntityType(), getBeanType(), beanList, false);
//    }
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApplicationResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryApplication(@ApiParam(value = "查询条件") @RequestBody UpApplicationSearchBean searchBean) {
//        UpApplicationBean applicationBean = searchBean.getBean();
//        if (applicationBean == null) {
//            applicationBean = new UpApplicationBean();
//            searchBean.setBean(applicationBean);
//        }
//        applicationBean.setUpTenantId(Utility.ZERO);
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApplicationBean.class)})
//    @ResponseBody
//    public ResponseBean getApplication(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApplicationBean.class)})
//    @ResponseBody
//    public ResponseBean updateApplication(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                          @ApiParam(value = "实例对象") @Valid @RequestBody UpApplicationBean bean,
//                                          BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApplicationListBean.class)})
//    @ResponseBody
//    public ResponseBean updateApplication(@ApiParam(value = "实例对象") @Valid @RequestBody UpApplicationListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApplication(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApplication(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }
//
//    // =================================================================================================================
//
//    @RequestMapping(value = "/resubmit", method = RequestMethod.POST)
//    @ApiOperation(notes = "/resubmit", httpMethod = "POST", value = "批量部署申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean resubmit(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        ThreadCache.operationLogLocal.get().setOperationType(UpOperationType.application_create);
//        upApplicationService.resubmit(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/deploy", method = RequestMethod.POST)
//    @ApiOperation(notes = "/deploy", httpMethod = "POST", value = "批量部署申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deploy(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        upApplicationService.deploy(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/redeploy", method = RequestMethod.POST)
//    @ApiOperation(notes = "/redeploy", httpMethod = "POST", value = "批量部署申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean redeploy(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        upApplicationService.redeploy(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/deploy_reject", method = RequestMethod.POST)
//    @ApiOperation(notes = "/deploy_reject", httpMethod = "POST", value = "批量部署申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deployReject(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        upApplicationService.deployReject(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/audit", method = RequestMethod.POST)
//    @ApiOperation(notes = "/audit", httpMethod = "POST", value = "稽核申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean audit(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        upApplicationService.audit(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/close", method = RequestMethod.POST)
//    @ApiOperation(notes = "/close", httpMethod = "POST", value = "批量部署申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean close(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        upApplicationService.close(bean);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/export", method = RequestMethod.POST)
//    @ApiOperation(notes = "/export", httpMethod = "POST", value = "查询结果导出")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回文件路径", response = String.class)})
//    @ResponseBody
//    public ResponseBean applicationExport(@ApiParam(value = "查询条件", required = true) @RequestBody UpApplicationSearchBean searchBean, HttpServletRequest request, HttpServletResponse response) {
//
//        searchBean.setPageNum(SdkConstants.QUERY_PAGE_FIRST_NUM);
//        searchBean.setPageSize(SdkConstants.QUERY_PAGE_MAX_SIZE);
//
//        ResponseBean responseBean = queryApplication(searchBean);
//        UpApplicationResultBean rslt = responseBean.getData(UpApplicationResultBean.class);
//        UpApplicationBean[] dataList = rslt.getDataList();
//
//        LinkedHashMap<String, String> headMap = getTitle();
//        List<Map<String, Object>> exportData = getContent(dataList);
//
//        IExecutorAAA<String, Object, Map<String, CellStyleBean>> executor = null;
//        String fileName = export(headMap, exportData, searchBean.getExportFileType(), "申请单列表", request, executor);
//
//        return ResponseBean.success(fileName);
//    }
//
//    /**
//     * @return
//     */
//    /**
//     * @return
//     */
//    private LinkedHashMap<String, String> getTitle() {
//        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
//        headMap.put("1", "序号");
//        headMap.put("2", "申请单号");
//        headMap.put("3", "申请单名称");
//        headMap.put("4", "部门");
//        headMap.put("5", "应用系统");
//        headMap.put("6", "所有者");
//        headMap.put("7", "类型");
//        headMap.put("8", "申请时间");
//        headMap.put("9", "申请单状态");
//
//        return headMap;
//    }
//
//    private List<Map<String, Object>> getContent(UpApplicationBean[] dataList) {
//        List<Map<String, Object>> exportData = new ArrayList<Map<String, Object>>();
//        for (int i = 0; i < dataList.length; i++) {
//            UpApplicationBean bean = dataList[i];
//            Map<String, Object> row = new LinkedHashMap<String, Object>();
//            row.put("1", i + 1);
//            row.put("2", FormatUtil.formatSeqNumber(bean.getId()));
//            row.put("3", bean.getName());
//            row.put("4", bean.getGroupName());
//            row.put("5", bean.getAppSystemName());
//            row.put("6", bean.getOwnerDisplayName());
//            row.put("7", bean.getType().getTitle());
//            row.put("8", FormatUtil.formatDateTime(bean.getCreateTm()));
//            row.put("9", bean.getApplicationStatus().getTitle());
//            exportData.add(row);
//        }
//
//        return exportData;
//    }
}
