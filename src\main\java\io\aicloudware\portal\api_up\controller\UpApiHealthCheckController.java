package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.framework.controller.BaseController;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;


/**
 * 服务状态健康检查
 */
@Controller
@RequestMapping(value = "")
public class UpApiHealthCheckController extends BaseController {

    /**
     * 服务状态健康检查
     */
    @RequestMapping(value = "/apihealth", method = RequestMethod.GET)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void health() {
    }
}
