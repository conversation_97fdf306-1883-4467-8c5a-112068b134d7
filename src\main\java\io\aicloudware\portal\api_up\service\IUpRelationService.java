package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.sdk.bean.UpRelationBean;
import io.aicloudware.portal.framework.sdk.bean.UpRelationListBean;
import io.aicloudware.portal.framework.sdk.bean.UpWarrantListBean;
import io.aicloudware.portal.framework.sdk.contants.UpRelationType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public interface IUpRelationService {

    public void createRelation(UpRelationListBean bean);

    public void deleteRelation(List<Integer> relationIdList);

    public void directlyWarrant(UpWarrantListBean bean);

    public List<UpRelationBean> getUserRelationList(Integer userId, UpRelationType... typeList);

    public List<UpRelationBean> getGroupRelationList(Integer groupId, UpRelationType... typeList);

    public List<UpRelationBean> getRoleRelationList(Integer roleId, UpRelationType... typeList);

    public void clearCache();

}
