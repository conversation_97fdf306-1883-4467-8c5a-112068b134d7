package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpInstanceIndex;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.sdk.contants.SpServiceCatalog;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实例索引同步服务
 * 专门负责处理实例索引的同步逻辑，确保事务正确执行
 */
@Service
public class UpInstanceIndexSyncService extends BaseService {

    /**
     * 同步指定组织和区域的实例索引数据
     * 该方法会在新事务中执行，确保数据一致性
     * 
     * @param orgId 组织ID
     * @param region 区域实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncInstanceIndex(Integer orgId, SpRegionEntity region) {
        Integer regionId = region.getId();
        String sql =
                "select id as instance_id, name as name, 'ecs' as service_type, 'compute' as service_catalog, sp_org_id, owner_id, region_id, create_tm, update_tm from sp_vm  where vm_type = 'iaas' and deploy_status = 'COMPLETE' and status =:status and sp_org_id=:orgId and region_id=:regionId \n" +
                "UNION ALL\n" +
                "select id as instance_id, disk_label as name, 'evs' as service_type, 'compute' as service_catalog, sp_org_id, null as owner_id, region_id, create_tm, update_tm from sp_vm_disk where status =:status and sp_org_id=:orgId and region_id=:regionId \n" +
                "UNION ALL\n" +
                "select id as instance_id, name as name, 'vpc' as service_type, 'network' as service_catalog, sp_org_id, owner_id, region_id, create_tm, update_tm from sp_vpc  where status =:status and sp_org_id=:orgId and region_id=:regionId\n" +
                "UNION ALL\n" +
                "select id as instance_id, name as name, 'elasticip' as service_type, 'network' as service_catalog, sp_org_id, owner_id, region_id, create_tm, update_tm from sp_elastic_ip where status =:status and sp_org_id=:orgId and region_id=:regionId \n" +
                "UNION ALL\n" +
                "select id as instance_id, name as name, 'securityGroup' as service_type, 'network' as service_catalog, sp_org_id, null as owner_id, region_id, create_tm, update_tm from sp_security_group where status =:status and sp_org_id=:orgId and region_id=:regionId \n" +
                "UNION ALL\n" +
                "select id as instance_id, name as name, 'k8s' as service_type, 'k8s' as service_catalog, sp_org_id, null as owner_id, region_id, create_tm, update_tm from sp_k8s_cluster where status =:status and sp_org_id=:orgId and region_id=:regionId";
        
        List<Object[]> list = queryDao.querySql(sql, MapUtil.of("status", RecordStatus.active.name(), "orgId", orgId, "regionId", regionId));
        List<UpInstanceIndex> dataList = list.stream().map(row -> {
            UpInstanceIndex entity = new UpInstanceIndex();
            entity.setInstanceId((Integer) row[0]);
            entity.setName(row[1] != null ? row[1].toString() : "");
            entity.setServiceType(row[2] != null ? SpService.valueOf(row[2].toString()) : null);
            entity.setServiceCatalog(row[3] != null ? SpServiceCatalog.valueOf(row[3].toString()) : null);
            entity.setSpOrg(new SpOrg((Integer) row[4]));
            entity.setOwner(row[5] != null ? new UpUser((Integer) row[5]) : null);
            entity.setRegion(new SpRegionEntity((Integer) row[6]));
            entity.setInstanceCreateTm(row[7] != null ? (Date) row[7] : null);
            entity.setInstanceUpdateTm(row[8] != null ? (Date) row[8] : null);
            return entity;
        }).collect(Collectors.toList());

        List<UpInstanceIndex> dbList = dao.list(UpInstanceIndex.class, MapUtil.of("spOrg", new SpOrg(orgId), "region", region));

        if(dataList.isEmpty() && dbList.isEmpty()){
            return;
        }

        // 定义比较器：基于 instanceId + serviceType + serviceCatalog
        Comparator<UpInstanceIndex> comparator = (o1, o2) -> {
            int result = Integer.compare(o1.getInstanceId(), o2.getInstanceId());
            if (result != 0) return result;

            result = o1.getServiceType().compareTo(o2.getServiceType());
            if (result != 0) return result;

            return o1.getServiceCatalog().compareTo(o2.getServiceCatalog());
        };

        // 1. dataList 中存在，dbList不存在的数据insert操作
        List<UpInstanceIndex> dataToAdd = ListUtil.getDifference(dataList, dbList, comparator);

        // 2. dbList中存在，dataList 不存在的数据做删除
        List<UpInstanceIndex> dataToDelete = ListUtil.getDifference(dbList, dataList, comparator);

        // 3. dataList 与 dbList 都存在的数据，比较instanceUpdateTm是否一致，不一致则更新
        List<UpInstanceIndex> dataToUpdate = ListUtil.getIntersection(dbList, dataList, comparator,
                (dbEntity, dataEntity) -> {
                    // 比较 instanceUpdateTm 是否一致，不一致则更新
                    if (!Utility.equals(dbEntity.getInstanceUpdateTm(), dataEntity.getInstanceUpdateTm())) {
                        dbEntity.setInstanceUpdateTm(dataEntity.getInstanceUpdateTm());
                        dbEntity.setInstanceCreateTm(dataEntity.getInstanceCreateTm());
                        dbEntity.setName(dataEntity.getName());
                        dbEntity.setOwner(dataEntity.getOwner());
                    }
                });

        dao.insert(dataToAdd);
        dao.destroy(UpInstanceIndex.class, dataToDelete.stream().map(UpInstanceIndex::getId).collect(Collectors.toList()));
        dao.update(dataToUpdate, "instanceUpdateTm");
    }
}
