package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderVdiBean;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

import javax.persistence.*;

@Entity
@Table(name = "up_order_vdi")
@Access(AccessType.FIELD)
public class UpOrderVdi extends UpOrderProduct<UpOrderVdiBean> implements IOrderEntity {

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

	@Column(name = "service_plan_id")
	private Integer servicePlanId;

	@Column(name = "cpu")
	private Integer cpu;

	@Column(name = "memory")
	private Integer memory;

	@Column(name = "disk")
	private Integer disk;

	@Column(name = "image_id")
	private Integer imageId;

	@Column(name = "vpc_id")
	private Integer vpcId;

	@Column(name = "network_id")
	private Integer networkId;

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	@Override
	public UpOrder getOrder() {
		return order;
	}

	@Override
	public void setOrder(UpOrder order) {
		this.order = order;
	}

	@Override
	public SpOrg getSpOrg() {
		return spOrg;
	}

	@Override
	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public Integer getServicePlanId() {
		return servicePlanId;
	}

	public void setServicePlanId(Integer servicePlanId) {
		this.servicePlanId = servicePlanId;
	}

	public Integer getCpu() {
		return cpu;
	}

	public void setCpu(Integer cpu) {
		this.cpu = cpu;
	}

	public Integer getMemory() {
		return memory;
	}

	public void setMemory(Integer memory) {
		this.memory = memory;
	}

	public Integer getImageId() {
		return imageId;
	}

	public void setImageId(Integer imageId) {
		this.imageId = imageId;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Integer networkId) {
		this.networkId = networkId;
	}

	public Integer getDisk() {
		return disk;
	}

	public void setDisk(Integer disk) {
		this.disk = disk;
	}
}
