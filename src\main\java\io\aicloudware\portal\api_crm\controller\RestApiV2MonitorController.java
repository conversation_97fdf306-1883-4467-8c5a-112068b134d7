package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpMonitorBean;
import io.aicloudware.portal.framework.sdk.bean.SpMonitorResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpMonitorSearchBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpMonitor;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.service.ISpMonitorService;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

@Controller
@RequestMapping("/api/v2/monitor")
@Api(value = "/api/v2/monitor", description = "监控", position = 47)
public class RestApiV2MonitorController extends BaseEntityController<SpMonitor, SpMonitorBean, SpMonitorResultBean> {
	@Autowired
	private ISpMonitorService spMonitorService;
	
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpMonitorResultBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpMonitorSearchBean searchBean) {
    	SpMonitor entity = BeanCopyUtil.copy(searchBean.getBean(), getEntityType());
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setIsAutoScaling(false);
        entity.setRegion(ThreadCache.getRegion());
        if(StringUtils.isNotEmpty(entity.getName())) {
        	SpMonitorBean fuzzyBean = new SpMonitorBean();
        	fuzzyBean.setName(entity.getName());
        	entity.setName(null);
        	searchBean.setFuzzyBean(fuzzyBean);
        }
        SpMonitorBean[] entityList = doQuery(searchBean, entity);
        ResultListBean<SpMonitorBean> result = Utility.newInstance(getResultType());
        fillPageInfo(searchBean, result);
       
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }
    
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "新建监控")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpMonitorBean.class)})
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.MONITOR, description = "新建")
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody SpMonitorBean bean, BindingResult bindingResult) {
    	if (StringUtils.isEmpty(bean.getName())) {
    		return ResponseBean.error(1, "", "规则名称不能为空");
    	}
    	if (bean.getValue1() == null) {
    		return ResponseBean.error(1, "", "值1不能为空");
    	}
    	if (SpMonitorRuleType.cpu_usage_average.equals(bean.getRuleType()) || SpMonitorRuleType.mem_usage_average.equals(bean.getRuleType())) {
    		if (bean.getValue1()!=null && (bean.getValue1().compareTo(new BigDecimal(0))<0 || bean.getValue1().compareTo(new BigDecimal(100))>0)) {
    			return ResponseBean.error(1, "", "值1必须在0-100之间");
    		}
    		if (bean.getValue2()!=null && (bean.getValue2().compareTo(new BigDecimal(0))<0 || bean.getValue2().compareTo(new BigDecimal(100))>0)) {
    			return ResponseBean.error(1, "", "值2必须在0-100之间");
    		}
    	}
    	if (SpMonitorConditionType.bt.equals(bean.getCondition()) && bean.getValue2()==null) {
    		return ResponseBean.error(1, "", "值2不能为空");
    	}
    	Integer orgId = ThreadCache.getOrgId();
    	SpOrg spOrg = new SpOrg();
    	spOrg.setId(orgId);
    	SpMonitor monitor = BeanCopyUtil.copy(bean, SpMonitor.class);
    	monitor.setSpOrg(spOrg);
    	monitor.setIsAutoScaling(false);
    	monitor.setRegion(ThreadCache.getRegion());
    	SpMonitor spMonitor = spMonitorService.addMonitor(monitor);
        return ResponseBean.success(BeanCopyUtil.copy2Bean(spMonitor, SpMonitorBean.class).getId());
    }
    
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(notes = "/update", httpMethod = "POST", value = "修改监控")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpMonitorBean.class)})
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.MONITOR, description = "修改")
    public ResponseBean update(@ApiParam(value = "实例对象") @Valid @RequestBody SpMonitorBean bean, BindingResult bindingResult) {
    	if (StringUtils.isEmpty(bean.getName())) {
    		return ResponseBean.error(1, "", "规则名称不能为空");
    	}
    	if (bean.getValue1() == null) {
    		return ResponseBean.error(1, "", "值1不能为空");
    	}
    	if (SpMonitorRuleType.cpu_usage_average.equals(bean.getRuleType()) || SpMonitorRuleType.mem_usage_average.equals(bean.getRuleType())) {
    		if (bean.getValue1()!=null && (bean.getValue1().compareTo(new BigDecimal(0))<0 || bean.getValue1().compareTo(new BigDecimal(100))>0)) {
    			return ResponseBean.error(1, "", "值1必须在0-100之间");
    		}
    		if (bean.getValue2()!=null && (bean.getValue2().compareTo(new BigDecimal(0))<0 || bean.getValue2().compareTo(new BigDecimal(100))>0)) {
    			return ResponseBean.error(1, "", "值2必须在0-100之间");
    		}
    	}
    	if (SpMonitorConditionType.bt.equals(bean.getCondition()) && bean.getValue2()==null) {
    		return ResponseBean.error(1, "", "值2不能为空");
    	}
    	Integer orgId = ThreadCache.getOrgId();
    	SpOrg spOrg = new SpOrg();
    	spOrg.setId(orgId);
    	SpMonitor monitor = BeanCopyUtil.copy(bean, SpMonitor.class);
    	// check org
    	commonService.load(SpMonitor.class, SpMonitorBean.class, monitor.getId(), ThreadCache.getOrgId());
    	monitor.setSpOrg(spOrg);
    	monitor.setIsAutoScaling(false);
    	monitor = spMonitorService.updateMonitor(monitor);
        return ResponseBean.success(true);
    }
    
    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpMonitorBean.class)})
    @ResponseBody
    public ResponseBean getIpUsage(@ApiParam(value = "对象ID") @PathVariable Integer id) {
    	// check org
    	commonService.load(SpMonitor.class, SpMonitorBean.class, id, ThreadCache.getOrgId());
        return getEntity(id);
    }
    
    
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.MONITOR, description = "删除")
    public ResponseBean delete(@ApiParam(value = "对象ID") @PathVariable Integer id) {
    	// check org
    	commonService.load(SpMonitor.class, SpMonitorBean.class, id, ThreadCache.getOrgId());
    	List<Integer> list = new ArrayList<Integer>();
    	list.add(id);
    	commonService.delete(SpMonitor.class, list);
    	return ResponseBean.success(id);
    }
    
    @RequestMapping(value = "/types", method = RequestMethod.GET)
    @ApiOperation(notes = "/types", httpMethod = "GET", value = "查询类型")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回类型", response = HashMap.class)})
    @ResponseBody
    public HashMap<String, String> queryIpUsage() {
    	HashMap<String, String> map = new HashMap<>();
    	map.put(SpMonitorRuleType.cpu_usage_average.toString(),SpMonitorRuleType.cpu_usage_average.getTitle());
    	map.put(SpMonitorRuleType.mem_usage_average.toString(),SpMonitorRuleType.mem_usage_average.getTitle());
    	map.put(SpMonitorRuleType.virtualDisk_read_average.toString(),SpMonitorRuleType.virtualDisk_read_average.getTitle());
    	map.put(SpMonitorRuleType.virtualDisk_write_average.toString(),SpMonitorRuleType.virtualDisk_write_average.getTitle());
        return map;
    }
    
    @RequestMapping(value = "/init", method = RequestMethod.GET)
    @ApiOperation(notes = "/init", httpMethod = "GET", value = "初始化类型")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "初始化类型", response = HashMap.class)})
    @ResponseBody
    public Map<String, Object> init() {
    	Map<String,Object> datas = new HashMap<>();
    	
    	List<Map<String,String>> monitorRuleTypes = new ArrayList<>();
        for (SpMonitorRuleType type : SpMonitorRuleType.values()) {
        	Map<String, String> item = new LinkedHashMap<String, String>();
        	item.put("name", type.toString());
        	item.put("title", type.getTitle());
        	monitorRuleTypes.add(item);
        }
        
        List<Map<String,String>> monitorConditionTypes = new ArrayList<>();
        for (SpMonitorConditionType type : SpMonitorConditionType.values()) {
        	Map<String, String> item = new LinkedHashMap<String, String>();
        	item.put("name", type.toString());
        	item.put("title", type.getTitle());
        	monitorConditionTypes.add(item);
        }
        
        List<Map<String,String>> monitorTypes = new ArrayList<>();
        for (SpMonitorType type : SpMonitorType.values()) {
        	Map<String, String> item = new LinkedHashMap<String, String>();
        	item.put("name", type.toString());
        	item.put("title", type.getTitle());
        	monitorTypes.add(item);
        }
        
        List<Map<String,String>> notificationTypes = new ArrayList<>();
        for (SpNotificationType type : SpNotificationType.values()) {
        	Map<String, String> item = new LinkedHashMap<String, String>();
        	item.put("name", type.toString());
        	item.put("title", type.getTitle());
        	notificationTypes.add(item);
        }
        datas.put("monitorRuleTypes", monitorRuleTypes);
        datas.put("monitorConditionTypes", monitorConditionTypes);
        datas.put("monitorTypes", monitorTypes);
        datas.put("notificationTypes", notificationTypes);
        return datas;
    }
}
