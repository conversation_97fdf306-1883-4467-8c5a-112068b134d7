package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBean;
import io.swagger.annotations.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/api/v2/deployment")
@Api(value = "/api/v2/deployment", description = "订单", position = 140)
public class RestApiV2DeploymentController extends BaseEntityController {

    @RequestMapping(value = "/status/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/status/{id}", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpVmBean.class)})
    @ResponseBody
    public ResponseBean get(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        UpOrderBean bean = commonService.load(UpOrder.class, UpOrderBean.class, id, ThreadCache.getOrgId());
        bean.setApplyUserId(null);
        bean.setOwnerId(null);
        bean.setSpOrgId(null);
        bean.setSpOrgName(null);
        return ResponseBean.success(bean);
    }
    

}
