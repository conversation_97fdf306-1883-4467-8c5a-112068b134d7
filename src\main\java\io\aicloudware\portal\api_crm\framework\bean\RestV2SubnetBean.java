package io.aicloudware.portal.api_crm.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "VPC")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, RestV2SubnetBean.class})
public class RestV2SubnetBean extends RecordBean {

	private Long vpcId;

	private String gateway;

	private String netMask;

	private String networkSegment;

	private String ipBegin;

	private String ipEnd;

	private String dns1;

	private String dns2;

	public Long getVpcId() {
		return vpcId;
	}

	public void setVpcId(Long vpcId) {
		this.vpcId = vpcId;
	}

	public String getGateway() {
		return gateway;
	}

	public void setGateway(String gateway) {
		this.gateway = gateway;
	}

	public String getNetMask() {
		return netMask;
	}

	public void setNetMask(String netMask) {
		this.netMask = netMask;
	}

	public String getIpBegin() {
		return ipBegin;
	}

	public void setIpBegin(String ipBegin) {
		this.ipBegin = ipBegin;
	}

	public String getIpEnd() {
		return ipEnd;
	}

	public void setIpEnd(String ipEnd) {
		this.ipEnd = ipEnd;
	}

	public String getDns1() {
		return dns1;
	}

	public void setDns1(String dns1) {
		this.dns1 = dns1;
	}

	public String getDns2() {
		return dns2;
	}

	public void setDns2(String dns2) {
		this.dns2 = dns2;
	}

	public String getNetworkSegment() {
		return networkSegment;
	}

	public void setNetworkSegment(String networkSegment) {
		this.networkSegment = networkSegment;
	}
}
