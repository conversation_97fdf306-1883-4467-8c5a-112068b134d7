package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpOperationLog;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpOperationLogBean;
import io.aicloudware.portal.framework.sdk.bean.UpOperationLogResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/operation_log")
@Api(value = "/operation_log", description = "操作日志", position = 613)
public class UpOperationLogController extends BaseUpController<UpOperationLog, UpOperationLogBean, UpOperationLogResultBean> {

//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpOperationLogResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryOperationLog(@ApiParam(value = "查询条件") @RequestBody UpOperationLogSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpOperationLogBean.class)})
//    @ResponseBody
//    public ResponseBean getOperationLog(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
}
