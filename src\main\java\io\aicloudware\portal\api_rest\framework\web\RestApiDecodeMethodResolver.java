package io.aicloudware.portal.api_rest.framework.web;

import java.io.InputStreamReader;
import java.nio.charset.Charset;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import io.aicloudware.portal.api_rest.framework.annotation.RestRequestBodyDecode;
import io.aicloudware.portal.api_rest.framework.util.AesUtils;
import io.aicloudware.portal.framework.utility.Logger;

public class RestApiDecodeMethodResolver implements HandlerMethodArgumentResolver {

	private final static Logger logger = Logger.getLogger(RestApiDecodeMethodResolver.class);
	
	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		return parameter.hasParameterAnnotation(RestRequestBodyDecode.class) || parameter.hasMethodAnnotation(RestRequestBodyDecode.class);
	}

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
		HttpInputMessage message = new ServletServerHttpRequest(request);
		Charset charset = this.getContentCharset(message.getHeaders().getContentType());
		String body = FileCopyUtils.copyToString(new InputStreamReader(request.getInputStream(), charset));
		if (StringUtils.isEmpty(body)) {
			body = request.getQueryString();
		}
		if (body == null) {
			return null;
		}
		try {
			logger.info("" + body);
			return new JSONObject(AesUtils.decrypt(body));
		} catch (Exception e) {
			try {
				return new JSONObject(body);
			}catch (Exception ex) {
				throw new RuntimeException(ex.getMessage());
			}
		}
	}

	private Charset getContentCharset(MediaType mediaType) {
		if (mediaType != null && mediaType.getCharset() != null) {
			return mediaType.getCharset();
		} else {
			return Charset.forName("UTF-8");
		}
	}
}
