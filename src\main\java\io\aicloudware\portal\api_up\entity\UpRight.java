package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpRightBean;
import io.aicloudware.portal.framework.sdk.contants.UpRightType;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name = "up_right")
@Access(AccessType.FIELD)
public final class UpRight extends BaseUpEntity<UpRightBean> {

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpRightType type;

    @Column(name = "url")
    private String url;

    @Column(name = "target_id")
    private Integer targetId;

    @Column(name = "seq")
    private Integer seq;

    @Column(name = "icon")
    private String icon;

    @Column(name = "is_system_admin", nullable = false)
    private Boolean systemAdmin;

    @Column(name = "is_tenant_admin", nullable = false)
    private Boolean tenantAdmin;

    public UpRightType getType() {
        return type;
    }

    public void setType(UpRightType type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getTargetId() {
        return targetId;
    }

    public void setTargetId(Integer targetId) {
        this.targetId = targetId;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Boolean getSystemAdmin() {
        return systemAdmin;
    }

    public void setSystemAdmin(Boolean systemAdmin) {
        this.systemAdmin = systemAdmin;
    }

    public Boolean getTenantAdmin() {
        return tenantAdmin;
    }

    public void setTenantAdmin(Boolean tenantAdmin) {
        this.tenantAdmin = tenantAdmin;
    }
}
