package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.framework.bean.RestV2CloudServerBean;
import io.aicloudware.portal.api_crm.service.IRestCloudServerService;
import io.aicloudware.portal.api_crm.service.IRestVmService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.SpMetricsBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmSearchBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * rest api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/api/v2")
@Api(value = "/api/v2", description = "REST API V2")
public class RestApiV2CloudServerController extends BaseEntityController {

    @Autowired
    private IRestCloudServerService restCloudServerService;

    @Autowired
    private IRestVmService restVmService;



	@RequestMapping(value = "/cloud_server/add", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.SERVER, description = "新增云服务器")
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody RestV2CloudServerBean bean, HttpServletRequest request) {
        commonService.load(SpOVDCNetwork.class, bean.getNetworkId(), ThreadCache.getOrgId());
        return ResponseBean.success(restCloudServerService.save(bean));
    }

    @RequestMapping(value = "/cloud_server/update", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.SERVER, description = "更新云服务器")
    public ResponseBean update(@ApiParam(value = "实例对象") @Valid @RequestBody RestV2CloudServerBean bean, HttpServletRequest request) {
        commonService.load(SpVm.class, bean.getVmId(), ThreadCache.getOrgId());
        return ResponseBean.success(restCloudServerService.update(bean));
    }

    @RequestMapping(value = "/cloud_server/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/cloud_server/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVmResultBean.class)})
    @ResponseBody
    public ResponseBean queryVm(@ApiParam(value = "查询条件") @RequestBody SpVmSearchBean searchBean) {
        if(ThreadCache.getOrgId()==null) {
            return ResponseBean.success(new ArrayList<>());
        }
        SpVm entity = BeanCopyUtil.copy(searchBean.getBean(), SpVm.class);
        entity.setRegion(ThreadCache.getRegion());
        SpOrg org = commonService.load(SpOrg.class, ThreadCache.getOrgId());
//        SpOrg org = commonService.load(SpOrg.class,399);

        entity.setSpOrg(org);
        if(org.getIsArchive() != null && org.getIsArchive()) {
            entity.setOwner(ThreadCache.getUser());
        }else {
//        	SpVmBean bean = searchBean.getBean() == null ? new SpVmBean() : searchBean.getBean();
//        	bean.setOwnerId(ThreadCache.getUserId());
//        	searchBean.setBean(bean);
        }
        searchBean.setSearchType(SpVmType.iaas);

        if (StringUtils.isNotEmpty(entity.getName())) {
            SpVmBean fuzzyBean = new SpVmBean();
            fuzzyBean.setName(entity.getName());
            fuzzyBean.setIpAddress(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }

        try {
            SpVmBean[] entityList = restVmService.query(searchBean, entity);
            SpVmResultBean result = new SpVmResultBean();
            fillPageInfo(searchBean, result);
            result.setDataList(entityList);
            return ResponseBean.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseBean.success(null);
    }

    @RequestMapping(value = "/cloud_server/power_on/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/cloud_server/power_on/{id}", httpMethod = "POST", value = "虚拟机开机")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SERVER, description = "开机")
    public ResponseBean powerOn(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success(restVmService.powerOn(id));
    }

    @RequestMapping(value = "/cloud_server/power_off/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/cloud_server/power_off/{id}", httpMethod = "POST", value = "虚拟机关机")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SERVER, description = "关机")
    public ResponseBean powerOff(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success(restVmService.powerOff(id));
    }

    @RequestMapping(value = "/cloud_server/power_reboot/{id}", method = RequestMethod.POST)
    @ApiOperation(notes = "/cloud_server/power_reboot/{id}", httpMethod = "POST", value = "虚拟机重启")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SERVER, description = "重启")
    public ResponseBean powerReboot(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success(restVmService.powerReboot(id));
    }

//    @RequestMapping(value = "/cloud_server/power_reset/{id}", method = RequestMethod.POST)
//    @ApiOperation(notes = "/cloud_server/power_reset/{id}", httpMethod = "POST", value = "虚拟机重置")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
//    @ResponseBody
//    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SERVER, description = "重置")
//    public ResponseBean powerReset(@ApiParam(value = "对象ID") @PathVariable String id) {
//        return ResponseBean.success(spVmService.powerReset(id));
//    }

    @RequestMapping(value = "/cloud_server/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/cloud_server/delete/{id}", httpMethod = "POST", value = "删除虚机")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SERVER, description = "删除")
    public ResponseBean deleteVm(@ApiParam(value = "对象ID") @PathVariable String id) {
        restVmService.deleteVm(id);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/cloud_server/ticket/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/cloud_server/ticket/{id}", httpMethod = "GET", value = "虚拟机mksticket")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = String.class)})
    @ResponseBody
    public ResponseBean getMksTicket(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success(restVmService.getMksTicket(id));
    }

    @RequestMapping(value = "/cloud_server/refresh/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/cloud_server/refresh/{id}", httpMethod = "GET", value = "获取实例对象")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpVmBean.class)})
    @ResponseBody
    public ResponseBean refreshVm(@ApiParam(value = "对象ID") @PathVariable String id) {
        Integer vmId = restVmService.refresh(id);
        SpVmBean bean = commonService.load(SpVm.class, SpVmBean.class, vmId, ThreadCache.getOrgId());
        return ResponseBean.success(bean);
    }


    @RequestMapping(value = "/cloud_server/images", method = RequestMethod.GET)
    @ApiOperation(notes = "/cloud_server/images", httpMethod = "GET", value = "查询监控数据")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回类型", response = ArrayList.class)})
    @ResponseBody
    public List<Map<String,Object>> images() {
        List<Map<String,Object>> images = restVmService.getTemplatesByCatalog(ThreadCache.getRegion(), ThreadCache.getOrgId(), UpOrderSystemEnums.CatalogType.iaas, UpOrderSystemEnums.CatalogType.local);
        return images;
    }



}
