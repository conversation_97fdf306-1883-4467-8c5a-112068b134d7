package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.entity.UpVmRecord;
import io.aicloudware.portal.api_up.service.IUpQuotaService;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.dao.ICloudDao;
import io.aicloudware.portal.framework.entity.IEntity;
import io.aicloudware.portal.framework.entity.IRequestEntity;
import io.aicloudware.portal.framework.exception.LogicException;
import io.aicloudware.portal.framework.hibernate.PropertyFilter;
import io.aicloudware.portal.framework.sdk.bean.SpVmBean;
import io.aicloudware.portal.framework.sdk.bean.SpVmTicketBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmRecordSearchBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.entity.*;
import io.aicloudware.portal.platform_vcd.service.ISpVappTemplateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;

@Service
@Transactional
public class RestVmService extends BaseService implements IRestVmService {
    
    @Autowired
    protected IUpQuotaService upQuotaService;

    @Autowired
    protected ICommonService commonService;
    
    @Autowired
    protected ICloudDao cloudDao;
    
    @Autowired
    protected IUpOrderService orderService;

    @Autowired
    protected ISpVappTemplateService spBlueprintService;
    
    @Override
    public SpVmBean[] query(SearchBean<SpVmBean> searchBean, SpVm spVm) {
        logger.info("orgId:"+spVm.getSpOrg().getId());
        SpOrg spOrg = dao.load(SpOrg.class, spVm.getSpOrg().getId());
        if(spOrg != null && Boolean.TRUE.equals(spOrg.getExternal())) {
            List<SpExtVm> spExtVms = queryDao.queryHql("from SpExtVm where status=:status and spOrg=:spOrg",
                    MapUtil.of("status", RecordStatus.active, "spOrg", spOrg));
            if (spExtVms != null) {
                SpVmBean[] vmBeanList = new SpVmBean[spExtVms.size()];
                for (int i = 0; i < spExtVms.size(); i++) {
                    SpExtVm extVm = spExtVms.get(i);
                    SpVmBean vmBean = new SpVmBean();
                    vmBean.setId(extVm.getId());
                    vmBean.setName(extVm.getName());
                    vmBean.setIpAddress(extVm.getIpAddress());
                    vmBean.setCpuNum(extVm.getCpuNum());
                    vmBean.setMemoryGB(extVm.getMemoryGB());
                    vmBean.setPowerStatus(extVm.getPowerStatus());
                    vmBean.setSpUuid(extVm.getSpUuid());
                    vmBean.setDeployStatus(SpDeployStatus.COMPLETE);
                    vmBeanList[i] = vmBean;
                }
                return vmBeanList;
            }

            return new SpVmBean[0];
        }
        List<SpVm> vmList = dao.query(searchBean, spVm);
        SpVmBean[] vmBeanList = BeanCopyUtil.copy2BeanList(vmList, SpVmBean.class);
        Map<Integer, SpVm> vmMap = ListUtil.map(vmList, new ListUtil.ConvertKey<SpVm, Integer>() {
            @Override
            public Integer getKey(SpVm value) {
                return value.getId();
            }
        });
        for (SpVmBean vmBean : vmBeanList) {
            List<SpIpBinding> ipBindings = vmMap.get(vmBean.getId()).getIpBindings();
            if (Utility.isNotEmpty(ipBindings)) {
//                if (ipBindings.get(0) != null && ipBindings.get(0).getElasticIp() != null
//                        && ipBindings.get(0).getElasticIp().getStatus() != RecordStatus.deleted) {
//                    vmBean.setPublicIpAddress(ipBindings.get(0).getElasticIp().getIpAddress());
//                }
                List<String> publicIPList = new ArrayList<>();
                List<String> publicPort = new ArrayList<>();
                List<String> vmPort = new ArrayList<>();
                List<SpProtocolType> protocolList = new ArrayList<>();
                List<SpIpBindingType> ipBindingTypeList = new ArrayList<>();
                for (SpIpBinding ipBinding:ipBindings) {
                	publicIPList.add(ipBinding.getElasticIp().getIpAddress());
                	publicPort.add(ipBinding.getPublic_port());
                	vmPort.add(ipBinding.getVm_port());
                	protocolList.add(ipBinding.getProtocol());
                	ipBindingTypeList.add(ipBinding.getType());
                }
                vmBean.setPublicIpAddress(publicIPList.toArray(new String[0]));
                vmBean.setPublicPort(publicPort.toArray(new String[0]));
                vmBean.setPort(vmPort.toArray(new String[0]));
                vmBean.setProtocol(protocolList.toArray(new SpProtocolType[0]));
                vmBean.setBindingType(ipBindingTypeList.toArray(new SpIpBindingType[0]));
            }
            SpVmBackupStrategy backupStrategy = vmMap.get(vmBean.getId()).getVmBackupStrategy();
            if (backupStrategy != null) {
                vmBean.setBackupStrategyName(backupStrategy.getName());
                vmBean.setBackupStrategyId(backupStrategy.getId());
            }
        }
        Arrays.sort(vmBeanList, (s1, s2) -> Integer.compare(s2.getId(), s1.getId()));
        return vmBeanList;
    }

    private void cascadeUpdate(RecordBean bean, IEntity entity, UpOrder order) {
        if (bean != null && entity != null) {
            for (Field field : Utility.describeFieldMap(bean).values()) {
                if (field.getType().isArray()) {
                    RecordBean[] beanList = (RecordBean[]) Utility.getFieldValue(field, bean);
                    if (Utility.isNotEmpty(beanList)) {
                        Field field2 = Utility.describeFieldMap(entity).get(field.getName());
                        if (field2 != null) {
                            Collection<IEntity> entityList = (Collection<IEntity>) Utility.getFieldValue(field2, entity);
                            if (entityList == null) {
                                entityList = new ArrayList<>();
                            }
                            Map<Integer, IEntity> entityMap = ListUtil.map(entityList, new ListUtil.ConvertKey<IEntity, Integer>() {
                                @Override
                                public Integer getKey(IEntity value) {
                                    return value.getId();
                                }
                            });
                            for (RecordBean childBean : beanList) {
                                if (UpOperateType.add.equals(childBean.getOperateType())) {
                                    Class<IEntity> clazz = (Class<IEntity>) Utility.getCollectionType(entity.getClass(), field2);
                                    IEntity childEntity = BeanCopyUtil.copy(childBean, clazz);
                                    DaoUtil.setParentEntity(entity, childEntity);
                                    if (childEntity instanceof IRequestEntity) {
                                        ((IRequestEntity) childEntity).setOrder(order);
                                    }
                                    dao.insert(childEntity);
                                } else if (UpOperateType.update.equals(childBean.getOperateType())
                                        || UpOperateType.update_child.equals(childBean.getOperateType())) {
                                    IEntity childEntity = entityMap.get(childBean.getId());
                                    if (UpOperateType.update.equals(childBean.getOperateType())) {
                                        BeanCopyUtil.copy(childBean, childEntity, PropertyFilter.isCopyOnUpdate);
                                        if (childEntity instanceof IRequestEntity) {
                                            ((IRequestEntity) childEntity).setOrder(order);
                                        }
                                        dao.update(childEntity);
                                    }
                                    cascadeUpdate(childBean, childEntity, order);
                                } else if (UpOperateType.delete.equals(childBean.getOperateType())) {
                                    Class<IEntity> clazz = (Class<IEntity>) Utility.getCollectionType(entity.getClass(), field2);
                                    dao.delete(clazz, childBean.getId());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void updateVmRecord(SpVm vm, UpVmRecordType recordType) {
        if (vm != null) {
            UpVmRecord vmRecord = new UpVmRecord();
            vmRecord.setRegion(vm.getRegion());
            vmRecord.setVm(vm);
            vmRecord.setRecordDt(FormatUtil.formatCalendar(Calendar.getInstance()));
            vmRecord = ListUtil.first(dao.query(new UpVmRecordSearchBean(), vmRecord));
            if (vmRecord == null) {
                vmRecord = new UpVmRecord();
                vmRecord.setRecordDt(FormatUtil.formatCalendar(Calendar.getInstance()));
                vmRecord.setName(vm.getName());
                vmRecord.setVm(vm);
            }
            vmRecord.setRecordType(recordType);
            vmRecord.setOwner(vm.getOwner());

            if (UpVmRecordType.delete.equals(recordType)) {
                vmRecord.setCpu(0);
                vmRecord.setMemoryGB(0);
                vmRecord.setDiskGB(0);
                vmRecord.setNetwork(0);
            } else if (UpVmRecordType.archive.equals(recordType)) {
                vmRecord.setCpu(0);
                vmRecord.setMemoryGB(0);
                vmRecord.setDiskGB(vm.getDiskGB());
                vmRecord.setNetwork(0);
            } else {
                vmRecord.setCpu(vm.getCpuNum());
                vmRecord.setMemoryGB(vm.getMemoryGB());
                vmRecord.setDiskGB(vm.getDiskGB());
                vmRecord.setNetwork(vm.getNetworkList().size());
            }

            if (Utility.isZero(vmRecord.getId())) {
                dao.insert(vmRecord);
            } else {
                dao.update(vmRecord);
            }
        }
    }

    @Override
    public Integer powerOn(String vmId) {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        SpVm vm = cloudDao.loadBySpUuid(SpVm.class, org, vmId);
        AssertUtil.check(vm != null, "虚拟机不存在");

        UpUser user = vm.getOwner();
        if (user == null) {
            user = ThreadCache.getUser();
        }
        if (user.getIsArrearage() == null) {
            user = this.dao.load(UpUser.class, user.getId());
        }
        AssertUtil.check(user.getIsArrearage() == null || !user.getIsArrearage(), "您的账户已欠费，请充值！");

        return orderService.createSimpleOrderCloudServer(new SpVm[]{vm}, OrderType.vm_power_on);
    }

    @Override
    public Integer powerOff(String vmId) {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        SpVm vm = cloudDao.loadBySpUuid(SpVm.class, org, vmId);
        return powerOff(vm);
    }
    
    @Override
    public Integer powerOff(SpVm vm) {
        AssertUtil.check(vm!=null, "虚拟机不存在");
        return orderService.createSimpleOrderCloudServer(new SpVm[]{vm}, OrderType.vm_power_off);
    }
    
    @Override
    public Integer powerReboot(String vmId) {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        SpVm vm = cloudDao.loadBySpUuid(SpVm.class, org, vmId);
        AssertUtil.check(vm!=null, "虚拟机不存在");

        return orderService.createSimpleOrderCloudServer(new SpVm[]{vm}, OrderType.vm_power_reboot);
    }
    
    @Override
    public Integer powerReset(String vmId) {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        SpVm vm = cloudDao.loadBySpUuid(SpVm.class, org, vmId);
        AssertUtil.check(vm!=null, "虚拟机不存在");

        return orderService.createSimpleOrderCloudServer(new SpVm[]{vm}, OrderType.vm_power_reset);
    }

    @Override
    public Integer deleteVm(String vmId) {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        if(org != null && Boolean.TRUE.equals(org.getExternal())) {
            AssertUtil.check(false, "此虚拟机不支持删除。");
        }
        SpVm vm = cloudDao.loadBySpUuid(SpVm.class, org, vmId);
        AssertUtil.check(vm != null, "虚拟机不存在");

        String loadBalancerPool = queryVmBindLoadBalancerPool(vm);
        AssertUtil.check(Utility.isBlank(loadBalancerPool), "虚拟机已绑定负载均衡(" + loadBalancerPool + ")，请解绑后再删除.");

        List<SpIpBinding> ipbindings = vm.getIpBindings();
        if (ipbindings != null && ipbindings.size() > 0) {
            String bindingIp = ipbindings.get(0).getElasticIp().getIpAddress();
            throw new LogicException("虚拟机已绑定公网IP(" + bindingIp + ")，请解绑后再删除.");
        }

        List<SpVmDisk> diskList = dao.list(SpVmDisk.class, MapUtil.of( "vm", vm, "type", SpVmDiskType.mount));
        if(diskList != null && diskList.size() > 0){
            throw new LogicException("虚拟机存在挂载的存储(" + diskList.get(0).getDiskLabel() + ")，请删除存储后再删除虚机.");
        }

        return orderService.createSimpleOrderCloudServer(new SpVm[]{vm}, OrderType.cloud_server_delete);
    }

    @Override
    public SpVmTicketBean getMksTicket(String vmId) {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        if(org != null && Boolean.TRUE.equals(org.getExternal())) {
            AssertUtil.check(false, "此虚拟机不支持获取显示控制台。");
        }
        SpVm vm = cloudDao.loadBySpUuid(SpVm.class, org, vmId);
        AssertUtil.check(vm!=null, "虚拟机不存在");
        return cloudService.getMksTicket(vm);
    }

    @Override
    public String queryVmBindLoadBalancerPool(SpVm vm) {
        String[] ipaddresss = StringUtils.split(vm.getIpAddress());
        String ipaddrs = "";
        if (ipaddresss != null && ipaddresss.length > 0) {
            for (String ipaddr : ipaddresss) {
                ipaddrs += "'" + ipaddr + "',";
            }
        }

        if (StringUtils.isBlank(ipaddrs)) {
            return "";
        }else {
            ipaddrs = StringUtils.substringBeforeLast(ipaddrs, ",");
        }
        String lbPoolName = "";
        String sql = "select p.id, p.name "
                    + " from sp_load_balancer_member m, sp_vm v, sp_load_balancer_pool p"
                    + " where m.status = '" + RecordStatus.active + "' and v.status = '" + RecordStatus.active + "' and p.status = '" + RecordStatus.active + "' and m.load_balancer_pool_id=p.id "
                    + " and m.sp_org_id = v.sp_org_id and v.sp_org_id = " + vm.getSpOrg().getId()
                    + " and m.ip_address = v.ip_address and m.ip_address in (" + ipaddrs + ")"
                    + " and v.vm_type = '" + SpVmType.iaas + "'";

        List<Object[]> datas = queryDao.querySql(sql, null);
        if (datas != null && datas.size() > 0) {
            for (Object[] data : datas) {
                lbPoolName = ((String) data[1]);
                break;
            }
        }
        return lbPoolName;
    }

    @Override
    public Integer refresh(String vmId) {
        SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
        SpVm entity = cloudDao.loadBySpUuid(SpVm.class, org, vmId);
        DaoUtil.cascadeChildEntity(entity, null, null);
        cloudService.vm_refresh(entity);
        dao.update(entity);
        if(entity.getDiskList() != null) {
            for(SpVmDisk disk:entity.getDiskList()) {
                if(disk.getId() == null) {
                    dao.insert(disk);
                } else {
                    dao.update(disk);
                }
            }
        }
        return entity.getId();
    }

    @Override
    public void refresh(Integer vmId) {
        SpVm entity = dao.load(SpVm.class, vmId);
        DaoUtil.cascadeChildEntity(entity, null, null);
        cloudService.vm_refresh(entity);
        dao.update(entity);
        if(entity.getDiskList() != null) {
            for(SpVmDisk disk:entity.getDiskList()) {
                if(disk.getId() == null) {
                    dao.insert(disk);
                } else {
                    dao.update(disk);
                }
            }
        }
    }

    private SpVimServer createEndpoint() {
        SpVimServer endpoint = new SpVimServer();
        endpoint.setServerUrl("https://10.204.13.100");
        endpoint.setUsername("<EMAIL>");
        endpoint.setPassword("dchXdJ6z$0GvSw3i");
        return endpoint;
    }

    @Override
    public List<Map<String,Object>> getTemplatesByCatalog(SpRegionEntity region, Integer orgId, UpOrderSystemEnums.CatalogType... types) {
        List<Map<String,Object>> list = new ArrayList<>();
        for(UpOrderSystemEnums.CatalogType type : types) {
            if(type == UpOrderSystemEnums.CatalogType.local) {
                SpOrg privateOrg = this.dao.load(SpOrg.class, orgId);
                collectTemplate(region, type, privateOrg, list);
            }else {
                List<SpOrg> publicOrgs = this.dao.list(SpOrg.class, "name", "Public");
                if(publicOrgs == null || publicOrgs.size() == 0) {
                    continue;
                }
                SpOrg publicOrg = publicOrgs.get(0);
                collectTemplate(region, type, publicOrg, list);
            }
        }
        return list;
    }

    private void collectTemplate(SpRegionEntity region, UpOrderSystemEnums.CatalogType type, SpOrg org, List<Map<String,Object>> list) {
        List<SpCatalog> catalogs = this.queryDao.queryHql("from SpCatalog where name like '"+type.toString()+"%' and spOrg=:spOrg and status=:status",
                MapUtil.of("status", RecordStatus.active, "spOrg", org));
        if(catalogs == null || catalogs.size() == 0) {
            return;
        }
        // get all private image from all region
        for (SpCatalog catalog : catalogs) {
            if(type== UpOrderSystemEnums.CatalogType.iaas) {
                if(!catalog.getName().equals("iaas")){
                    continue;
                }
            } else if (type== UpOrderSystemEnums.CatalogType.local) {
            }
            List<SpVappTemplate> entitys = this.dao.list(SpVappTemplate.class, "catalog", catalog);
            for (SpVappTemplate entity : entitys) {
                Map<String, Object> item = new HashMap<>();
                item.put("catalogId", entity.getId());
                item.put("name", entity.getDisplayName());
                item.put("systemDiskGB", entity.getDiskSize() / 1024L / 1024L / 1024L);
                list.add(item);
            }
        }
    }
}
