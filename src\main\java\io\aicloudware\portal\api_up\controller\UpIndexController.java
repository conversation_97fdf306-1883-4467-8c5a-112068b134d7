package io.aicloudware.portal.api_up.controller;

import com.google.common.base.Optional;
import io.aicloudware.portal.framework.controller.BaseController;
import io.aicloudware.portal.framework.utility.ClassReaderUtil;
import io.aicloudware.portal.framework.utility.ListUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;
import springfox.documentation.service.Documentation;
import springfox.documentation.service.Tag;
import springfox.documentation.spring.web.DocumentationCache;
import springfox.documentation.spring.web.plugins.Docket;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Controller
@RequestMapping("/")
@ApiIgnore
public class UpIndexController extends BaseController {

    @Autowired
    private DocumentationCache documentationCache;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public void index(HttpServletRequest request, HttpServletResponse response) {
        try {
            response.sendRedirect(request.getContextPath() + "/swagger");
        } catch (IOException e) {
        }
    }

    @RequestMapping(value = "/api-list", method = RequestMethod.GET)
    @ResponseBody
    public List<String> apiList() {
        List<Api> apiList = new ArrayList<>();
        Set<Class<?>> classSet = ClassReaderUtil.getClasses(getClass().getPackage().getName());
        for (Class<?> clazz : classSet) {
            Api api = clazz.getAnnotation(Api.class);
            if (api != null) {
                apiList.add(api);
            }
        }
        Collections.sort(apiList, (o1, o2) -> o1.position() - o2.position());
        List<String> apiNameList = new ArrayList<>(apiList.size());
        String groupName = Optional.fromNullable((String) null).or(Docket.DEFAULT_GROUP_NAME);
        Documentation documentation = documentationCache.documentationByGroup(groupName);
        Map<String, String> tagMap = ListUtil.map(new ArrayList<Tag>(documentation.getTags()),
                new ListUtil.Convert<Tag, String, String>() {
                    @Override
                    public String getKey(Tag value) {
                        return value.getDescription();
                    }

                    @Override
                    public String getValue(Tag value) {
                        return value.getName();
                    }
                }
        );
        for (Api api : apiList) {
            apiNameList.add(tagMap.get(api.description()));
        }
        return apiNameList;
    }
}
