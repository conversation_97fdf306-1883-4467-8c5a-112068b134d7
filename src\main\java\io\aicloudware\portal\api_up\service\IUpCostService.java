package io.aicloudware.portal.api_up.service;


import io.aicloudware.portal.framework.sdk.bean.UpVmCostResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostSearchBean;
import io.aicloudware.portal.framework.sdk.bean.UpYearlyStartDateBean;
import io.aicloudware.portal.framework.sdk.bean.UpYearlyStartDateSetResultBean;
import org.springframework.transaction.annotation.Transactional;

@Transactional
public interface IUpCostService {

    public UpVmCostResultBean groupStatistic(UpVmCostSearchBean searchBean);

    public UpVmCostResultBean groupStatisticDetail(UpVmCostSearchBean searchBean);

    public UpVmCostResultBean appSystemStatistic(UpVmCostSearchBean searchBean);

    public UpVmCostResultBean appSystemStatisticDetail(UpVmCostSearchBean searchBean);

    public UpVmCostResultBean query(UpVmCostResultBean resultBean, UpVmCostSearchBean searchBean);

    public UpYearlyStartDateSetResultBean yearlyStartdateSet(UpYearlyStartDateBean bean);
}
