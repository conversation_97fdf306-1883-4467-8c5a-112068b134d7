package io.aicloudware.portal.api_up.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseEntity;

@Entity
@Table(name = "up_user_logout")
@Access(AccessType.FIELD)
public final class UpUserLogout extends BaseEntity{

    @Column(name = "invalid_time_millis")
    private String invalidTimeMillis;

    @JoinColumn(name = "user_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private UpUser user;

	public String getInvalidTimeMillis() {
		return invalidTimeMillis;
	}

	public void setInvalidTimeMillis(String invalidTimeMillis) {
		this.invalidTimeMillis = invalidTimeMillis;
	}

	public UpUser getUser() {
		return user;
	}

	public void setUser(UpUser user) {
		this.user = user;
	}
    
}
