package io.aicloudware.portal.api_rest.framework.bean;

/**
 * @author: lqx
 * @date: 2020/6/1 15:18
 * @version: 1.0
 * @desc: 生成用户token信息, 所有字段类型必须是String
 */
public class GenTokenParams {

    /**
     * userId memberId clientId
     */
    private String id;

    /**
     * 用户编码
     */
    private String userCode;

    private String regionId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String telPhone;

    /**
     * 回话id
     */
    private String sessionId;

    /**
     * 类型 MEMBER USER CLIENT
     */
    private String type;

    /**
     * 员工编号
     * 管理端使用
     */
    private String staffNo;

    /**
     * api使用
     */
    private String clientId;

    /**
     * 租户id
     * portal使用
     */
    private String tenantId;

    /**
     * 父账号id
     * portal使用
     */
    private String parentMemberId;

    /**
     * 是否为默认子账号
     * portal使用
     */
    private String childDefFlag;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getRegionId() {
		return regionId;
	}

	public void setRegionId(String regionId) {
		this.regionId = regionId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getTelPhone() {
		return telPhone;
	}

	public void setTelPhone(String telPhone) {
		this.telPhone = telPhone;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getStaffNo() {
		return staffNo;
	}

	public void setStaffNo(String staffNo) {
		this.staffNo = staffNo;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getParentMemberId() {
		return parentMemberId;
	}

	public void setParentMemberId(String parentMemberId) {
		this.parentMemberId = parentMemberId;
	}

	public String getChildDefFlag() {
		return childDefFlag;
	}

	public void setChildDefFlag(String childDefFlag) {
		this.childDefFlag = childDefFlag;
	}

}
