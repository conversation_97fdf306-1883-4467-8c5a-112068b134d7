package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpAppSystemBean;
import io.aicloudware.portal.framework.sdk.contants.UpAppSystemStatus;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "up_app_system")
@Access(AccessType.FIELD)
public class UpAppSystem extends BaseUpEntity<UpAppSystemBean> {

    public UpAppSystem(){}

    public UpAppSystem(Integer id) {
        super(id);
    }

    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @Column(name = "comment")
    private String comment;

    @Column(name = "uid")
    private String uid;

    @JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg org;

    @JoinColumn(name = "parent_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpAppSystem parent;

    @Column(name = "app_system_status")
    @Enumerated(EnumType.STRING)
    private UpAppSystemStatus appSystemStatus;

    @Column(name = "delete_flag")
    private Boolean deleteFlag;


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "parent")
    @Where(clause = "status!='deleted'")
    @org.hibernate.annotations.OrderBy(clause = "id")
    private List<UpAppSystem> children;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "appSystem")
    @Where(clause = "status!='deleted'")
    @org.hibernate.annotations.OrderBy(clause = "id")
    private List<UpAppSystemUserRelation> relations;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public SpOrg getOrg() {
        return org;
    }

    public void setOrg(SpOrg org) {
        this.org = org;
    }

    public UpAppSystem getParent() {
        return parent;
    }

    public void setParent(UpAppSystem parent) {
        this.parent = parent;
    }

    public List<UpAppSystem> getChildren() {
        return children;
    }

    public void setChildren(List<UpAppSystem> children) {
        this.children = children;
    }

    public List<UpAppSystemUserRelation> getRelations() {
        return relations;
    }

    public void setRelations(List<UpAppSystemUserRelation> relations) {
        this.relations = relations;
    }

    public UpAppSystemStatus getAppSystemStatus() {
        return appSystemStatus;
    }

    public void setAppSystemStatus(UpAppSystemStatus appSystemStatus) {
        this.appSystemStatus = appSystemStatus;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
    }
}
