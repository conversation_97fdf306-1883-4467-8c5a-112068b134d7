package io.aicloudware.portal.platform_vcd.service;

import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.framework.sdk.contants.CloudType;
import io.aicloudware.portal.framework.sdk.contants.SpSecurityVersion;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.platform_vcd.entity.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域服务实现类
 */
@Service
@Transactional
public class SpRegionService extends BaseService implements ISpRegionService {

    private static final Logger log = Logger.getLogger(SpRegionService.class);

    private static Map<String, SpRegionEntity> regionMap = new HashMap<>();

    public static final String CIDCRP11 = "CIDCRP11";
    public static final String CIDCRP11_CODE = "CIDC-RP-11";
    public static final String CIDCRP12 = "CIDCRP12";
    public static final String CIDCRP35 = "CIDCRP35";

    /**
     * 初始化区域数据
     * 如果数据库中没有区域数据，则从原先的Enum中导入
     */
    @Override
    public void init() {
        if(dao.list(SpRegionEntity.class).isEmpty()){
            // 从原先的Enum中导入区域数据
            List<SpRegionEntity> regions = new ArrayList<>();
            addRegion(regions, "CIDCRP11","华北-北京2","CIDC-RP-11",SpSecurityVersion.v2,  CloudType.jointecloud, "000");
            addRegion(regions, "CIDCRP12","华东-无锡","CIDC-RP-12",SpSecurityVersion.v2,  CloudType.jointecloud, "000");
            addRegion(regions, "CIDCRP04","云南-昆明","CIDC-RP-04",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP25","华东-苏州","CIDC-RP-25",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP26","华南-广州3","CIDC-RP-26",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP27","西南-成都","CIDC-RP-27",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP28","华中-郑州1","CIDC-RP-28",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP29","华北-北京3","CIDC-RP-29",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP30","华中-长沙2","CIDC-RP-30",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP31","华东-济南","CIDC-RP-31",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP32","西北-西安","CIDC-RP-32",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP33","华东-上海1","CIDC-RP-33",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP34","西南-重庆","CIDC-RP-34",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP35","华东-杭州","CIDC-RP-35",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP36","天津-天津","CIDC-RP-36",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP37","吉林-长春","CIDC-RP-37",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP38","湖北-襄阳","CIDC-RP-38",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP39","江西-南昌","CIDC-RP-39",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP40","甘肃-兰州","CIDC-RP-40",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP41","山西-太原","CIDC-RP-41",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP42","辽宁-沈阳","CIDC-RP-42",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP43","云南-昆明2","CIDC-RP-43",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP44","河北-石家庄","CIDC-RP-44",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP45","福建-厦门","CIDC-RP-45",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP46","广西-南宁","CIDC-RP-46",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP47","安徽-淮南","CIDC-RP-47",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP48","华北-呼和浩特","CIDC-RP-48",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP49","西南-贵阳","CIDC-RP-49",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP53","海南-海口","CIDC-RP-53",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP54","新疆-昌吉","CIDC-RP-54",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP55","黑龙江-哈尔滨","CIDC-RP-55",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP60","宁夏-中卫","CIDC-RP-60",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP61","青海-海东","CIDC-RP-61",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP62","西藏-拉萨","CIDC-RP-62",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            addRegion(regions, "CIDCRP65","广西-南宁3","CIDC-RP-65",SpSecurityVersion.v2,  CloudType.ecloud, "000");
            dao.insert(regions);
            dao.flush();
            log.info("Initialized " + regions.size() + " regions");

            updateEntityRegion(SpAuditLog.class, SpElasticIp.class, SpIpScope.class, SpK8sCluster.class, SpK8sClusterNode.class,
                    SpK8sClusterNodePool.class, SpOVDC.class, SpOVDCNetwork.class, SpSecureKey.class,  SpVm.class, SpVmDisk.class);


//            SpProject.class,

        }
    }

    private void updateEntityRegion(Class<? extends BaseSpEntity>... clazzList) {
        Arrays.stream(clazzList).forEach(clazz -> {
            List<? extends BaseSpEntity> list = dao.list(clazz); // 修改为支持泛型子类
            list.forEach(entity -> entity.setRegion(regionMap.get(entity.getOldRegion())));
            dao.update(list, "region");
        });
    }

    /**
     * 辅助方法，用于创建区域实体
     */
    private void addRegion(List<SpRegionEntity> regions, String name, String title, String code,
                           SpSecurityVersion version, CloudType type, String securityUrlPort) {
        SpRegionEntity entity = new SpRegionEntity();
        entity.setName(name);
        entity.setTitle(title);
        entity.setCode(code);
        entity.setVersion(version);
        entity.setType(type);
        entity.setSecurityUrlPort(securityUrlPort);
        regions.add(entity);
    }

    @Override
    public SpRegionBean[] list() {
        return BeanCopyUtil.copy2BeanList(regionMap.values(), SpRegionBean.class);
//        return BeanCopyUtil.copy2BeanList(dao.list(SpRegionEntity.class), SpRegionBean.class);
    }

    @Override
    public Map<String, SpRegionEntity> getRegionMap() {
        // 如果regionMap为空，先刷新一次
        if (regionMap.isEmpty()) {
            refreshRegionMap();
        }
        return regionMap;
    }

    /**
     * 刷新区域映射表
     * 定时刷新 regionMap，每10分钟执行一次
     */
    @Override
    @Scheduled(fixedRate = 600000) // 600000 毫秒 = 10 分钟
    public void refreshRegionMap() {
        List<SpRegionEntity> regions = dao.list(SpRegionEntity.class);
        regionMap = regions.stream()
                .collect(Collectors.toMap(
                        SpRegionEntity::getName,
                        region -> region,
                        (existing, replacement) -> existing // 如果有重复 key，保留旧值
                ));
        // 可选：打印日志
        log.info("Region refreshed, size: " + regionMap.size());
    }

    @Override
    public SpRegionEntity getRegionByName(String name) {
        if (!regionMap.containsKey(name)) {
            refreshRegionMap();
        }
        return regionMap.get(name);
    }

    @Override
    public SpRegionEntity getRegionByCode(String code) {
        if (regionMap.size() == 0) {
            refreshRegionMap();
        }
        return regionMap.values().stream().filter(region -> region.getCode().equals(code)).findFirst().get();
    }

    @Override
    public SpRegionBean CIDCRP35() {
        return BeanCopyUtil.copy(getRegionByName(CIDCRP35), SpRegionBean.class);
    }

    @Override
    public SpRegionBean CIDCRP11() {
        return BeanCopyUtil.copy(getRegionByName(CIDCRP11), SpRegionBean.class);
    }

    @Override
    public SpRegionBean CIDCRP12() {
        return BeanCopyUtil.copy(getRegionByName(CIDCRP12), SpRegionBean.class);
    }

    @Override
    public boolean isEdgeSite(SpRegionEntity region) {
        return getRegionMap().values().stream()
                .filter(rg -> rg.equals(region)).count() > 0;
    }
}
