package io.aicloudware.portal.api_vcpp.controller.bill;

import io.aicloudware.portal.api_vcpp.entity.UpServiceUsageRecord;
import io.aicloudware.portal.api_vcpp.service.bill.IUpServiceUsageRecordService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpServiceUsageRecordBean;
import io.aicloudware.portal.framework.sdk.bean.UpServiceUsageRecordResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpServiceUsageRecordSearchBean;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/service_usage_record")
public class UpServiceUsageRecordController extends BaseUpController<UpServiceUsageRecord, UpServiceUsageRecordBean, UpServiceUsageRecordResultBean> {
    @Autowired
    private IUpServiceUsageRecordService upServiceUsageRecordService;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpServiceUsageRecordSearchBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody UpServiceUsageRecordSearchBean searchBean) {
        UpServiceUsageRecord entity = BeanCopyUtil.copy(searchBean.getBean(), UpServiceUsageRecord.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        entity.setIsParent(true);
        if(StringUtils.isNotEmpty(entity.getName())) {
            UpServiceUsageRecordBean fuzzyBean = new UpServiceUsageRecordBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }
//        UpServiceUsageRecordBean[] entityList = upServiceUsageRecordService.query(searchBean);
        UpServiceUsageRecordBean[] entityList = doQuery(searchBean, entity);
        for(UpServiceUsageRecordBean bean : entityList){
            bean.setServicePlanTypeText(bean.getServicePlanType().getTitle());
        }
        ResultListBean<UpServiceUsageRecordBean> result = Utility.newInstance(getResultType());
        fillPageInfo(searchBean, result);
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }

    @RequestMapping(value = "/getChildList/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/getChildList/{id}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpServiceUsageRecordSearchBean.class)})
    @ResponseBody
    public ResponseBean getChildList(@PathVariable("id") Integer id) {
        return ResponseBean.success(upServiceUsageRecordService.getChildList(id));
    }
}
