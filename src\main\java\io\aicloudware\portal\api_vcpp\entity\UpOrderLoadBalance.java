package io.aicloudware.portal.api_vcpp.entity;

import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderLoadBalanceBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_order_load_balance")
@Access(AccessType.FIELD)
//public class UpOrderLoadBalance extends BaseUpEntity<UpOrderLoadBalanceBean> implements IRequestEntity<UpOrderLoadBalanceBean> {
public class UpOrderLoadBalance extends UpOrderProduct<UpOrderLoadBalanceBean> implements IOrderEntity{

	@Column(name = "payment_type")
	@Enumerated(EnumType.STRING)
	private PaymentType paymentType;

	@Column(name = "vpc_id")
	private Integer vpcId;
	
	@JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderLoadBalance")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<UpOrderElasticIp> elasticIpList;
	
	@Column(name = "elastic_ip_id")
	private Integer elasticIpId;
	
	@Column(name = "vip")
	private String vip;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderLoadBalance")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<UpOrderMonitor> monitorList;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderLoadBalance")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<UpOrderBalanceServerRelation> balanceServerRelationList;

	@JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;
	
	@Column(name = "load_balance_config_id")
	private Integer loadBalanceConfigId;
	
	@JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;
	
    @Column(name = "task_sequence")
    private Integer taskSequence;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public List<UpOrderElasticIp> getElasticIpList() {
		return elasticIpList;
	}

	public void setElasticIpList(List<UpOrderElasticIp> elasticIpList) {
		this.elasticIpList = elasticIpList;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public List<UpOrderMonitor> getMonitorList() {
		return monitorList;
	}

	public void setMonitorList(List<UpOrderMonitor> monitorList) {
		this.monitorList = monitorList;
	}

	public List<UpOrderBalanceServerRelation> getBalanceServerRelationList() {
		return balanceServerRelationList;
	}

	public void setBalanceServerRelationList(List<UpOrderBalanceServerRelation> balanceServerRelationList) {
		this.balanceServerRelationList = balanceServerRelationList;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public Integer getElasticIpId() {
		return elasticIpId;
	}

	public void setElasticIpId(Integer elasticIpId) {
		this.elasticIpId = elasticIpId;
	}

	public Integer getLoadBalanceConfigId() {
		return loadBalanceConfigId;
	}

	public void setLoadBalanceConfigId(Integer loadBalanceConfigId) {
		this.loadBalanceConfigId = loadBalanceConfigId;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

    public Integer getTaskSequence() {
        return taskSequence;
    }

    public void setTaskSequence(Integer taskSequence) {
        this.taskSequence = taskSequence;
    }

	public String getVip() {
		return vip;
	}

	public void setVip(String vip) {
		this.vip = vip;
	}
    
    

}
