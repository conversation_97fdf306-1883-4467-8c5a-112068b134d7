package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.sdk.bean.UpUserLdapBean;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "up_user_ldap")
@Access(AccessType.FIELD)
public final class UpUserLdap extends BaseEntity<UpUserLdapBean> {

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "email")
    private String email;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "telephone")
    private String telephone;

    @Column(name = "user_dn", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String userDn;

    @Column(name = "user_ou", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String userOu;

    @JoinColumn(name = "user_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser user;

    public UpUserLdap() {
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getUserDn() {
        return userDn;
    }

    public void setUserDn(String userDn) {
        this.userDn = userDn;
    }

    public String getUserOu() {
        return userOu;
    }

    public void setUserOu(String userOu) {
        this.userOu = userOu;
    }

    public UpUser getUser() {
        return user;
    }

    public void setUser(UpUser user) {
        this.user = user;
    }
}
