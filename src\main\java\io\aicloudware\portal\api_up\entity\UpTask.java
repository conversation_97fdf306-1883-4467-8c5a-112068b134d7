package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpTaskBean;
import io.aicloudware.portal.framework.sdk.bean.UpTaskSearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;
import org.hibernate.criterion.DetachedCriteria;

import javax.persistence.*;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "up_task")
@Access(AccessType.FIELD)
public class UpTask extends BaseUpEntity<UpTaskBean> implements IEnvironmentEntity<UpTaskBean> {

    @JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;
    
    @JoinColumn(name = "parent_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpTask parent;

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpTaskType type;

    @JoinColumn(name = "order_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UpOrder order;

    @Column(name = "order_type")
    @Enumerated(EnumType.STRING)
    private OrderType orderType;
    
    @Column(name = "target_id")
    private Integer targetId;

    @Column(name = "target_ids")
    private String targetIds;
    
    @Column(name = "request_id")
    private String requestId;

    @Column(name = "dependent_task_id")
    private String dependentTaskId;
    
    @Column(name = "request_number")
    private Integer requestNumber;

    @Column(name = "request_status")
    @Enumerated(EnumType.STRING)
    private UpTaskStatus requestStatus;

    @Column(name = "task_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpTaskStatus taskStatus;

    @Column(name = "status_message", length = ApiConstants.STRING_MAX_LENGTH)
    private String statusMessage;
    
    @Column(name = "backup_strategy_id")
    private Integer backupStrategyId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "parent")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<UpTask> childList;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpTaskBean> searchBean, Set<String> aliasSet) {
        UpTaskSearchBean bean = (UpTaskSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getTaskStatusList())) {
            DaoUtil.addInValues(criteria, "taskStatus", Arrays.asList(bean.getTaskStatusList()));
        }
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }


    public SpOrg getSpOrg() {
		return spOrg;
	}


	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}


	public UpTask getParent() {
        return parent;
    }

    public void setParent(UpTask parent) {
        this.parent = parent;
    }

    public UpTaskType getType() {
        return type;
    }

    public void setType(UpTaskType type) {
        this.type = type;
    }

    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public Integer getTargetId() {
        return targetId;
    }

    public void setTargetId(Integer targetId) {
        this.targetId = targetId;
    }

    public String getTargetIds() {
        return targetIds;
    }

    public void setTargetIds(String targetIds) {
        this.targetIds = targetIds;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getRequestNumber() {
        return requestNumber;
    }

    public void setRequestNumber(Integer requestNumber) {
        this.requestNumber = requestNumber;
    }

    public UpTaskStatus getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(UpTaskStatus requestStatus) {
        this.requestStatus = requestStatus;
    }

    public UpTaskStatus getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(UpTaskStatus taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    public String getDependentTaskId() {
        return dependentTaskId;
    }

    public void setDependentTaskId(String dependentTaskId) {
        this.dependentTaskId = dependentTaskId;
    }

    public Integer getBackupStrategyId() {
        return backupStrategyId;
    }


    public void setBackupStrategyId(Integer backupStrategyId) {
        this.backupStrategyId = backupStrategyId;
    }


    public List<UpTask> getChildList() {
        return childList;
    }

    public void setChildList(List<UpTask> childList) {
        this.childList = childList;
    }
}
