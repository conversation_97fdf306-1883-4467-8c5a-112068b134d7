package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.bean.UpServicePlanBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "VdiPool")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderVdiPoolBean.class})
public class UpOrderVdiPoolBean extends RecordBean {

	@ApiModelProperty(value = "配置")
	private UpServicePlanBean servicePlan;

	private Integer imageId;

	@ApiModelProperty(value = "专有网络")
	private Integer vpcId;
	
	@ApiModelProperty(value = "子网")
	private Integer networkId;

	public UpServicePlanBean getServicePlan() {
		return servicePlan;
	}

	public void setServicePlan(UpServicePlanBean servicePlan) {
		this.servicePlan = servicePlan;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Integer networkId) {
		this.networkId = networkId;
	}

	public Integer getImageId() {
		return imageId;
	}

	public void setImageId(Integer imageId) {
		this.imageId = imageId;
	}
}
