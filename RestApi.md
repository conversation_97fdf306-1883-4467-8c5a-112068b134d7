# API接口文档

本文档整理了io.aicloudware.portal.api_vcpp.controller.order包下的所有Controller的API接口说明。

## 目录

- [1. UpOrderController - 订单管理](#1-upordercontroller---订单管理)
- [2. UpOrderCloudServerController - 云服务器订单](#2-upordercloudservercontroller---云服务器订单)
- [3. UpOrderElasticIpController - 弹性公网IP订单](#3-uporderelasticipcontroller---弹性公网ip订单)
- [4. UpOrderRdsController - 云数据库RDS订单](#4-uporderrdscontroller---云数据库rds订单)
- [5. UpOrderRedisController - 云缓存Redis订单](#5-uporderrediscontroller---云缓存redis订单)
- [6. UpOrderFileStorageController - 文件存储订单](#6-uporderfilestoragecontroller---文件存储订单)
- [7. UpOrderCloudDiskController - 云硬盘订单](#7-upordercloudDiskcontroller---云硬盘订单)
- [8. UpOrderK8sClusterController - K8s集群订单](#8-uporderk8sclustercontroller---k8s集群订单)
- [9. UpOrderElasticSearchController - 云日志服务器订单](#9-uporderelasticsearchcontroller---云日志服务器订单)
- [10. UpOrderLoadBalanceController - 负载均衡订单](#10-uporderloadbalancecontroller---负载均衡订单)
- [11. SpVPCController - VPC管理](#11-spvpccontroller---vpc管理)
- [12. SpVmController - 虚拟机管理](#12-spvmcontroller---虚拟机管理)
- [13. SpRdsController - 云数据库RDS管理](#13-sprdscontroller---云数据库rds管理)
- [14. SpRedisController - 云缓存Redis管理](#14-sprediscontroller---云缓存redis管理)

## 1. UpOrderController - 订单管理

### 1.1 获取订单配置信息

- **URL**: `/order/init`
- **请求方式**: GET
- **请求参数**: 无
- **返回值**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "orderStatus": {
        "pending_deploy": "待部署",
        "deploying": "部署中",
        "deploy_failed": "部署失败",
        "deployed": "已部署",
        "pending_delete": "待删除",
        "deleting": "删除中",
        "delete_failed": "删除失败",
        "deleted": "已删除"
      },
      "orderType": {
        "SERVER": "云服务器",
        "STORAGE": "云硬盘",
        "ELASTIC_IP": "弹性公网IP",
        "LOAD_BALANCER": "负载均衡",
        "CLOUD_MYSQL": "云数据库RDS",
        "CLOUD_REDIS": "云缓存Redis",
        "FILE_STORAGE": "文件存储",
        "ELASTIC_SEARCH": "云日志服务器",
        "K8S_CLUSTER": "K8s集群"
      },
      "taskStatus": {
        "start": "开始",
        "running": "运行中",
        "success": "成功",
        "error": "失败",
        "cancel": "取消"
      }
    }
  }
  ```

### 1.2 查询订单列表

- **URL**: `/order/query`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "orderName1": "updateTm", // 排序字段，非必填，默认为updateTm
    "orderBy1": false,        // 排序方式，非必填，默认为false（降序）
    "bean": {                 // 查询条件，非必填
      "name": "订单名称",      // 订单名称，非必填
      "orderStatus": "订单状态", // 订单状态，非必填
      "type": "订单类型"       // 订单类型，非必填
    }
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "orderName1": "updateTm",
    "orderBy1": false,
    "bean": {
      "name": "测试订单",
      "orderStatus": "deployed",
      "type": "SERVER"
    }
  }
  ```
- **返回值**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "dataList": [
        {
          "id": 1,
          "name": "订单名称",
          "type": "订单类型",
          "orderStatus": "订单状态",
          "createTm": "创建时间",
          "updateTm": "更新时间"
        }
      ],
      "totalCount": 1
    }
  }
  ```

### 1.3 重新部署订单

- **URL**: `/order/redeploy/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: 订单ID，必填
- **返回值**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": true
  }
  ```

### 1.4 更新订单

- **URL**: `/order/update`
- **请求方式**: PUT
- **请求参数**:
  ```json
  {
    "id": 1,           // 订单ID，必填
    "name": "订单名称", // 订单名称，非必填
    "type": "订单类型", // 订单类型，非必填
    "orderStatus": "订单状态" // 订单状态，非必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "id": 1001,
    "name": "更新后的订单名称",
    "type": "SERVER",
    "orderStatus": "deployed"
  }
  ```
- **返回值**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": true
  }
  ```

## 2. UpOrderCloudServerController - 云服务器订单

### 2.1 获取云服务器订单初始化信息

- **URL**: `/order/cloudServer/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回云服务器配置相关信息，包括付费方式、镜像、网络、密钥对等

### 2.2 保存云服务器订单（管理员）

- **URL**: `/order/cloudServer/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",        // 必填
    "vmName": "虚拟机名称",    // 必填
    "imageId": "镜像ID",      // 必填
    "vmSetId": "虚拟机配置ID", // 必填
    "diskSize": 40,          // 系统盘大小，必填
    "diskType": "磁盘类型",   // 必填
    "vpcId": "网络ID",        // 必填
    "keyType": "密钥类型",     // 必填
    "password": "密码",       // 当keyType为PASSWORD时必填
    "keyPairId": "密钥对ID"   // 当keyType为KEY_PAIR时必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "测试云服务器订单",
    "vmName": "test-vm-001",
    "imageId": "img-12345678",
    "vmSetId": "vm-set-001",
    "diskSize": 100,
    "diskType": "SSD",
    "vpcId": "vpc-12345678",
    "keyType": "PASSWORD",
    "password": "Test@123456"
  }
  ```
- **返回值**: 返回订单ID

### 2.3 保存云服务器订单（用户）

- **URL**: `/order/cloudServer/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",           // 必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 必填
    "vmName": "虚拟机名称",       // 必填
    "imageId": "镜像ID",         // 必填
    "vmSetId": "虚拟机配置ID",    // 必填
    "diskSize": 40,             // 系统盘大小，必填
    "diskType": "磁盘类型",      // 必填
    "vpcId": "网络ID",           // 必填
    "keyType": "密钥类型",        // 必填
    "password": "密码",          // 当keyType为PASSWORD时必填
    "keyPairId": "密钥对ID"      // 当keyType为KEY_PAIR时必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户云服务器订单",
    "vmName": "user-vm-001",
    "imageId": "img-12345678",
    "vmSetId": "vm-set-001",
    "diskSize": 80,
    "diskType": "SSD",
    "vpcId": "vpc-12345678",
    "keyType": "KEY_PAIR",
    "keyPairId": "key-12345678"
  }
  ```
- **返回值**: 返回订单ID

### 2.4 更新云服务器订单

- **URL**: `/order/cloudServer/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "id": 1,                    // 订单ID，必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 非必填
    "vmName": "虚拟机名称"        // 非必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "id": 1001,
    "quotaDetailId": "quota-detail-002",
    "name": "更新后的云服务器订单",
    "vmName": "updated-vm-001"
  }
  ```
- **返回值**: 返回更新结果

## 3. UpOrderElasticIpController - 弹性公网IP订单

### 3.1 获取弹性公网IP订单初始化信息

- **URL**: `/order/elasticIp/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回弹性公网IP配置相关信息，包括带宽配置等

### 3.2 获取可用IP列表

- **URL**: `/order/elasticIp/availableips`
- **请求方式**: GET
- **请求参数**: 无
- **返回值**: 返回可用IP列表

### 3.3 保存弹性公网IP订单

- **URL**: `/order/elasticIp/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",           // 必填
    "bandwidthId": "带宽配置ID",  // 必填
    "resourceType": "资源类型",   // 必填
    "resourceId": "资源ID"       // 当绑定资源时必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "弹性公网IP订单",
    "bandwidthId": "bw-12345678",
    "resourceType": "VM",
    "resourceId": "vm-12345678"
  }
  ```
- **返回值**: 返回订单ID

### 3.4 保存弹性公网IP订单（用户）

- **URL**: `/order/elasticIp/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",           // 必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 必填
    "bandwidthId": "带宽配置ID",  // 必填
    "resourceType": "资源类型",   // 必填
    "resourceId": "资源ID"       // 当绑定资源时必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户弹性公网IP订单",
    "bandwidthId": "bw-12345678",
    "resourceType": "VM",
    "resourceId": "vm-12345678"
  }
  ```
- **返回值**: 返回订单ID

## 4. UpOrderRdsController - 云数据库RDS订单

### 4.1 获取云数据库RDS订单初始化信息

- **URL**: `/order/rds/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回云数据库RDS配置相关信息，包括磁盘类型、服务器配置、镜像、网络等

### 4.2 保存云数据库RDS订单（管理员）

- **URL**: `/order/rds/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",        // 必填
    "vmName": "实例名称",      // 必填
    "imageId": "镜像ID",      // 必填
    "vmSetId": "实例配置ID",   // 必填
    "diskSize": 40,          // 磁盘大小，必填
    "diskType": "磁盘类型",   // 必填
    "vpcId": "网络ID"         // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "RDS订单",
    "vmName": "rds-mysql-001",
    "imageId": "img-mysql-5.7",
    "vmSetId": "rds-set-001",
    "diskSize": 100,
    "diskType": "SSD",
    "vpcId": "vpc-12345678"
  }
  ```
- **返回值**: 返回订单ID

### 4.3 保存云数据库RDS订单（用户）

- **URL**: `/order/rds/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",           // 必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 必填
    "vmName": "实例名称",         // 必填
    "imageId": "镜像ID",         // 必填
    "vmSetId": "实例配置ID",      // 必填
    "diskSize": 40,             // 磁盘大小，必填
    "diskType": "磁盘类型",      // 必填
    "vpcId": "网络ID"            // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户RDS订单",
    "vmName": "user-rds-001",
    "imageId": "img-mysql-5.7",
    "vmSetId": "rds-set-001",
    "diskSize": 80,
    "diskType": "SSD",
    "vpcId": "vpc-12345678"
  }
  ```
- **返回值**: 返回订单ID

### 4.4 更新云数据库RDS订单

- **URL**: `/order/rds/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "id": 1,                    // 订单ID，必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 非必填
    "vmName": "实例名称"          // 非必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "id": 1001,
    "quotaDetailId": "quota-detail-002",
    "name": "更新后的RDS订单",
    "vmName": "updated-rds-001"
  }
  ```
- **返回值**: 返回更新结果

## 5. UpOrderRedisController - 云缓存Redis订单

### 5.1 获取云缓存Redis订单初始化信息

- **URL**: `/order/redis/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回云缓存Redis配置相关信息，包括付费方式、Redis类型、节点类型等

### 5.2 保存云缓存Redis订单（管理员）

- **URL**: `/order/redis/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",        // 必填
    "vmName": "实例名称",      // 必填
    "redisType": "Redis类型", // 必填
    "vmSetId": "实例配置ID",   // 必填
    "vpcId": "网络ID"         // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "Redis订单",
    "vmName": "redis-001",
    "redisType": "STANDALONE",
    "vmSetId": "redis-set-001",
    "vpcId": "vpc-12345678"
  }
  ```
- **返回值**: 返回订单ID

### 5.3 保存云缓存Redis订单（用户）

- **URL**: `/order/redis/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",           // 必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 必填
    "vmName": "实例名称",         // 必填
    "redisType": "Redis类型",     // 必填
    "vmSetId": "实例配置ID",      // 必填
    "vpcId": "网络ID"            // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户Redis订单",
    "vmName": "user-redis-001",
    "redisType": "CLUSTER",
    "vmSetId": "redis-set-001",
    "vpcId": "vpc-12345678"
  }
  ```
- **返回值**: 返回订单ID

### 5.4 更新云缓存Redis订单

- **URL**: `/order/redis/update`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "id": 1,                    // 订单ID，必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 非必填
    "vmName": "实例名称"          // 非必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "id": 1001,
    "quotaDetailId": "quota-detail-002",
    "name": "更新后的Redis订单",
    "vmName": "updated-redis-001"
  }
  ```
- **返回值**: 返回更新结果

## 6. UpOrderFileStorageController - 文件存储订单

### 6.1 获取文件存储订单初始化信息

- **URL**: `/order/fileStorage/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回文件存储配置相关信息，包括磁盘配置等

### 6.2 保存文件存储订单（管理员）

- **URL**: `/order/fileStorage/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",        // 必填
    "diskSetId": "磁盘配置ID", // 必填
    "size": 100              // 存储大小，必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "文件存储订单",
    "diskSetId": "disk-set-001",
    "size": 500
  }
  ```
- **返回值**: 返回订单ID

### 6.3 保存文件存储订单（用户）

- **URL**: `/order/fileStorage/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",           // 必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 必填
    "diskSetId": "磁盘配置ID",    // 必填
    "size": 100                  // 存储大小，必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户文件存储订单",
    "diskSetId": "disk-set-001",
    "size": 300
  }
  ```
- **返回值**: 返回订单ID

### 6.4 更新文件存储订单

- **URL**: `/order/fileStorage/change`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "id": 1,                // 订单ID，必填
    "name": "订单名称",      // 非必填
    "size": 200            // 新的存储大小，必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "id": 1001,
    "name": "更新后的文件存储订单",
    "size": 800
  }
  ```
- **返回值**: 返回更新结果

## 7. UpOrderCloudDiskController - 云硬盘订单

### 7.1 获取云硬盘订单初始化信息

- **URL**: `/order/cloudDisk/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回云硬盘配置相关信息，包括磁盘类型、计费方式等

### 7.2 保存云硬盘订单（管理员）

- **URL**: `/order/cloudDisk/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",        // 必填
    "diskName": "磁盘名称",    // 必填
    "diskType": "磁盘类型",    // 必填
    "diskSize": 100,          // 磁盘大小，必填
    "chargeType": "计费方式"   // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "云硬盘订单",
    "diskName": "data-disk-001",
    "diskType": "SSD",
    "diskSize": 200,
    "chargeType": "PREPAID"
  }
  ```
- **返回值**: 返回订单ID

### 7.3 保存云硬盘订单（用户）

- **URL**: `/order/cloudDisk/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",           // 必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 必填
    "diskName": "磁盘名称",       // 必填
    "diskType": "磁盘类型",       // 必填
    "diskSize": 100,             // 磁盘大小，必填
    "chargeType": "计费方式"      // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户云硬盘订单",
    "diskName": "user-disk-001",
    "diskType": "SSD",
    "diskSize": 150,
    "chargeType": "POSTPAID"
  }
  ```
- **返回值**: 返回订单ID

## 8. UpOrderK8sClusterController - K8s集群订单

### 8.1 获取K8s集群订单初始化信息

- **URL**: `/order/k8scluster/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回K8s集群配置相关信息，包括网络、密钥对等

### 8.2 保存K8s集群订单（用户）

- **URL**: `/order/k8scluster/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",           // 必填
    "clusterName": "集群名称",    // 必填
    "vpcId": "网络ID",           // 必填
    "keyType": "密钥类型",        // 必填
    "password": "密码",          // 当keyType为PASSWORD时必填
    "keyPairId": "密钥对ID",     // 当keyType为KEY_PAIR时必填
    "nodePools": [              // 节点池配置，必填
      {
        "name": "节点池名称",     // 必填
        "vmSetId": "虚拟机配置ID", // 必填
        "count": 3               // 节点数量，必填
      }
    ]
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "K8s集群订单",
    "clusterName": "k8s-cluster-001",
    "vpcId": "vpc-12345678",
    "keyType": "PASSWORD",
    "password": "K8s@123456",
    "nodePools": [
      {
        "name": "master-pool",
        "vmSetId": "vm-set-001",
        "count": 3
      },
      {
        "name": "worker-pool",
        "vmSetId": "vm-set-002",
        "count": 5
      }
    ]
  }
  ```
- **返回值**: 返回订单ID

## 9. UpOrderElasticSearchController - 云日志服务器订单

### 9.1 获取云日志服务器订单初始化信息

- **URL**: `/order/esk/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回云日志服务器配置相关信息

### 9.2 保存云日志服务器订单（管理员）

- **URL**: `/order/esk/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",        // 必填
    "vmName": "实例名称",      // 必填
    "vmSetId": "实例配置ID",   // 必填
    "vpcId": "网络ID"         // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "云日志服务器订单",
    "vmName": "es-001",
    "vmSetId": "es-set-001",
    "vpcId": "vpc-12345678"
  }
  ```
- **返回值**: 返回订单ID

### 9.3 保存云日志服务器订单（用户）

- **URL**: `/order/esk/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",           // 必填
    "quotaDetailId": "配额明细ID", // 必填
    "name": "订单名称",           // 必填
    "vmName": "实例名称",         // 必填
    "vmSetId": "实例配置ID",      // 必填
    "vpcId": "网络ID"            // 必填
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户云日志服务器订单",
    "vmName": "user-es-001",
    "vmSetId": "es-set-001",
    "vpcId": "vpc-12345678"
  }
  ```
- **返回值**: 返回订单ID

## 10. UpOrderLoadBalanceController - 负载均衡订单

### 10.1 获取负载均衡订单初始化信息

- **URL**: `/order/loadBalance/init/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 用户ID，必填
- **返回值**: 返回负载均衡配置相关信息，包括负载均衡算法、传输层协议等

### 10.2 保存负载均衡订单（管理员）

- **URL**: `/order/loadBalance/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "订单名称",                // 必填
    "loadBalanceName": "负载均衡名称", // 必填
    "loadBalanceId": "负载均衡配置ID", // 必填
    "vpcId": "网络ID",                // 必填
    "algorithm": "负载均衡算法",       // 必填
    "transportLayer": "传输层协议",    // 必填
    "interval": 5,                   // 间隔时间，非必填，默认5
    "timeout": 8,                    // 超时时间，非必填，默认8
    "maxNumber": 3                   // 最大重试次数，非必填，默认3
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "负载均衡订单",
    "loadBalanceName": "lb-001",
    "loadBalanceId": "lb-set-001",
    "vpcId": "vpc-12345678",
    "algorithm": "ROUND_ROBIN",
    "transportLayer": "TCP",
    "interval": 10,
    "timeout": 15,
    "maxNumber": 5
  }
  ```
- **返回值**: 返回订单ID

### 10.3 保存负载均衡订单（用户）

- **URL**: `/order/loadBalance/quota/save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "quotaId": "配额ID",              // 必填
    "quotaDetailId": "配额明细ID",    // 必填
    "name": "订单名称",               // 必填
    "loadBalanceName": "负载均衡名称", // 必填
    "loadBalanceId": "负载均衡配置ID", // 必填
    "vpcId": "网络ID",                // 必填
    "algorithm": "负载均衡算法",       // 必填
    "transportLayer": "传输层协议",    // 必填
    "interval": 5,                   // 间隔时间，非必填，默认5
    "timeout": 8,                    // 超时时间，非必填，默认8
    "maxNumber": 3                   // 最大重试次数，非必填，默认3
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "quotaId": "quota-001",
    "quotaDetailId": "quota-detail-001",
    "name": "用户负载均衡订单",
    "loadBalanceName": "user-lb-001",
    "loadBalanceId": "lb-set-001",
    "vpcId": "vpc-12345678",
    "algorithm": "LEAST_CONNECTIONS",
    "transportLayer": "HTTP",
    "interval": 8,
    "timeout": 10,
    "maxNumber": 4
  }
  ```
- **返回值**: 返回订单ID

## 11. SpVPCController - VPC管理

### 11.1 查询VPC列表

- **URL**: `/vpc/query`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "bean": {
      "name": "VPC名称"  // 非必填，用于模糊查询
    }
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "bean": {
      "name": "test-vpc"
    }
  }
  ```
- **返回值**: 返回VPC列表

### 11.2 获取VPC详情

- **URL**: `/vpc/get/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: VPC ID，必填
- **返回值**: 返回VPC详情

### 11.3 获取可用区列表

- **URL**: `/vpc/az`
- **请求方式**: GET
- **请求参数**: 无
- **返回值**: 返回可用区列表

### 11.4 同步VPC

- **URL**: `/vpc/sync`
- **请求方式**: GET
- **请求参数**: 无
- **返回值**: 返回同步结果

### 11.5 添加网络

- **URL**: `/vpc/addNetwork`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "name": "网络名称",           // 必填
    "ovdcNetworkList": [         // 必填
      {
        "name": "子网名称",       // 必填
        "gateway": "网关地址",    // 必填
        "netmask": "子网掩码",    // 必填
        "dns1": "DNS1",          // 非必填
        "dns2": "DNS2"           // 非必填
      }
    ]
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "name": "新建VPC网络",
    "ovdcNetworkList": [
      {
        "name": "subnet-1",
        "gateway": "192.168.1.1",
        "netmask": "255.255.255.0",
        "dns1": "8.8.8.8",
        "dns2": "114.114.114.114"
      },
      {
        "name": "subnet-2",
        "gateway": "192.168.2.1",
        "netmask": "255.255.255.0",
        "dns1": "8.8.8.8"
      }
    ]
  }
  ```
- **返回值**: 返回创建的VPC信息

### 11.6 删除VPC

- **URL**: `/vpc/delete/{id}`
- **请求方式**: DELETE
- **请求参数**:
  - id: VPC ID，必填
- **返回值**: 返回删除结果

## 12. SpVmController - 虚拟机管理

### 12.1 查询虚拟机列表

- **URL**: `/vm/query`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "bean": {
      "name": "虚拟机名称"  // 非必填，用于模糊查询
    }
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "bean": {
      "name": "web-server"
    }
  }
  ```
- **返回值**: 返回虚拟机列表

### 12.2 查询已部署完成的虚拟机列表

- **URL**: `/vm/queryCompleteVm`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "bean": {
      "name": "虚拟机名称"  // 非必填，用于模糊查询
    }
  }
  ```
- **返回值**: 返回已部署完成的虚拟机列表

### 12.3 获取虚拟机详情

- **URL**: `/vm/get/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 虚拟机ID，必填
- **返回值**: 返回虚拟机详情

### 12.4 刷新虚拟机信息

- **URL**: `/vm/refresh/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 虚拟机ID，必填
- **返回值**: 返回刷新后的虚拟机信息

### 12.5 虚拟机开机

- **URL**: `/vm/power_on/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: 虚拟机ID，必填
- **返回值**: 返回操作结果

### 12.6 虚拟机关机

- **URL**: `/vm/power_off/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: 虚拟机ID，必填
- **返回值**: 返回操作结果

### 12.7 同步虚拟机

- **URL**: `/vm/sync`
- **请求方式**: GET
- **请求参数**: 无
- **返回值**: 返回同步结果

### 12.8 获取VNC连接

- **URL**: `/vm/vnc/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: 虚拟机ID，必填
- **返回值**: 返回VNC连接URL

## 13. SpRdsController - 云数据库RDS管理

### 13.1 获取RDS配置信息

- **URL**: `/rds/init`
- **请求方式**: GET
- **请求参数**: 无
- **返回值**: 返回RDS配置信息，包括RDS类型等

### 13.2 查询RDS实例列表

- **URL**: `/rds/query`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "bean": {
      "name": "RDS实例名称"  // 非必填，用于模糊查询
    }
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "bean": {
      "name": "mysql-prod"
    }
  }
  ```
- **返回值**: 返回RDS实例列表

### 13.3 获取RDS实例详情

- **URL**: `/rds/get/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: RDS实例ID，必填
- **返回值**: 返回RDS实例详情

### 13.4 RDS实例开机

- **URL**: `/rds/on/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: RDS实例ID，必填
- **返回值**: 返回操作结果

### 13.5 RDS实例关机

- **URL**: `/rds/off/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: RDS实例ID，必填
- **返回值**: 返回操作结果

### 13.6 RDS实例重启

- **URL**: `/rds/reboot/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: RDS实例ID，必填
- **返回值**: 返回操作结果

### 13.7 删除RDS实例

- **URL**: `/rds/delete/{id}`
- **请求方式**: DELETE
- **请求参数**:
  - id: RDS实例ID，必填
- **返回值**: 返回删除结果

## 14. SpRedisController - 云缓存Redis管理

### 14.1 获取Redis配置信息

- **URL**: `/redis/init`
- **请求方式**: GET
- **请求参数**: 无
- **返回值**: 返回Redis配置信息，包括Redis节点类型、Redis类型等

### 14.2 查询Redis实例列表

- **URL**: `/redis/query`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "bean": {
      "name": "Redis实例名称"  // 非必填，用于模糊查询
    }
  }
  ```
- **入参JSON示例**:
  ```json
  {
    "bean": {
      "name": "redis-cache"
    }
  }
  ```
- **返回值**: 返回Redis实例列表

### 14.3 获取Redis实例详情

- **URL**: `/redis/get/{id}`
- **请求方式**: GET
- **请求参数**:
  - id: Redis实例ID，必填
- **返回值**: 返回Redis实例详情

### 14.4 Redis实例开机

- **URL**: `/redis/on/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: Redis实例ID，必填
- **返回值**: 返回操作结果

### 14.5 Redis实例关机

- **URL**: `/redis/off/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: Redis实例ID，必填
- **返回值**: 返回操作结果

### 14.6 Redis实例重启

- **URL**: `/redis/reboot/{id}`
- **请求方式**: POST
- **请求参数**:
  - id: Redis实例ID，必填
- **返回值**: 返回操作结果

### 14.7 删除Redis实例

- **URL**: `/redis/delete/{id}`
- **请求方式**: DELETE
- **请求参数**:
  - id: Redis实例ID，必填
- **返回值**: 返回删除结果