package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderMonitorBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.BalanceArithmetic;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.TransportLayer;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_order_monitor")
@Access(AccessType.FIELD)
public class UpOrderMonitor extends BaseUpEntity<UpOrderMonitorBean> {

	@Column(name = "front_layer")
	@Enumerated(EnumType.STRING)
	private TransportLayer frontLayer;
	
	@Column(name = "front_port")
	private String frontPort;
	
	@Column(name = "balance_arithmetic")
	@Enumerated(EnumType.STRING)
	private BalanceArithmetic balanceArithmetic;
	
	@Column(name = "backend_layer")
	@Enumerated(EnumType.STRING)
	private TransportLayer backendLayer;
	
	@Column(name = "backend_port")
	private String backendPort;
	
	@JoinColumn(name = "order_loadbalance_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderLoadBalance orderLoadBalance;

	@Column(name = "interval")
	private Integer interval;
	
	@Column(name = "timeout")
	private Integer timeout;
	
	@Column(name = "max_number")
	private Integer maxNumber;
	
	@JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

	@JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpOrg org;
	
	public TransportLayer getFrontLayer() {
		return frontLayer;
	}

	public void setFrontLayer(TransportLayer frontLayer) {
		this.frontLayer = frontLayer;
	}

	public String getFrontPort() {
		return frontPort;
	}

	public void setFrontPort(String frontPort) {
		this.frontPort = frontPort;
	}

	public BalanceArithmetic getBalanceArithmetic() {
		return balanceArithmetic;
	}

	public void setBalanceArithmetic(BalanceArithmetic balanceArithmetic) {
		this.balanceArithmetic = balanceArithmetic;
	}

	public TransportLayer getBackendLayer() {
		return backendLayer;
	}

	public void setBackendLayer(TransportLayer backendLayer) {
		this.backendLayer = backendLayer;
	}

	public String getBackendPort() {
		return backendPort;
	}

	public void setBackendPort(String backendPort) {
		this.backendPort = backendPort;
	}

	public UpOrderLoadBalance getOrderLoadBalance() {
		return orderLoadBalance;
	}

	public void setOrderLoadBalance(UpOrderLoadBalance orderLoadBalance) {
		this.orderLoadBalance = orderLoadBalance;
	}

	public Integer getInterval() {
		return interval;
	}

	public void setInterval(Integer interval) {
		this.interval = interval;
	}

	public Integer getTimeout() {
		return timeout;
	}

	public void setTimeout(Integer timeout) {
		this.timeout = timeout;
	}

	public Integer getMaxNumber() {
		return maxNumber;
	}

	public void setMaxNumber(Integer maxNumber) {
		this.maxNumber = maxNumber;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public SpOrg getOrg() {
		return org;
	}

	public void setOrg(SpOrg org) {
		this.org = org;
	}
	
}
