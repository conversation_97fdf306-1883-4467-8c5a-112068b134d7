package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.framework.sdk.bean.SpMetricsBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderLoadBalanceBean;
import io.aicloudware.portal.platform_vcd.entity.SpLoadBalancer;
import io.aicloudware.portal.platform_vcd.entity.SpLoadBalancerPool;

import java.util.ArrayList;

public interface IRestLoadBalancerService {

	public Integer save(UpOrderLoadBalanceBean bean);
	public void updateLBPool(SpLoadBalancerPool pool);
	public void updateLBPoolAndVs(SpLoadBalancer loadBalancer);
	ArrayList<SpMetricsBean> queryLBHistoryMetrics(Integer id, float f);
}
