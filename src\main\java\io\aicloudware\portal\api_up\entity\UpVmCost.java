package io.aicloudware.portal.api_up.entity;


import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.common.BeanFactory;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IDataScopeEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostBean;
import io.aicloudware.portal.framework.sdk.bean.UpVmCostSearchBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.utility.FormatUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOVDC;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Set;

@Entity
@Table(name = "up_vm_cost")
@Access(AccessType.FIELD)
public class UpVmCost extends BaseUpEntity<UpVmCostBean> implements IDataScopeEntity<UpVmCostBean>, IEnvironmentEntity<UpVmCostBean> {

    @JoinColumn(name = "reservation_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOVDC reservation;

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "sp_vm_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;

    @Column(name = "date", nullable = false)
    private Integer date;

    @Column(name = "fiscal_year", nullable = false)
    private Integer fiscalYear;

    @Column(name = "cpu_num", nullable = false)
    private Integer cpuNum;

    @Column(name = "memory_gb", nullable = false)
    private Integer memoryGB;

    @Column(name = "disk_gb", nullable = false)
    private Integer diskGB;

    @Column(name = "cpu_price", nullable = false)
    private Double cpuPrice;

    @Column(name = "memory_price", nullable = false)
    private Double memoryPrice;

    @Column(name = "disk_price", nullable = false)
    private Double diskPrice;

    @Column(name = "cpu_money", nullable = false)
    private Double cpuMoney;

    @Column(name = "memory_money", nullable = false)
    private Double memoryMoney;

    @Column(name = "disk_money", nullable = false)
    private Double diskMoney;

    @Column(name = "total_money", nullable = false)
    private Double totalMoney;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpVmCostBean> searchBean, Set<String> aliasSet) {
        UpVmCostSearchBean bean = (UpVmCostSearchBean) searchBean;
        if (Utility.isNotEmpty(bean.getStartDt())) {
            criteria.add(Restrictions.ge("date", FormatUtil.formatDate(bean.getStartDt())));
        }
        if (Utility.isNotEmpty(bean.getEndDt())) {
            criteria.add(Restrictions.le("date", FormatUtil.formatDate(bean.getEndDt())));
        }
        DaoUtil.addDataScope(searchBean.getMainMenuType(), criteria, aliasSet);
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }

    public Integer getDate() {
        return date;
    }

    public void setDate(Integer date) {
        this.date = date;
    }

    public Integer getFiscalYear() {
        return fiscalYear;
    }

    public void setFiscalYear(Integer fiscalYear) {
        this.fiscalYear = fiscalYear;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Integer getMemoryGB() {
        return memoryGB;
    }

    public void setMemoryGB(Integer memoryGB) {
        this.memoryGB = memoryGB;
    }

    public Integer getDiskGB() {
        return diskGB;
    }

    public void setDiskGB(Integer diskGB) {
        this.diskGB = diskGB;
    }

    public Double getCpuPrice() {
        return cpuPrice;
    }

    public void setCpuPrice(Double cpuPrice) {
        this.cpuPrice = cpuPrice;
    }

    public Double getMemoryPrice() {
        return memoryPrice;
    }

    public void setMemoryPrice(Double memoryPrice) {
        this.memoryPrice = memoryPrice;
    }

    public Double getDiskPrice() {
        return diskPrice;
    }

    public void setDiskPrice(Double diskPrice) {
        this.diskPrice = diskPrice;
    }

    public Double getCpuMoney() {
        return cpuMoney;
    }

    public void setCpuMoney(Double cpuMoney) {
        this.cpuMoney = cpuMoney;
    }

    public Double getMemoryMoney() {
        return memoryMoney;
    }

    public void setMemoryMoney(Double memoryMoney) {
        this.memoryMoney = memoryMoney;
    }

    public Double getDiskMoney() {
        return diskMoney;
    }

    public void setDiskMoney(Double diskMoney) {
        this.diskMoney = diskMoney;
    }

    public Double getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(Double totalMoney) {
        this.totalMoney = totalMoney;
    }

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpOVDC getReservation() {
        return reservation;
    }

    public void setReservation(SpOVDC reservation) {
        this.reservation = reservation;
    }

    public SpVm getVm() {
        return vm;
    }

    public void setVm(SpVm vm) {
        this.vm = vm;
        if (vm != null && Utility.isNotZero(vm.getId())) {
            vm = BeanFactory.getCloudDao().load(SpVm.class, vm.getId());
        }
    }

}
