package io.aicloudware.portal.api_vcpp.entity;

import java.util.Date;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.profile.UpMessageBean;
import io.aicloudware.portal.framework.sdk.contants.UpMessageStatus;
import io.aicloudware.portal.framework.sdk.contants.UpMessageType;

@Entity
@Table(name = "up_message")
@Access(AccessType.FIELD)
public final class UpMessage extends BaseUpEntity<UpMessageBean> {

	@JoinColumn(name = "user_id")
	@ManyToOne(fetch = FetchType.EAGER)
	private UpUser user;

	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private UpMessageType type;
	
	@Column(name = "message_status", nullable = false)
	@Enumerated(EnumType.STRING)
	private UpMessageStatus messageStatus;
	
	@Column(name = "content")
	private String content;
	
	@Column(name = "message_tm")
	private Date messageTm;

	public UpUser getUser() {
		return user;
	}

	public void setUser(UpUser user) {
		this.user = user;
	}

	public UpMessageType getType() {
		return type;
	}

	public void setType(UpMessageType type) {
		this.type = type;
	}

	public UpMessageStatus getMessageStatus() {
		return messageStatus;
	}

	public void setMessageStatus(UpMessageStatus messageStatus) {
		this.messageStatus = messageStatus;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getMessageTm() {
		return messageTm;
	}

	public void setMessageTm(Date messageTm) {
		this.messageTm = messageTm;
	}

}
