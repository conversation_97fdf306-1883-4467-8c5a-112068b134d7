package io.aicloudware.portal.api_vcpp.controller.finance;

import io.aicloudware.portal.api_vcpp.controller.BaseController;

/**
 * 充值
 * <AUTHOR>
//@Controller
//@RequestMapping("/finance")
public class UpFinanceRechargeController extends BaseController {
	
//	@Autowired
//	private IUpFinanceRechargeService financeRechargeService;
//
//	/**
//	 * 配置
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/recharge/init")
//    @ResponseBody
//	public ResponseBean init(HttpServletRequest request) {
//
//		Map<String,Object> datas = new HashMap<>();
//
//		// 付费方式
//		List<Map<String, String>> channels = new ArrayList<>();
//        for (PayChannel type : PayChannel.values()) {
//        	Map<String, String> item = new LinkedHashMap<>();
//        	item.put("name", type.toString());
//        	item.put("title", type.getTitle());
//        	channels.add(item);
//        }
//
//        List<Map<String, String>> financeProductTypes = new ArrayList<>();
//        for (UpFinanceProductType type : UpFinanceProductType.values()) {
//        	Map<String, String> item = new LinkedHashMap<>();
//        	item.put("name", type.toString());
//        	item.put("title", type.getTitle());
//        	financeProductTypes.add(item);
//        }
//
//        datas.put("pay_channel", channels);
//        datas.put("financeProductTypes", financeProductTypes);
//		return ResponseBean.success(datas);
//	}
//
//	/**
//	 * 生成支付订单
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/recharge/order", method = RequestMethod.POST)
//    @ResponseBody
//	public ResponseBean order(@RequestBody UpFinanceRechargeOrderBean bean, HttpServletRequest request) {
//		return ResponseBean.success(financeRechargeService.order(ThreadCache.getUserId(), bean));
//	}
//
//	/**
//	 * 校验订单状态
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/recharge/check", method = RequestMethod.POST)
//    @ResponseBody
//	public ResponseBean check(@RequestBody UpFinanceRechargeOrderBean bean, HttpServletRequest request) {
//		return ResponseBean.success(financeRechargeService.check(ThreadCache.getUserId(), bean));
//	}
//
//	/**
//	 * 查询余额
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/balance", method = RequestMethod.GET)
//    @ResponseBody
//	public ResponseBean balance(HttpServletRequest request) {
//		return ResponseBean.success(financeRechargeService.balance(ThreadCache.getUserId()));
//	}
//
//	/**
//	 * 查询余额
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/checkBalance", method = RequestMethod.GET)
//    @ResponseBody
//	public ResponseBean checkBalance(HttpServletRequest request) {
//		return ResponseBean.success(financeRechargeService.checkBalance(ThreadCache.getUserId()));
//	}
//
//	/**
//	 * 查询充值记录
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/recharge/list", method = RequestMethod.POST)
//    @ResponseBody
//	public ResponseBean list(@RequestBody UpFinanceBillSearchBean bean, HttpServletRequest request) {
//		return ResponseBean.success(financeRechargeService.list(ThreadCache.getUserId(),bean));
//	}
	
}
