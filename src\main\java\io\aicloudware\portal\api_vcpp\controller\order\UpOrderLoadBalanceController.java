package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderLoadBalanceService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderLoadBalanceBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.BalanceArithmetic;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.BandwidthCost;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ElasticIpChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.TransportLayer;
import io.aicloudware.portal.framework.utility.AssertUtil;

import io.swagger.annotations.ApiParam;

/**
 * 负载均衡
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/loadBalance")
public class UpOrderLoadBalanceController extends BaseController {

	@Autowired
	private IUpOrderLoadBalanceService loadBalanceService;
	
	@Autowired
	private IUpProductService productService;
	
	@Autowired
	private IUpOrderService orderService;
	
	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 弹性公网IP-计费方式
        Map<String, String> elasticIpChargeType = new LinkedHashMap<String, String>();
        for (ElasticIpChargeType type : ElasticIpChargeType.values()) {
        	elasticIpChargeType.put(type.toString(), type.getTitle());
        }
        datas.put("elasticIpChargeType", elasticIpChargeType);
        
        // 弹性公网IP-带宽峰值
        Map<String, String> bandwidthCost = new LinkedHashMap<String, String>();
        for (BandwidthCost type : BandwidthCost.values()) {
        	bandwidthCost.put(type.toString(), type.getTitle());
        }
        datas.put("bandwidthCost", bandwidthCost);
        
        // 弹性公网IP
        datas.put("elasticIps", orderService.queryUnbindLBVip(id));
        
        // 弹性公网IP-带宽峰值
        Map<String, String> balanceArithmetic = new LinkedHashMap<String, String>();
        for (BalanceArithmetic type : BalanceArithmetic.values()) {
        	balanceArithmetic.put(type.toString(), type.getTitle());
        }
        datas.put("balanceArithmetic", balanceArithmetic);
        
        // VPC
        datas.put("vpc", orderService.getVPC(id));
        
        // 云服务器
        datas.put("cloudServers", orderService.queryUnbindSpVm(id));
        
        // 传输层协议
        Map<String, String> transportLayer = new LinkedHashMap<String, String>();
        for (TransportLayer type : TransportLayer.values()) {
        	transportLayer.put(type.toString(), type.getTitle());
        }
        datas.put("transportLayer", transportLayer);
        
        // 间隔时间
        datas.put("interval", 5);
        
        // 超时时间
        datas.put("timeout", 8);
        
        // 最大重试次数
        datas.put("maxNumber", 3);
        
        // 配置
//        datas.put("bandwidthConfig", productService.getBandwidthSetByOrg(id));
        datas.put("loadBalanceConfig", productService.getLoadBalanceSetByOrg(id));
        
		return ResponseBean.success(datas);
	}
	
	@RequestMapping(value = "/quota/init/{code}")
    @ResponseBody
	public ResponseBean quotaInit(@ApiParam(value = "对象code") @PathVariable String code) {
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 弹性公网IP-计费方式
        Map<String, String> elasticIpChargeType = new LinkedHashMap<String, String>();
        for (ElasticIpChargeType type : ElasticIpChargeType.values()) {
        	elasticIpChargeType.put(type.toString(), type.getTitle());
        }
        datas.put("elasticIpChargeType", elasticIpChargeType);
        
        // 弹性公网IP-带宽峰值
        Map<String, String> bandwidthCost = new LinkedHashMap<String, String>();
        for (BandwidthCost type : BandwidthCost.values()) {
        	bandwidthCost.put(type.toString(), type.getTitle());
        }
        datas.put("bandwidthCost", bandwidthCost);
        
        // 弹性公网IP
        datas.put("elasticIps", orderService.queryUnbindLBVip(user.getId()));
        
        // 弹性公网IP-带宽峰值
        Map<String, String> balanceArithmetic = new LinkedHashMap<String, String>();
        for (BalanceArithmetic type : BalanceArithmetic.values()) {
        	balanceArithmetic.put(type.toString(), type.getTitle());
        }
        datas.put("balanceArithmetic", balanceArithmetic);
        
        // VPC
        datas.put("vpc", orderService.getVPC(user.getId()));
        
        // 云服务器
        datas.put("cloudServers", orderService.queryUnbindSpVm(user.getId()));
        
        // 传输层协议
        Map<String, String> transportLayer = new LinkedHashMap<String, String>();
        for (TransportLayer type : TransportLayer.values()) {
        	transportLayer.put(type.toString(), type.getTitle());
        }
        datas.put("transportLayer", transportLayer);
        
        // 间隔时间
        datas.put("interval", 5);
        
        // 超时时间
        datas.put("timeout", 8);
        
        // 最大重试次数
        datas.put("maxNumber", 3);
        
        // 配置
//        datas.put("bandwidthConfig", productService.getBandwidthSetByOrg(id));
        datas.put("loadBalanceConfig", productService.getLoadBalanceSetByOrg(user.getId()));
        
		return ResponseBean.success(datas);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.LOAD_BALANCER, description = "管理员新增订单")
	public ResponseBean save(@RequestBody UpOrderLoadBalanceBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(loadBalanceService.save(bean, applyUser));
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.LOAD_BALANCER, description = "用户新增订单")
	public ResponseBean quotaSave(@RequestBody UpOrderLoadBalanceBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(loadBalanceService.save(bean, ThreadCache.getUser()));
	}
}