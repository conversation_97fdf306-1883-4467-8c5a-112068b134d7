package io.aicloudware.portal.api_up.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import io.aicloudware.portal.framework.controller.BaseController;

import io.swagger.annotations.Api;

@Controller
@RequestMapping("/new_application")
@Api(value = "/new_application", description = "新资源申请单", position = 600)
public class UpNewApplicationController extends BaseController {

//    @Autowired
//    private IUpNewApplicationService upNewApplicationService;
//
//    @RequestMapping(value = "/get_application/{applicationId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get_application/{applicationId}", httpMethod = "GET", value = "获取申请单详情")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取申请单详情", response = UpApplicationBean.class)})
//    @ResponseBody
//    public ResponseBean get_application(@ApiParam(value = "申请单ID") @PathVariable Integer applicationId) {
//        UpApplicationBean applicationBean = upNewApplicationService.getApplication(applicationId);
//        return ResponseBean.success(applicationBean);
//    }
//
//    @RequestMapping(value = "/create_application", method = RequestMethod.POST)
//    @ApiOperation(notes = "/create_application", httpMethod = "POST", value = "创建新申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "创建新申请单", response = UpApplicationBean.class)})
//    @ResponseBody
//    public ResponseBean create_application(@ApiParam(value = "申请单Bean") @RequestBody UpApplicationBean applicationBean, BindingResult bindingResult) {
//        applicationBean = upNewApplicationService.createApplication(applicationBean);
//        return ResponseBean.success(applicationBean);
//    }
//
//    @RequestMapping(value = "/update_application", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_application", httpMethod = "POST", value = "更新申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "更新申请单", response = UpApplicationBean.class)})
//    @ResponseBody
//    public ResponseBean update_application(@ApiParam(value = "申请单Bean") @RequestBody UpApplicationBean applicationBean,
//                                           BindingResult bindingResult) {
//        applicationBean = upNewApplicationService.updateApplication(applicationBean);
//        return ResponseBean.success(applicationBean);
//    }
//
//    @RequestMapping(value = "/submit_application/{applicationId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/submit_application/{applicationId}", httpMethod = "GET", value = "提交新申请单")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "提交新申请单", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean submit_application(@ApiParam(value = "申请单ID") @PathVariable Integer applicationId) {
//        upNewApplicationService.submitApplication(applicationId);
//        return ResponseBean.success(Boolean.TRUE);
//    }
//
//    @RequestMapping(value = "/get_reservation_list", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get_reservation_list", httpMethod = "GET", value = "获取预留列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取预留列表", response = SpOVDCBean[].class)})
//    @ResponseBody
//    public ResponseBean get_reservation_list() {
//        SpOVDCBean[] reservationList = upNewApplicationService.getReservationList();
//        return ResponseBean.success(reservationList);
//    }
//
//    @RequestMapping(value = "/get_available_ip_list/{appSystemId}/{reservationId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get_available_ip_list/{appSystemId}/{reservationId}", httpMethod = "GET", value = "获取可用IP列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取可用IP列表", response = SpIpUsageBean[].class)})
//    @ResponseBody
//    public ResponseBean get_available_ip_list(@ApiParam(value = "应用系统ID") @PathVariable Integer appSystemId,
//                                              @ApiParam(value = "预留ID") @PathVariable Integer reservationId) {
//        SpIpUsageBean[] availableIpList = upNewApplicationService.getAvailableIpList(appSystemId, reservationId);
//        return ResponseBean.success(availableIpList);
//    }
//
//    @RequestMapping(value = "/get_blueprint_machine_list/{blueprintId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get_blueprint_machine_list/{blueprintId}", httpMethod = "GET", value = "获取蓝图机器列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取蓝图机器列表", response = SpVappTemplateMachineBean[].class)})
//    @ResponseBody
//    public ResponseBean get_blueprint_machine_list(@ApiParam(value = "蓝图ID") @PathVariable Integer blueprintId) {
//        SpVappTemplateMachineBean[] blueprintMachineList = upNewApplicationService.getBlueprintMachineList(blueprintId);
//        return ResponseBean.success(blueprintMachineList);
//    }
//
//    @RequestMapping(value = "/get_req_blueprint_deployment_list/{applicationId}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get_req_blueprint_deployment_list/{applicationId}", httpMethod = "GET", value = "获取申请单部署列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "获取申请单部署列表", response = ReqBlueprintDeploymentBean[].class)})
//    @ResponseBody
//    public ResponseBean get_req_blueprint_deployment_list(@ApiParam(value = "申请单ID") @PathVariable Integer applicationId) {
//        ReqBlueprintDeploymentBean[] reqBlueprintDeploymentList = upNewApplicationService.getReqBlueprintDeploymentList(applicationId);
//        return ResponseBean.success(reqBlueprintDeploymentList);
//    }
//
//    @RequestMapping(value = "/save_req_blueprint_deployment_list/{applicationId}", method = RequestMethod.POST)
//    @ApiOperation(notes = "/save_req_blueprint_deployment_list/{applicationId}", httpMethod = "POST", value = "保存申请单部署列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "保存申请单部署列表", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean save_req_blueprint_deployment_list(@ApiParam(value = "申请单ID") @PathVariable Integer applicationId,
//                                                           @ApiParam(value = "部署列表") @RequestBody ReqBlueprintDeploymentBean[] reqBlueprintDeploymentList) {
//        upNewApplicationService.saveReqBlueprintDeploymentList(applicationId, reqBlueprintDeploymentList);
//        return ResponseBean.success(Boolean.TRUE);
//    }
}
