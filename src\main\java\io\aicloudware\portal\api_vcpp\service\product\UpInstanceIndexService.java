package io.aicloudware.portal.api_vcpp.service.product;

import io.aicloudware.portal.api_vcpp.entity.UpInstanceIndex;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpInstanceIndexBean;
import io.aicloudware.portal.framework.sdk.contants.RecordStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class UpInstanceIndexService extends BaseService implements IUpInstanceIndexService {

    private static final SimpleDateFormat DateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private UpInstanceIndexSyncService syncService;

    @Override
    public UpInstanceIndexBean[] list(UpInstanceIndexBean param) {
        Map<String, Object> params = new HashMap<>();
        if (param.getServiceType() != null) {
            params.put("serviceType", param.getServiceType());
        }
        if (param.getServiceCatalog() != null) {
            params.put("serviceCatalog", param.getServiceCatalog());
        }
        params.put("spOrg", new SpOrg(ThreadCache.getOrgId()));
        List<UpInstanceIndex> entities = dao.list(UpInstanceIndex.class, params);
        UpInstanceIndexBean[] beans = BeanCopyUtil.copy2BeanList(entities, UpInstanceIndexBean.class);
        for (UpInstanceIndexBean bean : beans) {
            bean.setServiceTypeText(bean.getServiceType() != null ? bean.getServiceType().getTitle() : "");
            bean.setServiceCatalogText(bean.getServiceCatalog() != null ? bean.getServiceCatalog().getTitle() : "");
        }
        return beans;
    }

    @Override
    public void sync(){
        List<SpRegionEntity> regions = dao.list(SpRegionEntity.class);
        List<Integer> spOrgIds = dao.list(SpOrg.class).stream().map(SpOrg::getId).collect(Collectors.toList());
        regions.forEach(region -> {
            spOrgIds.forEach(spOrgId -> {
                // 调用专门的同步服务，确保新事务生效
                syncService.syncInstanceIndex(spOrgId, region);
            });
        });
    }

    @Override
    public void syncByTask(){
        System.out.println("========================syncByTask===");
        String sql =
                "select sp_org_id,region_id from up_order where status =:status and update_tm >=:starttime and order_status =:orderStatus group by sp_org_id,region_id\n" +
                "union\n" +
                "select sp_org_id,region_id from up_task where status =:status and update_tm >=:starttime and task_status =:taskStatus and parent_id is null group by sp_org_id,region_id";
        List<Object[]> list = queryDao.querySql(sql,
                MapUtil.of("starttime", getPassedTime(),
                        "status", RecordStatus.active,
                        "orderStatus", UpOrderSystemEnums.OrderStatus.close_success.name(),
                        "taskStatus", UpTaskStatus.finish.name()));
        if(list.isEmpty()){
            return;
        }
        list.forEach(row -> {
            syncService.syncInstanceIndex((Integer) row[0], new SpRegionEntity((Integer) row[1]));
        });
    }

    @Override
    public void sync(Integer orgId, SpRegionEntity region) {
        syncService.syncInstanceIndex(orgId, region);
    }

    private Date getPassedTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -20);
        return calendar.getTime();
    }
}
