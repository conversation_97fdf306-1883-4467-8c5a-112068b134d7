package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpServicePlanBean;
import io.aicloudware.portal.framework.sdk.contants.SpServerConnectionType;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.platform_vcd.entity.SpServerConnection;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "up_service_plan")
@Access(AccessType.FIELD)
public final class UpServicePlan extends BaseEntity<UpServicePlanBean> {

	public  UpServicePlan() {
	}

	public UpServicePlan(Integer id){
		super(id);
	}

	@Column(name = "service_plan_type")
	@Enumerated(EnumType.STRING)
	private UpServicePlanType servicePlanType;

	@EntityProperty(isCopyOnUpdate = false)
	@Column(name = "service_plan_code")
	private String servicePlanCode;

	@Column(name = "price")
	private BigDecimal price;

	@Column(name = "auto_effective_date")
	private Date autoEffectiveDate;

	@Column(name = "auto_expiry_date")
	private Date autoExpiryDate;

	@Column(name = "remark")
	private String remark;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "source_service_plan_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpServicePlan sourceServicePlan;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "servicePlan")
	@Where(clause = "status!='deleted'")
	@org.hibernate.annotations.OrderBy(clause = "id")
	private List<UpServicePlanRegionRelation> servicePlanRegionRelations;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "servicePlan")
	@Where(clause = "status!='deleted'")
	@org.hibernate.annotations.OrderBy(clause = "id")
	private List<UpServicePlanItem> servicePlanItems;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "coupon_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpCoupon coupon;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "cloud_connection_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpServerConnection cloudConnection;

	@Column(name = "cloud_connection_type", nullable = false)
	@Enumerated(EnumType.STRING)
	private SpServerConnectionType cloudConnectionType;

	public UpServicePlanType getServicePlanType() {
		return servicePlanType;
	}

	public void setServicePlanType(UpServicePlanType servicePlanType) {
		this.servicePlanType = servicePlanType;
	}

	public String getServicePlanCode() {
		return servicePlanCode;
	}

	public void setServicePlanCode(String servicePlanCode) {
		this.servicePlanCode = servicePlanCode;
	}

	public List<UpServicePlanRegionRelation> getServicePlanRegionRelations() {
		return servicePlanRegionRelations;
	}

	public void setServicePlanRegionRelations(List<UpServicePlanRegionRelation> servicePlanRegionRelations) {
		this.servicePlanRegionRelations = servicePlanRegionRelations;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Date getAutoEffectiveDate() {
		return autoEffectiveDate;
	}

	public void setAutoEffectiveDate(Date autoEffectiveDate) {
		this.autoEffectiveDate = autoEffectiveDate;
	}

	public Date getAutoExpiryDate() {
		return autoExpiryDate;
	}

	public void setAutoExpiryDate(Date autoExpiryDate) {
		this.autoExpiryDate = autoExpiryDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public UpServicePlan getSourceServicePlan() {
		return sourceServicePlan;
	}

	public void setSourceServicePlan(UpServicePlan sourceServicePlan) {
		this.sourceServicePlan = sourceServicePlan;
	}

	public List<UpServicePlanItem> getServicePlanItems() {
		return servicePlanItems;
	}

	public void setServicePlanItems(List<UpServicePlanItem> servicePlanItems) {
		this.servicePlanItems = servicePlanItems;
	}

	public UpCoupon getCoupon() {
		return coupon;
	}

	public void setCoupon(UpCoupon coupon) {
		this.coupon = coupon;
	}

	public SpServerConnection getCloudConnection() {
		return cloudConnection;
	}

	public void setCloudConnection(SpServerConnection serverConnection) {
		this.cloudConnection = serverConnection;
	}

	public SpServerConnectionType getCloudConnectionType() {
		return cloudConnectionType;
	}

	public void setCloudConnectionType(SpServerConnectionType serverConnectionType) {
		this.cloudConnectionType = serverConnectionType;
	}
}
