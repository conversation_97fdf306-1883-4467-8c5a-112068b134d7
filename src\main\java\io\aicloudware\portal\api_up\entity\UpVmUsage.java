package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.dao.DaoUtil;
import io.aicloudware.portal.framework.common.BeanFactory;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.entity.IDataScopeEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpVmUsageBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

import org.hibernate.criterion.DetachedCriteria;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Set;

@Entity
@Table(name = "up_vm_usage")
@Access(AccessType.FIELD)
public class UpVmUsage extends BaseEntity<UpVmUsageBean> implements IDataScopeEntity<UpVmUsageBean>, IEnvironmentEntity<UpVmUsageBean> {

    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @Column(name = "date")
    private Integer date;

    @JoinColumn(name = "sp_vm_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;

    @Column(name = "cpu_usage_mhz")
    private Integer cpuUsageMHz;
    @Column(name = "cpu_free_mhz")
    private Integer cpuFreeMHz;

    @Column(name = "memory_usage_m")
    private Integer memoryUsageM;
    @Column(name = "memory_free_m")
    private Integer memoryFreeM;

    @Column(name = "disk_usage_g")
    private Integer diskUsageG;
    @Column(name = "disk_free_g")
    private Integer diskFreeG;

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpVmUsageBean> searchBean, Set<String> aliasSet) {
        DaoUtil.addDataScope(searchBean.getMainMenuType(), criteria, aliasSet);
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }

    public Integer getDate() {
        return date;
    }

    public void setDate(Integer date) {
        this.date = date;
    }

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpVm getVm() {
        return vm;
    }

    public void setVm(SpVm vm) {
        this.vm = vm;
        if (vm != null && Utility.isNotZero(vm.getId())) {
            vm = BeanFactory.getCloudDao().load(SpVm.class, vm.getId());
        }
    }

    public Integer getCpuUsageMHz() {
        return cpuUsageMHz;
    }

    public void setCpuUsageMHz(Integer cpuUsageMHz) {
        this.cpuUsageMHz = cpuUsageMHz;
    }

    public Integer getCpuFreeMHz() {
        return cpuFreeMHz;
    }

    public void setCpuFreeMHz(Integer cpuFreeMHz) {
        this.cpuFreeMHz = cpuFreeMHz;
    }

    public Integer getMemoryUsageM() {
        return memoryUsageM;
    }

    public void setMemoryUsageM(Integer memoryUsageM) {
        this.memoryUsageM = memoryUsageM;
    }

    public Integer getMemoryFreeM() {
        return memoryFreeM;
    }

    public void setMemoryFreeM(Integer memoryFreeM) {
        this.memoryFreeM = memoryFreeM;
    }

    public Integer getDiskUsageG() {
        return diskUsageG;
    }

    public void setDiskUsageG(Integer diskUsageG) {
        this.diskUsageG = diskUsageG;
    }

    public Integer getDiskFreeG() {
        return diskFreeG;
    }

    public void setDiskFreeG(Integer diskFreeG) {
        this.diskFreeG = diskFreeG;
    }
}
