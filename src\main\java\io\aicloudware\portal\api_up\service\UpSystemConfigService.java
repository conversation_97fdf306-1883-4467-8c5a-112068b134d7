package io.aicloudware.portal.api_up.service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.event.Level;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpSystemConfig;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.executor.IExecutorAA;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Logger;
import io.aicloudware.portal.framework.utility.Utility;

@Service
@Transactional
public class UpSystemConfigService extends BaseService implements IUpSystemConfigService {

    private static final Map<Integer, UpSystemConfigBean> SystemConfigCache = new ConcurrentHashMap<>();

    private void initCache() {
        if (SystemConfigCache.isEmpty()) {
            synchronized (getClass()) {
                if (SystemConfigCache.isEmpty()) {
                    for (UpSystemConfig systemConfig : dao.list(UpSystemConfig.class)) {
                        UpSystemConfigBean bean = BeanCopyUtil.copy2Bean(systemConfig, UpSystemConfigBean.class);
                        SystemConfigCache.put(systemConfig.getId(), bean);
                    }
                }
            }
        }
    }

    private void handle(UpSystemConfigListBean bean, IExecutorAA<UpSystemConfigBean, UpSystemConfigBean> executor) {
        initCache();
        for (UpSystemConfigBean systemConfigBean : bean.getDataList()) {
            UpSystemConfigBean dbBean = null;
            if (Utility.isNotZero(systemConfigBean.getId())) {
                UpSystemConfigBean cache = SystemConfigCache.get(systemConfigBean.getId());
                if (Utility.equals(systemConfigBean.getKey(), cache.getKey())
                        && cache.getKey().isSystemOrTenantLevel()
                        && Utility.equals(systemConfigBean.getName(), cache.getName())) {
                    dbBean = cache;
                }
            }
            if (dbBean == null) {
                for (UpSystemConfigBean cache : SystemConfigCache.values()) {
                    if (Utility.equals(systemConfigBean.getKey(), cache.getKey())
                            && (cache.getKey().isSystemOrTenantLevel())
                            && Utility.equals(systemConfigBean.getName(), cache.getName())) {
                        dbBean = cache;
                        break;
                    }
                }
            }
            executor.doExecute(dbBean, systemConfigBean);
        }
    }

    @Override
    public UpSystemConfigBean get(UpSystemConfigKey key) {
        UpSystemConfigBean bean = new UpSystemConfigBean();
        bean.setKey(key);
        bean.setName(key.name());
        UpSystemConfigListBean listBean = new UpSystemConfigListBean();
        listBean.setDataList(new UpSystemConfigBean[]{bean});
        query(listBean);
        if (Utility.isEmpty(bean.getValue())) {
            bean.setValue(key.getDefaultValue());
        }
        return bean;
    }

    @Override
    public UpSystemConfigListBean query(UpSystemConfigListBean bean) {
        handle(bean, (dbBean, reqBean) -> {
            if (dbBean != null) {
                reqBean.setId(dbBean.getId());
                reqBean.setValue(dbBean.getValue());
            }
            if (Utility.isEmpty(reqBean.getValue())) {
                reqBean.setValue(reqBean.getKey().getDefaultValue());
            }
        });
        return bean;
    }

    @Override
    public UpSystemConfigListBean save(UpSystemConfigListBean bean) {
        handle(bean, (dbBean, reqBean) -> {
            AssertUtil.check(!reqBean.getKey().isSystemOrTenantLevel() || ThreadCache.getUser() == null || ThreadCache.getUser().getSystemAdmin(), "全局参数必须由系统管理员设置");
            if (dbBean != null) {
                UpSystemConfig systemConfig = dao.load(UpSystemConfig.class, dbBean.getId());
                systemConfig.setValue(reqBean.getValue());
                if (UpSystemConfigKey.log_level.toString().equals(systemConfig.getKey().toString())) {
                    Logger.setLevel(Level.valueOf(systemConfig.getValue()));
                }
                if (Utility.isNotEmpty(reqBean.getValue()) && Utility.notEquals(reqBean.getValue(), reqBean.getKey().getDefaultValue())) {
                    dao.update(systemConfig, "value");
                } else {
                    dao.delete(UpSystemConfig.class, systemConfig.getId());
                }
                reqBean.setId(systemConfig.getId());
                dbBean.setValue(systemConfig.getValue());
            } else if (Utility.isNotEmpty(reqBean.getValue()) && Utility.notEquals(reqBean.getValue(), reqBean.getKey().getDefaultValue())) {
                UpSystemConfig systemConfig = dao.insert(BeanCopyUtil.copy(reqBean, UpSystemConfig.class));
                if (UpSystemConfigKey.log_level.toString().equals(systemConfig.getKey().toString())) {
                    Logger.setLevel(Level.valueOf(systemConfig.getValue()));
                }
                reqBean.setId(systemConfig.getId());
                reqBean = BeanCopyUtil.copy2Bean(systemConfig, UpSystemConfigBean.class);
                SystemConfigCache.put(systemConfig.getId(), reqBean);
            }
        });
        return bean;
    }

    @Override
    public void clearCache() {
        SystemConfigCache.clear();
    }
    
    @Override
    public void refreshConfig(UpSystemConfigKey key) {
    	List<UpSystemConfig> list = this.dao.list(UpSystemConfig.class,"key",key);
    	UpSystemConfig entity = null;
    	if(list!=null && !list.isEmpty()) {
    		entity = list.get(0);
    	}else {
    		entity = new UpSystemConfig();
    		entity.setKey(key);
    		entity.setName(key.name());
    		entity.setValue("");
    		this.dao.insert(entity);
    	}
    	if(SystemConfigCache.containsKey(entity.getId())) {
    		SystemConfigCache.get(entity.getId()).setValue(entity.getValue());
		}else {
			SystemConfigCache.put(entity.getId(), BeanCopyUtil.copy2Bean(entity, UpSystemConfigBean.class));
		}
    }
}
