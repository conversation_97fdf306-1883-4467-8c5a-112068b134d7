package io.aicloudware.portal.api_rest.framework.entity;

import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import io.aicloudware.portal.api_rest.framework.bean.RestVpcInfoBean;
import io.aicloudware.portal.framework.entity.BaseUpEntity;

@Entity
@Table(name = "rest_vpc_info")
@Access(AccessType.FIELD)
public class RestVpcInfo extends BaseUpEntity<RestVpcInfoBean> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8163495242507614643L;

	@Column(name = "vpc_id")
	private String vpcId;
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "vpcInfo")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
	private List<RestSubnet> subnets;

	public String getVpcId() {
		return vpcId;
	}

	public void setVpcId(String vpcId) {
		this.vpcId = vpcId;
	}

	public List<RestSubnet> getSubnets() {
		return subnets;
	}

	public void setSubnets(List<RestSubnet> subnets) {
		this.subnets = subnets;
	}
	
}
	