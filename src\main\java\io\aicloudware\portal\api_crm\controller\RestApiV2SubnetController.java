package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.framework.bean.RestV2SubnetBean;
import io.aicloudware.portal.api_crm.service.IRestVPCService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseEntityController;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import io.aicloudware.portal.platform_vcd.service.ISpVPCService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * rest api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/api/v2/subnet")
@Api(value = "/api/v2/subnet", description = "REST API V2")
public class RestApiV2SubnetController extends BaseEntityController<SpVPC, SpVPCBean, SpVPCResultBean> {

    @Autowired
    private ISpVPCService spVPCService;

    @Autowired
    private IRestVPCService restVPCService;

	@RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean add(@ApiParam(value = "实例对象") @Valid @RequestBody RestV2SubnetBean bean, HttpServletRequest request) {
        commonService.load(SpVPC.class, bean.getVpcId().intValue(), ThreadCache.getOrgId());
        return ResponseBean.success(restVPCService.addNetwork(bean));
    }


	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    public ResponseBean delete(@PathVariable Integer id, HttpServletRequest request) {
        commonService.load(SpOVDCNetwork.class, id, ThreadCache.getOrgId());
        try {
            spVPCService.deleteNetwork(id);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBean.error(2, "客户端业务逻辑异常", "该子网正在被使用，无法删除");
        }
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "实例对象") @Valid @RequestBody SpVPCBean bean) {
        SpVPCBean vpcBean = commonService.load(SpVPC.class, SpVPCBean.class, bean.getId(), ThreadCache.getOrgId());

        List<RestV2SubnetBean> SpSubnetBeanList = new ArrayList<>();
        if (vpcBean.getVpcRelation() != null) {
            for (SpVPCRelationBean relation :vpcBean.getVpcRelation()) {
                SpOVDCNetworkBean ovdcNetworkBean = spVPCService.getOvdcNetwork(relation.getOvdcNetworkId());

                RestV2SubnetBean subnetBean = new RestV2SubnetBean();
                subnetBean.setId(ovdcNetworkBean.getId());
                subnetBean.setName(ovdcNetworkBean.getName());
                subnetBean.setVpcId(bean.getId().longValue());

                if(ovdcNetworkBean.getIpScopeList() != null && ovdcNetworkBean.getIpScopeList().length > 0){
                    SpIpScopeBean spIpScopeBean = ovdcNetworkBean.getIpScopeList()[0];
                    subnetBean.setGateway(spIpScopeBean.getGateway());
                    subnetBean.setNetMask(spIpScopeBean.getNetMask());
                    subnetBean.setDns1(spIpScopeBean.getDns1());
                    subnetBean.setDns2(spIpScopeBean.getDns2());
                    if(spIpScopeBean.getIpRangeList() != null && spIpScopeBean.getIpRangeList().length > 0){
                        SpIpRangeBean spIpRangeBean = spIpScopeBean.getIpRangeList()[0];
                        subnetBean.setIpBegin(spIpRangeBean.getIpBegin());
                        subnetBean.setIpEnd(spIpRangeBean.getIpEnd());
                    }
                }
                SpSubnetBeanList.add(subnetBean);
            }
        }

        return ResponseBean.success(SpSubnetBeanList);
    }

}
