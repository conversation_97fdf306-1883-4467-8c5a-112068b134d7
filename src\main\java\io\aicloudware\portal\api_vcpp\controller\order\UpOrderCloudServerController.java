package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.*;

import javax.servlet.http.HttpServletRequest;

import io.aicloudware.portal.framework.sdk.bean.product.UpProductVmSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderCloudServerService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudServerChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ElasticIpChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.KeyType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductVmSetType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ServerType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.framework.utility.AssertUtil;

import io.swagger.annotations.ApiParam;

/**
 * 云服务器申请
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/cloudServer")
public class UpOrderCloudServerController extends BaseController {

	@Autowired
	private IUpOrderCloudServerService cloudServerService;
	
	@Autowired
	private IUpProductService productService;

	@Autowired
	private IUpOrderService orderService;
	
	@RequestMapping(value = "/quota/init/{code}")
    @ResponseBody
	public ResponseBean quotaInit(@ApiParam(value = "对象ID") @PathVariable String code) {
		
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");

		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (CloudServerChargeType type : CloudServerChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
 		Map<String, String> serverType = new LinkedHashMap<String, String>();
 		for (ServerType type : ServerType.values()) {
 			serverType.put(type.toString(), type.getTitle());
 		}
 		datas.put("serverStandardType", serverType);
        
 		Map<String, String> diskType = new LinkedHashMap<String, String>();
        for (SpVmDiskType type : SpVmDiskType.values()) {
        	diskType.put(type.toString(), type.getTitle());
        }
        datas.put("diskType", diskType);
        
        Map<String, String> diskStandardType = new LinkedHashMap<String, String>();
        for (DiskType type : DiskType.values()) {
        	diskStandardType.put(type.toString(), type.getTitle());
        }
        datas.put("diskStandardType", diskStandardType);


        
//        if(code.equals("standard_01_04")) {
//        	datas.put("diskConfig", productService.getDiskSet(ProductDiskSetType.vm_disk, "standard50"));
//        }else {
        	datas.put("diskConfig", new HashMap());
//        }
        datas.put("bandwidthConfig", productService.getBandwidthSetByOrg(ThreadCache.getOrgId()));

		//List<Map<String,Object>> images = orderService.getTemplatesByCatalog(ThreadCache.getRegion(), ThreadCache.getOrgId(), CatalogType.iaas, CatalogType.local);

		UpOrderQuotaDetail quotaDetail = commonService.load(UpOrderQuotaDetail.class, Integer.valueOf(code));
		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
		//	images.stream().forEach(item -> item.put("systemDiskGB", quotaDetail.getDiskG()));
			Map<ServerType,List<UpProductVmSetBean>> map = new HashMap<>();
			UpProductVmSetBean bean = new UpProductVmSetBean();
			bean.setCpuUnit(quotaDetail.getCpu());
			bean.setMemoryUnit(quotaDetail.getMemoryG());
			bean.setDiskUnit(quotaDetail.getDiskG());
			map.put(ServerType.high_performance, Collections.singletonList(bean));
			datas.put("serverConfigs", map);
		}else{
			// 配置
			datas.put("serverConfigs", productService.queryVmSet(ProductVmSetType.vm, code));
		}

        // 镜像
        //datas.put("images", images);
        
        // 网络
        datas.put("vpc", orderService.getVPC(ThreadCache.getUserId()));
        
        // 密钥对
        datas.put("keyPairs", cloudServerService.getKeyPairs());
        
        // 账户
        datas.put("account",  user.getName());
        
        // 弹性公网IP-计费方式
        Map<String, String> elasticIpChargeType = new LinkedHashMap<String, String>();
        for (ElasticIpChargeType type : ElasticIpChargeType.values()) {
        	elasticIpChargeType.put(type.toString(), type.getTitle());
        }
        datas.put("elasticIpChargeType", elasticIpChargeType);
        
        // 密钥方式
        Map<String, String> keyType = new LinkedHashMap<String, String>();
        for (KeyType type : KeyType.values()) {
        	keyType.put(type.toString(), type.getTitle());
        }
        datas.put("keyType", keyType);
        
		return ResponseBean.success(datas);
	}
	
	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (CloudServerChargeType type : CloudServerChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
 		Map<String, String> serverType = new LinkedHashMap<String, String>();
 		for (ServerType type : ServerType.values()) {
 			serverType.put(type.toString(), type.getTitle());
 		}
 		datas.put("serverStandardType", serverType);
        
 		Map<String, String> diskType = new LinkedHashMap<String, String>();
        for (SpVmDiskType type : SpVmDiskType.values()) {
        	diskType.put(type.toString(), type.getTitle());
        }
        datas.put("diskType", diskType);
        
        Map<String, String> diskStandardType = new LinkedHashMap<String, String>();
        for (DiskType type : DiskType.values()) {
        	diskStandardType.put(type.toString(), type.getTitle());
        }
        datas.put("diskStandardType", diskStandardType);
        
        // 配置
        datas.put("serverConfigs", productService.queryVmSet(null, ThreadCache.getRegion()));
        datas.put("diskConfig", productService.mapDiskSet(ProductDiskSetType.vm_disk));
        datas.put("bandwidthConfig", productService.getBandwidthSetByOrg(id));
        
        // 镜像
   //     datas.put("images", orderService.getTemplatesByCatalog(ThreadCache.getRegion(), ThreadCache.getOrgId(), CatalogType.iaas, CatalogType.local));
        
        // 网络
        datas.put("vpc", orderService.getVPC(id));
        
        // 密钥对
        datas.put("keyPairs", cloudServerService.getKeyPairs(id));
        
        // 账户
        datas.put("account",  user.getName());
        
        // 弹性公网IP-计费方式
        Map<String, String> elasticIpChargeType = new LinkedHashMap<String, String>();
        for (ElasticIpChargeType type : ElasticIpChargeType.values()) {
        	elasticIpChargeType.put(type.toString(), type.getTitle());
        }
        datas.put("elasticIpChargeType", elasticIpChargeType);
        
        // 密钥方式
        Map<String, String> keyType = new LinkedHashMap<String, String>();
        for (KeyType type : KeyType.values()) {
        	keyType.put(type.toString(), type.getTitle());
        }
        datas.put("keyType", keyType);
        
		return ResponseBean.success(datas);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.SERVER, description = "管理员新增订单")
	public ResponseBean save(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(cloudServerService.save(bean, applyUser));
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.SERVER, description = "用户新增订单")
	public ResponseBean quotaSave(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(cloudServerService.save(bean, ThreadCache.getUser()));
	}
	
	@RequestMapping(value = "/getUpdateConfigByQuotaDetail/{id}")
    @ResponseBody
    public ResponseBean getUpdateConfigByQuotaDetail(@ApiParam(value = "quotaDetailId") @PathVariable Integer id) {
    	UpOrderQuotaDetail detail = commonService.load(UpOrderQuotaDetail.class, id, ThreadCache.getOrgId());
    	AssertUtil.check(detail != null && detail.getQuotaDetailStatus() == QuotaDetailStatus.start, "订单项状态异常");
		UpProductVmSetBean vmSetBean = null;
		if(detail.getIsCustom() != null && detail.getIsCustom()){
			vmSetBean = new UpProductVmSetBean();
			vmSetBean.setEnabled(true);
			vmSetBean.setCpuUnit(detail.getCpu());
			vmSetBean.setMemoryUnit(detail.getMemoryG());
			vmSetBean.setDiskUnit(detail.getDiskG());
		}else{
			vmSetBean = productService.getVmSetByCode(detail.getProductCode());
		}
        return ResponseBean.success(vmSetBean);
    }
	
	@RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.SERVER, description = "更新订单")
	public ResponseBean update(@RequestBody UpOrderCloudServerBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(cloudServerService.update(bean, ThreadCache.getUser()));
	}
}
