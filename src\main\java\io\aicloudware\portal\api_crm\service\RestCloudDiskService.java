package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_crm.framework.bean.RestV2CloudDiskBean;
import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderCloudDisk;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuota;
import io.aicloudware.portal.api_vcpp.entity.UpOrderQuotaDetail;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.dao.ICloudDao;
import io.aicloudware.portal.framework.sdk.bean.SpVmDiskBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductDiskSetBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVm;
import io.aicloudware.portal.platform_vcd.entity.SpVmDisk;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.regex.Pattern;

@Service
@Transactional
public class RestCloudDiskService extends BaseService implements IRestCloudDiskService {

	@Autowired
	private IUpOrderService orderService;

	@Autowired
	private IUpOrderQuotaService upOrderQuotaService;

	@Autowired
	private IUpProductService upProductService;

	@Autowired
	protected ICloudDao cloudDao;
	
	@Override
	public Integer save(RestV2CloudDiskBean bean) {
		UpOrderQuotaDetail quotaDetail = upOrderQuotaService.getQuotaDetail(new SpOrg(ThreadCache.getOrgId()), ThreadCache.getRegion(), bean.getDoorOrderItemId());
		UpOrderQuota quota = quotaDetail.getQuota();
		AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
				&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId())
				&& quota.getCatalog() == UpProductSystemEnums.QuotaCatalog.NEW
				&& quotaDetail.getType() == UpProductSystemEnums.ProductType.STORAGE
				,"非法操作，订单状态异常！");
		AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");

		if(quotaDetail.getIsCustom() != null && quotaDetail.getIsCustom()){
			bean.setDiskGB(quotaDetail.getDiskG());
		}else{
			UpProductDiskSetBean diskSetBean = upProductService.getDiskSet(quotaDetail.getProductCode());
			bean.setDiskGB(diskSetBean.getUnit());
		}
		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user, "所选用户不存在！");

		AssertUtil.check(bean.getDoorOrderItemId(), "请输入doorOrderItemId！");
		AssertUtil.check(bean.getDiskGB(), "请输入容量！");
		AssertUtil.check(bean.getName(), "请输入云盘名称！");
		//AssertUtil.check(bean.getDiskType(), "请选择云盘类型！");
		AssertUtil.check(Pattern.matches("^[0-9a-zA-Z-]+$", bean.getName()), "云盘名称只能由字母数字中划线组成！");
		AssertUtil.check(Pattern.matches(".*[a-zA-Z]+.*", bean.getName()), "云盘名称必须包含字母！");
		AssertUtil.check(bean.getCloudServerId(), "请选择云服务器！");

		bean.setOwnerId(ThreadCache.getUserId());

		UpOrderCloudDisk entity = BeanCopyUtil.copy(bean, UpOrderCloudDisk.class);
		if (entity.getType() == null) {
			entity.setType(SpVmDiskType.mount);
		}

		SpVm vm = this.dao.load(SpVm.class, bean.getCloudServerId());
		AssertUtil.check(vm, "未找到相关云服务器信息！");
		AssertUtil.check(vm.getSpOrg().getId().equals(user.getOrg().getId()), "云服务器所属用户组异常！");
		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_cloud_disk, user.getId()) == 0, "您有未完成的云盘申请！");
		
		entity.setCloudServerId(bean.getCloudServerId());

		int count = 0;
		if (vm.getDiskList() != null && vm.getDiskList().size() > 0) {
	        for (SpVmDisk disk : vm.getDiskList()) {
	            if (disk.getDiskNumber() != null && disk.getDiskNumber() > count) {
	                count = disk.getDiskNumber();
	            }
	        }
            count++;
		}
		entity.setDiskNumber(count);
		entity.setName(entity.getName() + "-" + System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000)) + "-" + count);

		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_cloud_disk);
		order.setName("[" + OrderType.new_cloud_disk + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(user);
		order.setPaymentType(bean.getPaymentType());
		order.setSystemDiskNum(0);
		order.setDiskNum(entity.getDiskGB());
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quota);

		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		this.dao.insert(order);

		entity.setSpOrg(user.getOrg());
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(order.getType());
		task.setTaskStatus(UpTaskStatus.start);
		task.setSpOrg(user.getOrg());
		task.setRegion(order.getRegion());
		dao.insert(task);

		return order.getId();
	}

	@Override
	public Integer deleteDisk(SpVmDiskBean bean) {
		SpOrg org = dao.load(SpOrg.class, ThreadCache.getOrgId());
		SpVm vm = cloudDao.loadBySpUuid(SpVm.class, org, String.valueOf(bean.getVmUuid()));
		AssertUtil.check(vm!=null, "资源不存在");

		UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		if(vm.getOrder()!= null ) {
			owner = vm.getOrder().getOwner();
		}
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.cloud_disk_delete);
		order.setName("[" + OrderType.cloud_disk_delete + "] VM Name: " + vm.getName() + "- Disk Number: " + bean.getDiskNumber() + System.currentTimeMillis());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(owner);
		order.setApplyUser(applyUser);
		order.setSpOrg(vm.getSpOrg());
		order.setNumber(1);
		this.dao.insert(order);

		UpOrderCloudDisk cloudOrderDisk = new UpOrderCloudDisk();
		cloudOrderDisk.setRegion(order.getRegion());
		cloudOrderDisk.setCloudServerId(vm.getId());
		cloudOrderDisk.setDiskNumber(bean.getDiskNumber());
		cloudOrderDisk.setOwner(owner);
		cloudOrderDisk.setSpOrg(vm.getSpOrg());
		cloudOrderDisk.setName("[" + OrderType.cloud_disk_delete + "] VM Name: " + vm.getName() + "- Disk Number: " + bean.getDiskNumber() + System.currentTimeMillis());
		cloudOrderDisk.setOrder(order);
		this.dao.insert(cloudOrderDisk);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(OrderType.cloud_disk_delete);
		task.setSpOrg(vm.getSpOrg());
		task.setTaskStatus(UpTaskStatus.start);
		task.setRegion(order.getRegion());
		dao.insert(task);
		return order.getId();
	}

}
