package io.aicloudware.portal.api_vcpp.entity;

import java.util.Date;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.profile.UpWorkSheetDetailBean;
import io.aicloudware.portal.framework.sdk.contants.UpWorkSheetChannel;

@Entity
@Table(name = "up_work_sheet_detail")
@Access(AccessType.FIELD)
public final class UpWorkSheetDetail extends BaseUpEntity<UpWorkSheetDetailBean> {

	private static final long serialVersionUID = -8214372816000722400L;

	@JoinColumn(name = "user_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpUser user;

	@EntityProperty(isCopyOnUpdate = false)
	@Column(name = "send_tm", nullable = false)
	private Date sendTm;

	@Column(name = "channel")
	@Enumerated(EnumType.STRING)
	private UpWorkSheetChannel channel;

	@Column(name = "content", length = ApiConstants.STRING_MAX_LENGTH)
	private String content;

	@JoinColumn(name = "work_sheet_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpWorkSheet workSheet;

	public UpUser getUser() {
		return user;
	}

	public void setUser(UpUser user) {
		this.user = user;
	}

	public Date getSendTm() {
		return sendTm;
	}

	public void setSendTm(Date sendTm) {
		this.sendTm = sendTm;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public UpWorkSheet getWorkSheet() {
		return workSheet;
	}

	public void setWorkSheet(UpWorkSheet workSheet) {
		this.workSheet = workSheet;
	}

	public UpWorkSheetChannel getChannel() {
		return channel;
	}

	public void setChannel(UpWorkSheetChannel channel) {
		this.channel = channel;
	}

}
