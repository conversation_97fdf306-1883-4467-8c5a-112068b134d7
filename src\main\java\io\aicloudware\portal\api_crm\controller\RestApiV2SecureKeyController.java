package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseSpInvokeController;
import io.aicloudware.portal.framework.sdk.bean.SpSecureKeyBean;
import io.aicloudware.portal.framework.sdk.bean.SpSecureKeyResultBean;
import io.aicloudware.portal.framework.sdk.bean.SpSecureKeySearchBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.service.IInvokeService;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpSecureKey;
import io.aicloudware.portal.platform_vcd.service.ISpSecureKeyService;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Controller
@RequestMapping("/api/v2/secure_key")
@Api(value = "/api/v2/secure_key", description = "密钥对", position = 33)
public class RestApiV2SecureKeyController extends BaseSpInvokeController<SpSecureKey, SpSecureKeyBean, SpSecureKeyResultBean> {

    @Autowired
    private ISpSecureKeyService spSecureKeyService;

    @Override
    protected IInvokeService<SpSecureKey, ? extends SearchBean<SpSecureKeyBean>, SpSecureKeyBean> getInvokeService() {
        return null;
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象列表", response = SpSecureKeyResultBean.class) })
    @ResponseBody
    public ResponseBean querySecureKey(@ApiParam(value = "查询条件") @RequestBody SpSecureKeySearchBean searchBean) {
        SpSecureKey entity = BeanCopyUtil.copy(searchBean.getBean(), SpSecureKey.class);
        entity.setSpOrg(new SpOrg());
        entity.getSpOrg().setId(ThreadCache.getOrgId());
        entity.setRegion(ThreadCache.getRegion());
        if (StringUtils.isNotEmpty(entity.getName())) {
            SpSecureKeyBean fuzzyBean = new SpSecureKeyBean();
            fuzzyBean.setName(entity.getName());
            entity.setName(null);
            searchBean.setFuzzyBean(fuzzyBean);
        }
        SpSecureKeyBean[] entityList = spSecureKeyService.query(searchBean, entity);
        SpSecureKeyResultBean result = new SpSecureKeyResultBean();
        fillPageInfo(searchBean, result);
        result.setDataList(entityList);
        return ResponseBean.success(result);
    }


    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(notes = "/add", httpMethod = "POST", value = "创建密钥对")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回实例对象", response = SpSecureKeyBean.class) })
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SECURE_KEY, description = "创建")
    public ResponseBean addSecureKey(@ApiParam(value = "实例对象") @Valid @RequestBody SpSecureKeyBean bean,
            BindingResult bindingResult) {
        return ResponseBean.success(spSecureKeyService.secureKeyCreate(bean));
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除密钥对")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class) })
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SECURE_KEY, description = "删除")
    public ResponseBean deleteSecureKey(@ApiParam(value = "对象ID") @PathVariable String id) {
        return ResponseBean.success(spSecureKeyService.secureKeyDelete(id));
    }
    
    @RequestMapping(value = "/download/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/download/{id}", produces="application/octet-stream", httpMethod = "GET", value = "密钥对下载")
    @ApiResponses(value = { @ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class) })
    @ResponseBody
    @AuditLogSpEntity(type = UpProductSystemEnums.ProductType.SECURE_KEY, description = "下载")
    public void download(@ApiParam(value = "对象ID") @PathVariable String id, HttpServletRequest request, HttpServletResponse response) throws Exception {
        spSecureKeyService.download(request, response, id);
    }


}
