package io.aicloudware.portal.api_rest.framework.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpRoleBean;

@Entity
@Table(name = "rest_create_tenant_message")
@Access(AccessType.FIELD)
public class RestCreateTenantMessage extends BaseUpEntity<UpRoleBean> {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4052847882103874370L;
	
	@Column(name = "tenant_id")
	private String tenantId;

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	
}
	