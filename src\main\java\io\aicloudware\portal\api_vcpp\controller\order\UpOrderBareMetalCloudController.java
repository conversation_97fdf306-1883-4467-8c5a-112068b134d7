package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBareMetalCloudBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.BareMetalCloudType;
import io.aicloudware.portal.framework.utility.AssertUtil;

import io.swagger.annotations.ApiParam;

/**
 * 裸金属
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/bareMetalCloud")
public class UpOrderBareMetalCloudController extends BaseController {

	@RequestMapping(value = "/quota/init/{code}")
    @ResponseBody
	public ResponseBean quotaInit(@ApiParam(value = "对象ID") @PathVariable String code) {
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		Map<String,Object> datas = new HashMap<>();
		
		List<Map> templates = new ArrayList<>();
		Map template_win = new HashMap();
		template_win.put("id", 2);
		template_win.put("name", "Windows2016");
		templates.add(template_win);
		
		Map template_centeos = new HashMap();
		template_centeos.put("id", 3);
		template_centeos.put("name", "CenteOS");
		templates.add(template_centeos);
		datas.put("templates", templates);
		
		UpOrderBareMetalCloudBean bean = new UpOrderBareMetalCloudBean();
		if(code.equals("product_bms_standard")) {
			bean.setType(BareMetalCloudType.bms_standard);
			bean.setName("2 路Intel Xeon E5-2650v4");
			bean.setCpuCacheM(30);
			bean.setCpuClockSpeedGHz("2.2");
			bean.setCpuNum(12);
			bean.setDisk("1.2T");
			bean.setDiskNum(4);
			bean.setDiskType("SAS");
			bean.setMemoryGB(64);
			bean.setMultiModeFiberNum(2);
		}else if(code.equals("product_bms_premium")) {
			bean.setType(BareMetalCloudType.bms_premium);
			bean.setName("2 路Intel Xeon E5-2650v4");
			bean.setCpuCacheM(30);
			bean.setCpuClockSpeedGHz("2.2");
			bean.setCpuNum(12);
			bean.setDisk("1.2T");
			bean.setDiskNum(6);
			bean.setDiskType("SAS");
			bean.setMemoryGB(128);
			bean.setMultiModeFiberNum(4);
		}else if(code.equals("product_bms_professional")) {
			bean.setType(BareMetalCloudType.bms_professional);
			bean.setName("2 路Intel Xeon E5-2650v4");
			bean.setCpuCacheM(30);
			bean.setCpuClockSpeedGHz("2.2");
			bean.setCpuNum(12);
			bean.setDisk("480G");
			bean.setDiskNum(10);
			bean.setDiskType("SAS");
			bean.setMemoryGB(256);
			bean.setMultiModeFiberNum(4);
		}
		
		Map<String, String> types = new LinkedHashMap<String, String>();
        for (BareMetalCloudType type : BareMetalCloudType.values()) {
        	types.put(type.toString(), type.getTitle());
        }
        datas.put("types", types);
        
        // 配置
        datas.put("config", bean);
		return ResponseBean.success(datas);
	}
	
	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		
		Map<String,Object> datas = new HashMap<>();
		
		List<Map> templates = new ArrayList<>();
		Map template_win = new HashMap();
		template_win.put("id", 2);
		template_win.put("name", "Windows2016");
		templates.add(template_win);
		
		Map template_centeos = new HashMap();
		template_centeos.put("id", 3);
		template_centeos.put("name", "CenteOS");
		templates.add(template_centeos);
		datas.put("templates", templates);
		
		List<UpOrderBareMetalCloudBean> beans = new ArrayList<>();
//		if(code.equals("product_bms_standard")) {
			UpOrderBareMetalCloudBean bean = new UpOrderBareMetalCloudBean();
			bean.setType(BareMetalCloudType.bms_standard);
			bean.setName("2 路Intel Xeon E5-2650v4");
			bean.setCpuCacheM(30);
			bean.setCpuClockSpeedGHz("2.2");
			bean.setCpuNum(12);
			bean.setDisk("1.2T");
			bean.setDiskNum(4);
			bean.setDiskType("SAS");
			bean.setMemoryGB(64);
			bean.setMultiModeFiberNum(2);
			beans.add(bean);
//		}else if(code.equals("product_bms_premium")) {
			UpOrderBareMetalCloudBean bean1 = new UpOrderBareMetalCloudBean();
			bean1.setType(BareMetalCloudType.bms_premium);
			bean1.setName("2 路Intel Xeon E5-2650v4");
			bean1.setCpuCacheM(30);
			bean1.setCpuClockSpeedGHz("2.2");
			bean1.setCpuNum(12);
			bean1.setDisk("1.2T");
			bean1.setDiskNum(6);
			bean1.setDiskType("SAS");
			bean1.setMemoryGB(128);
			bean1.setMultiModeFiberNum(4);
			beans.add(bean1);
//		}else if(code.equals("product_bms_professional")) {
			UpOrderBareMetalCloudBean bean2 = new UpOrderBareMetalCloudBean();
			bean2.setType(BareMetalCloudType.bms_professional);
			bean2.setName("2 路Intel Xeon E5-2650v4");
			bean2.setCpuCacheM(30);
			bean2.setCpuClockSpeedGHz("2.2");
			bean2.setCpuNum(12);
			bean2.setDisk("480G");
			bean2.setDiskNum(10);
			bean2.setDiskType("SAS");
			bean2.setMemoryGB(256);
			bean2.setMultiModeFiberNum(4);
			beans.add(bean2);
//		}
		
		Map<String, String> types = new LinkedHashMap<String, String>();
        for (BareMetalCloudType type : BareMetalCloudType.values()) {
        	types.put(type.toString(), type.getTitle());
        }
        datas.put("types", types);
        
        // 配置
        datas.put("configs", beans);
		return ResponseBean.success(datas);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
    @ResponseBody
	public ResponseBean quotaSave(@RequestBody UpOrderBareMetalCloudBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		
		AssertUtil.check(bean.getCpuCacheM() != null && StringUtils.isNotEmpty(bean.getCpuClockSpeedGHz()) && bean.getCpuNum()!=null, "请完善CPU信息");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getDisk()) && StringUtils.isNotEmpty(bean.getDiskType()) && bean.getDiskNum() != null, "请完善存储信息");
		AssertUtil.check(bean.getMemoryGB(), "请完善内存信息");
		
		AssertUtil.check(bean.getTemplateId(), "请选择镜像");
		
		AssertUtil.check(StringUtils.isNotEmpty(bean.getProductCode()), "非法产品编码");
		AssertUtil.check(bean.getType(), "未选择类型");
		
		AssertUtil.check(StringUtils.isNotEmpty(bean.getHostName()), "请输入主机名");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getPassword()), "请输入密码");
		
		return ResponseBean.success(true);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
	public ResponseBean save(@RequestBody UpOrderBareMetalCloudBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());

		AssertUtil.check(bean.getCpuCacheM() != null && StringUtils.isNotEmpty(bean.getCpuClockSpeedGHz()) && bean.getCpuNum()!=null, "请完善CPU信息");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getDisk()) && StringUtils.isNotEmpty(bean.getDiskType()) && bean.getDiskNum() != null, "请完善存储信息");
		AssertUtil.check(bean.getMemoryGB(), "请完善内存信息");
		
		AssertUtil.check(bean.getTemplateId(), "请选择镜像");
		AssertUtil.check(bean.getOwnerId(), "请选择所有者");
		
		AssertUtil.check(StringUtils.isNotEmpty(bean.getProductCode()), "非法产品编码");
		AssertUtil.check(bean.getType(), "未选择类型");
		
		AssertUtil.check(StringUtils.isNotEmpty(bean.getHostName()), "请输入主机名");
		AssertUtil.check(StringUtils.isNotEmpty(bean.getPassword()), "请输入密码");
		
		return ResponseBean.success(true);
	}
}