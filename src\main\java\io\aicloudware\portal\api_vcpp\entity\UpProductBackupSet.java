package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBackupSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.VTBackupSchedFreq;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.VTBackupSchedRet;

@Entity
@Table(name = "up_product_backup_set")
@Access(AccessType.FIELD)
public class UpProductBackupSet extends BaseUpEntity<UpProductBackupSetBean> {

	private static final long serialVersionUID = 3821892672220703882L;

	@Column(name = "product_code")
	private String productCode;
	
	@Column(name = "payment_type")
	private String paymentType;

	@Column(name = "unit")
	private Integer unit;
	
	@Column(name = "price")
	private BigDecimal price;
	
	@Column(name = "enabled")
	private Boolean enabled;

	@Column(name = "sched_freq")
    @Enumerated(EnumType.STRING)
	private VTBackupSchedFreq schedFreq;
	
	@Column(name = "sched_ret")
    @Enumerated(EnumType.STRING)
	private VTBackupSchedRet schedRet;

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getUnit() {
		return unit;
	}

	public void setUnit(Integer unit) {
		this.unit = unit;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public VTBackupSchedFreq getSchedFreq() {
		return schedFreq;
	}

	public void setSchedFreq(VTBackupSchedFreq schedFreq) {
		this.schedFreq = schedFreq;
	}

	public VTBackupSchedRet getSchedRet() {
		return schedRet;
	}

	public void setSchedRet(VTBackupSchedRet schedRet) {
		this.schedRet = schedRet;
	}

}
