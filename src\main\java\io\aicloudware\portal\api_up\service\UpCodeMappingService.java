package io.aicloudware.portal.api_up.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.aicloudware.portal.api_up.entity.UpCodeMapping;
import io.aicloudware.portal.framework.sdk.contants.UpCodeMappingType;
import io.aicloudware.portal.framework.service.BaseService;

@Service
@Transactional
public class UpCodeMappingService extends BaseService implements IUpCodeMappingService {
    @Override
    public void initDefaultDataForTenant() {
        UpCodeMapping codeMapping = new UpCodeMapping();
        codeMapping.setName("默认");
        codeMapping.setType(UpCodeMappingType.app_system_type);
        codeMapping.setDescription("项目类别：默认");
        dao.insert(codeMapping);
/*
        codeMapping = new UpCodeMapping();
        codeMapping.setUpTenant(new UpTenant(tenantId));
        codeMapping.setName("测试");
        codeMapping.setType(UpCodeMappingType.app_system_type);
        codeMapping.setDescription("测试类项目");
        dao.insert(codeMapping);
        codeMapping = new UpCodeMapping();
        codeMapping.setUpTenant(new UpTenant(tenantId));
        codeMapping.setName("生产");
        codeMapping.setType(UpCodeMappingType.app_system_type);
        codeMapping.setDescription("生产类项目");
        dao.insert(codeMapping);
*/
    }
}
