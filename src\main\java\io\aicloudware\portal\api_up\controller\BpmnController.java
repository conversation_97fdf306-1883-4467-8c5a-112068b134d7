package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.utility.FileUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/bpmn")
public class BpmnController {

    @RequestMapping(value = "/getTest", method = RequestMethod.GET)
    @ApiOperation(notes = "/getTest", httpMethod = "GET", value = "获取BPMN流程定义")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回BPMN对象", response = Object.class)})
    @ResponseBody
    public ResponseBean getTest() {
        try {
            // 使用 Camunda BpmnModelInstance 创建标准 BPMN 对象
            BpmnModelInstance modelInstance = Bpmn.createEmptyModel();
            Definitions definitions = modelInstance.newInstance(Definitions.class);
            definitions.setTargetNamespace("http://camunda.org/examples");
            modelInstance.setDefinitions(definitions);
            
            // 创建流程
            org.camunda.bpm.model.bpmn.instance.Process process = modelInstance.newInstance(org.camunda.bpm.model.bpmn.instance.Process.class);
            process.setId("sample-process");
            process.setExecutable(true);
            definitions.addChildElement(process);
            
            // 创建开始事件
            StartEvent startEvent = modelInstance.newInstance(StartEvent.class);
            startEvent.setId("start");
            startEvent.setName("流程开始");
            process.addChildElement(startEvent);
            
            // 创建用户任务
            UserTask userTask = modelInstance.newInstance(UserTask.class);
            userTask.setId("userTask");
            userTask.setName("用户审批");
            process.addChildElement(userTask);
            
            // 创建结束事件
            EndEvent endEvent = modelInstance.newInstance(EndEvent.class);
            endEvent.setId("end");
            endEvent.setName("流程结束");
            process.addChildElement(endEvent);
            
            // 创建序列流
            SequenceFlow flow1 = modelInstance.newInstance(SequenceFlow.class);
            flow1.setId("flow1");
            flow1.setSource(startEvent);
            flow1.setTarget(userTask);
            process.addChildElement(flow1);
            
            SequenceFlow flow2 = modelInstance.newInstance(SequenceFlow.class);
            flow2.setId("flow2");
            flow2.setSource(userTask);
            flow2.setTarget(endEvent);
            process.addChildElement(flow2);
            
            // 验证模型
            Bpmn.validateModel(modelInstance);
            
            // 转换为 XML 字符串
            String bpmnXml = Bpmn.convertToString(modelInstance);
            
            // 创建返回对象
            Map<String, Object> result = new HashMap<>();
            result.put("processDefinitionKey", "sample-process");
            result.put("bpmnXml", bpmnXml);
            result.put("modelInstance", modelInstance);
            
            return ResponseBean.success(result);
            
        } catch (Exception e) {
            return ResponseBean.error(500, "BPMN创建失败", e.getMessage());
        }
    }
}
