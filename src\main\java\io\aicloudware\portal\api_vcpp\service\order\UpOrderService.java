package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpTaskService;
import io.aicloudware.portal.api_vcpp.entity.*;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductBackupSetBean;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductSnapshotSetBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CatalogType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.ProductType;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaCatalog;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums.QuotaDetailStatus;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.*;
import io.aicloudware.portal.platform_vcd.entity.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class UpOrderService extends BaseService implements IUpOrderService {

	protected final static Logger logger = Logger.getLogger(UpOrderService.class);

	@Autowired
    private IUpOrderQuotaService orderQuotaService;

	@Autowired
	private IUpTaskService upTaskService;
	
	@Override
	// TODO  region
	public Map<CatalogType,List<Map<String,Object>>> getTemplatesByCatalog(CatalogType... types) {
		Map<CatalogType,List<Map<String,Object>>> map = new LinkedHashMap<>();
		for(CatalogType type : types) {
			List<SpCatalog> catalogs = this.dao.list(SpCatalog.class, "name", type.toString());
	        if(catalogs == null || catalogs.size() == 0) {
	        	continue;
	        }
	        List<SpVappTemplate> entitys = this.dao.list(SpVappTemplate.class, "catalog", catalogs.get(0));
			List<Map<String,Object>> templates = new ArrayList<>();
			for(SpVappTemplate entity : entitys) {
				Map<String,Object> item = new HashMap<>();
				item.put("catalogId", entity.getId());
				item.put("name", entity.getDisplayName());
				item.put("systemDiskGB", entity.getDiskSize()/1024L/1024L/1024L);
				templates.add(item);
				map.put(type, templates);
			}
		}
		return map;
	}
	
	@Override
	public List<Map<String,Object>> getTemplatesByCatalog(SpRegionEntity region, Integer orgId, CatalogType... types) {
		List<Map<String,Object>> list = new ArrayList<>();
		for(CatalogType type : types) {
			if(type == CatalogType.local) {
				SpOrg privateOrg = this.dao.load(SpOrg.class, orgId);
				collectTemplate(region, type, privateOrg, list);
			}else {
//				List<SpOrg> publicOrgs = this.dao.list(SpOrg.class, "name", "Public");
//		        if(publicOrgs == null || publicOrgs.size() == 0) {
//		        	continue;
//		        }
//		        SpOrg publicOrg = publicOrgs.get(0);
		        collectTemplate(region, type, null, list);
			}
		}
		return list;
	}
	
	private void collectTemplate(SpRegionEntity region, CatalogType type, SpOrg org, List<Map<String,Object>> list) {
//		List<SpCatalog> catalogs = this.dao.list(SpCatalog.class, MapUtil.of("name", type.toString(), "spOrg", org));

		List<SpVappTemplate> entitys = this.dao.list(SpVappTemplate.class, "region", region);
		for (SpVappTemplate entity : entitys) {
			Map<String, Object> item = new HashMap<>();
			item.put("catalogId", entity.getId());
			item.put("name", entity.getDisplayName());
			item.put("systemDiskGB", 40);
			list.add(item);
		}

	}
	
	@Override
	public List<SpVmBean> queryUnbindSpVm(Integer userId) {
		UpUser user = this.dao.load(UpUser.class, userId);
		String sql = "select id,name,sp_uuid,ip_address from sp_vm t where t.region_id = "+ThreadCache.getRegion().getId()+" and t.vm_type = '" + SpVmType.iaas + "' and t.deploy_status = '" + SpDeployStatus.COMPLETE + "' and t.sp_org_id = " + user.getOrg().getId()
				+ " and t.status <> '" + RecordStatus.deleted + "' and not exists (select 1 from sp_ip_binding e where t.id = e.vm_id and e.status = '" + RecordStatus.active + "')"
				+ " and not exists (select 1 from sp_load_balancer_member m where m.status='"+RecordStatus.active + "' and m.sp_org_id=t.sp_org_id and t.ip_address=m.ip_address)";
		List<SpVmBean> list = new ArrayList<>();
		List<Object[]> datas = queryDao.querySql(sql, null);
		if (datas != null && datas.size() > 0) {
			for (Object[] data : datas) {
				SpVmBean bean = new SpVmBean();
				bean.setId((Integer) data[0]);
				bean.setName((String) data[1]);
				bean.setSpUuid((String) data[2]);
				bean.setIpAddress((String) data[3]);
				list.add(bean);
			}
		}
		return list;
	}
	
	@Override
	public List<SpVmBean> querySpVm(Integer userId) {
		UpUser user = this.dao.load(UpUser.class, userId);
		SpVm params = new SpVm();
		params.setSpOrg(user.getOrg());
		params.setDeployStatus(SpDeployStatus.COMPLETE);
		params.setRegion(ThreadCache.getRegion());
		SpVmSearchBean search = new SpVmSearchBean();
		search.setPageSize(Integer.MAX_VALUE);
		return ListUtil.toList(dao.query(search, params), (vmList, vm) -> {
			SpVmBean item = new SpVmBean();
			item.setId(vm.getId());
			item.setName(vm.getName());
			vmList.add(item);
		});
	}

	@Override
	public List<SpLoadBalancerVirtualServerBean> queryUnbindLoadbalance(Integer userId) {
		UpUser user = this.dao.load(UpUser.class, userId);
		String sql = "select id,name from sp_load_balancer_virtual_server t where t.region_id = "+ThreadCache.getRegion().getId()+" and t.sp_org_id = " + user.getOrg().getId() + " and t.status <> '" + RecordStatus.deleted
				+ "' and not exists (select 1 from sp_ip_binding e where t.id = e.virtual_server_id and e.status = '" + RecordStatus.active + "')";
		List<SpLoadBalancerVirtualServerBean> list = new ArrayList<>();
		List<Object[]> datas = queryDao.querySql(sql, null);
		if (datas != null && datas.size() > 0) {
			for (Object[] data : datas) {
				SpLoadBalancerVirtualServerBean bean = new SpLoadBalancerVirtualServerBean();
				bean.setId((Integer) data[0]);
				bean.setName((String) data[1]);
				list.add(bean);
			}
		}
		return list;
	}

	@Override
	public List<Map<String, Object>> getVPC(Integer userId) {

		List<SpVPC> entitys = new ArrayList<>();
		
		SpOrg org = this.dao.load(UpUser.class, userId).getOrg();
		if(org != null) {
			entitys = this.dao.list(SpVPC.class, MapUtil.of("spOrg", org, "region", ThreadCache.getRegion()));
		}
		
		List<Map<String, Object>> vpc = new ArrayList<>();
		for (SpVPC entity : entitys) {
			Map<String, Object> map = new HashMap<>();
			map.put("id", entity.getId());
			map.put("name", entity.getDisplayName());

			List<Map<String, Object>> networks = new ArrayList<>();

				dao.list(SpOVDCNetwork.class, MapUtil.of("vpc", entity, "region", ThreadCache.getRegion())).forEach(network -> {
					if (network.getStatus() != RecordStatus.active) {
						return;
					}
					Map<String, Object> networkItem = new HashMap<>();
					networkItem.put("id", network.getId());
					networkItem.put("name", network.getDisplayName());
					networkItem.put("segment", "");// entity.getNetworkSegment());
					networks.add(networkItem);
				});

			map.put("network", networks);
			vpc.add(map);
		}
		return vpc;
	}

	@Override
	@Deprecated
	public List<SpElasticIpBean> queryUnbindIp(Integer userId) {
		UpUser user = this.dao.load(UpUser.class, userId);
		String sql = "select id,name from sp_elastic_ip t where t.sp_org_id = " + user.getOrg().getId() + " and t.status <> '" + RecordStatus.deleted
				+ "' and not exists (select 1 from sp_ip_binding e where t.id = e.elastic_ip_id and e.status = '" + RecordStatus.active + "')"
				+ " and not exists (select 1 from sp_load_balancer_virtual_server lb where lb.ip_address = t.ip_address and lb.status = '" + RecordStatus.active + "' and lb.sp_org_id = "
				+ user.getOrg().getId() + ")" + " order by t.ip_address";
		List<SpElasticIpBean> list = new ArrayList<>();
		List<Object[]> datas = queryDao.querySql(sql, null);
		if (datas != null && datas.size() > 0) {
			for (Object[] data : datas) {
				SpElasticIpBean bean = new SpElasticIpBean();
				bean.setId((Integer) data[0]);
				bean.setName((String) data[1]);
				list.add(bean);
			}
		}
		return list;
	}

	@Override
	@Deprecated
	public Integer createSnapshotOrder(SpSnapshot snapshot, UpProductSnapshotSetBean snapshotSetBean) {
		SpVm vm = snapshot.getSpVm();
        if (vm.getOrder() == null) {
            return null;
        }
		List<SpVmDisk> disks = vm.getDiskList();
		Integer diskNum = 0;
		for (SpVmDisk disk : disks) {
			diskNum += disk.getDiskGB();
		}
		UpOrder order = new UpOrder();
		order.setType(OrderType.vm_snapshot_create);
		order.setName("[" + OrderType.vm_snapshot_create + "]" + snapshot.getName());
		order.setOrderStatus(OrderStatus.close_success);
		order.setOwner(vm.getOrder().getOwner());
		order.setPaymentType(PaymentType.postpayment);
		order.setSnapshotNum(diskNum);
		order.setSnapshotPrice(snapshotSetBean.getPrice().multiply(BigDecimal.valueOf(diskNum)));
		order.setSnapshotSet(BeanCopyUtil.copy(snapshotSetBean, UpProductSnapshotSet.class));
//		order.setSpOrg(snapshot.getSpOrg());
		order.setNumber(1);
		this.dao.insert(order);
		snapshot.setOrder(order);
		this.dao.update(snapshot, "order");
		return order.getId();
	}

	@Override
	@Deprecated
	public Integer createBackupOrder(SpVm vm, UpProductBackupSetBean backupSetBean) {
		if (!vm.getVmType().equals(SpVmType.backup)) {
			return null;
		}
//		dao.flush();
		vm = this.dao.load(SpVm.class, vm.getId());
		if (vm.getOrder() == null) {
		    return null;
		}
		
		List<SpVmDisk> disks = vm.getDiskList();
		Integer diskNum = 0;
		for (SpVmDisk disk : disks) {
			diskNum += disk.getDiskGB();
		}
		UpOrder order = new UpOrder();
		order.setType(OrderType.vm_backup_manual_add);
		order.setName("[" + OrderType.vm_backup_manual_add + "]" + vm.getName() + "-" + System.currentTimeMillis());
		order.setOrderStatus(OrderStatus.close_success);
		order.setOwner(vm.getOrder().getOwner());
		order.setPaymentType(PaymentType.postpayment);
		order.setBackupNum(diskNum);
		order.setBackupPrice(backupSetBean.getPrice().multiply(BigDecimal.valueOf(diskNum)));
		order.setBackupSet(BeanCopyUtil.copy(backupSetBean, UpProductBackupSet.class));
//		order.setSpOrg(vm.getSpOrg());
		order.setNumber(1);
		this.dao.insert(order);
		vm.setOrder(order);
		this.dao.update(vm, "order");
		return order.getId();
	}

	@Override
	public Integer queryActiveOrder(OrderType type, Integer userId) {
		// TODO  测试阶段，不校验申请单重复提交
		if(true) {
			return 0;
		}
		
		if (type == null) {
			return 0;
		}
		String hql = "select count(1) from UpOrder where region.id = "+ThreadCache.getRegion().getId()+" and owner.id = " + userId + " and type = '" + type + "' and orderStatus not in ('" + OrderStatus.close_success + "','" + OrderStatus.close_error
				+ "','" + OrderStatus.deploy_failed + "') and status = '" + RecordStatus.active + "'";
		List<Long> datas = queryDao.queryHql(hql, null);
		if (datas != null && datas.size() > 0) {
			return datas.get(0).intValue();
		} else if (datas == null) {
			AssertUtil.check(false, "系统异常，请联系客服！");
		}
		return 0;
	}
	
	@Override
	public Integer createSimpleOrderCloudServer(SpVm[] vms, OrderType type) {
		if(vms == null || vms.length == 0 || type == null) {
			return null;
		}
		String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		if(vms[0].getOrder()!=null) {
			owner = vms[0].getOrder().getOwner();
		}
		
		UpOrder order = new UpOrder();
		order.setRegion(vms[0].getRegion());
		order.setType(type);
		if(vms.length == 1) {
			order.setName("[" + type + "]" + vms[0].getName());
		}else {
			order.setName("[" + type + "] Create Order @" + date);
		}
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(owner);
		order.setApplyUser(applyUser);
		order.setSpOrg(owner.getOrg());
        order.setNumber(vms.length);
        this.dao.insert(order);

        List<UpOrderCloudServer> orderCloudServerList = new ArrayList<>();
        for (SpVm vm : vms) {
            vm = this.dao.load(SpVm.class, vm.getId());
            UpOrderCloudServer cloudOrderServer = new UpOrderCloudServer();
            cloudOrderServer.setRegion(order.getRegion());
            cloudOrderServer.setVm(vm);
            cloudOrderServer.setOwner(owner);
            cloudOrderServer.setSpOrg(vm.getSpOrg());
            cloudOrderServer.setName("[" + type + "]  VM Name: " + vm.getName() + "-" + date);
            cloudOrderServer.setOrder(order);
            orderCloudServerList.add(cloudOrderServer);
        }
        this.dao.insert(orderCloudServerList);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(type);
        task.setSpOrg(owner.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(order.getRegion());
        dao.insert(task);

        return order.getId();
    }

    @Override
    public Integer createSimpleOrderSnapshot(SpSnapshot[] snapshots, OrderType type) {
        if (snapshots == null || snapshots.length == 0 || type == null) {
            return null;
        }
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		if(snapshots[0].getSpVm().getOrder()!=null) {
			owner = snapshots[0].getSpVm().getOrder().getOwner();
		}
		
        UpOrder order = new UpOrder();
        order.setRegion(snapshots[0].getSpVm().getRegion());
        order.setType(type);
        if(snapshots.length == 1) {
        	order.setName("[" + type + "]" + snapshots[0].getName());
        }else {
        	order.setName("[" + type + "] Create Order @" + date);
        }
        order.setOrderStatus(OrderStatus.pending_deploy);
        order.setOwner(owner);
        order.setApplyUser(applyUser);
        order.setSpOrg(owner.getOrg());
        order.setNumber(snapshots.length);
        this.dao.insert(order);

        List<UpOrderSnapshot> orderSnapshotList = new ArrayList<>();
        for (SpSnapshot snapshot : snapshots) {
            SpVm vm = snapshot.getSpVm();
            
            UpOrderSnapshot orderSnapshot = new UpOrderSnapshot();
            orderSnapshot.setRegion(order.getRegion());
            if (snapshot.getId() != null) {
                orderSnapshot.setSnapshot(snapshot);
            }
            orderSnapshot.setVm(vm);
            orderSnapshot.setOwner(owner);
            orderSnapshot.setSpOrg(vm.getSpOrg());
            orderSnapshot.setName("[" + type + "] VM Name: " + vm.getName() + (snapshot.getId() == null ? "" : (" by Snapshot_id: " + snapshot.getId())) + "-" + date);
            orderSnapshot.setOrder(order);
            orderSnapshot.setSnapshotName(snapshot.getName());
            orderSnapshot.setSnapshotComments(snapshot.getComments());
            orderSnapshotList.add(orderSnapshot);
        }
        this.dao.insert(orderSnapshotList);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(type);
        task.setSpOrg(owner.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(order.getRegion());
        dao.insert(task);
        return order.getId();
    }

    @Override
    public Integer createSimpleOrderBackup(SpVm[] backups, OrderType type, SpVmBackupStrategy backupStrategy) {
        if (backups == null || backups.length == 0 || type == null) {
            return null;
        }

        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		if(backups[0].getBackupVm().getOrder()!=null) {
			owner = backups[0].getBackupVm().getOrder().getOwner();
		}
        UpOrder order = new UpOrder();
        order.setRegion(backups[0].getBackupVm().getRegion());
        order.setType(type);
        order.setName("[" + type + "] Create Order  @" + date
                + (backupStrategy == null ? "" : (" by backup strategy:" + backupStrategy.getName() + "/" + backupStrategy.getId())));
        order.setOrderStatus(OrderStatus.pending_deploy);
        order.setOwner(owner);
        order.setApplyUser(applyUser);
        order.setSpOrg(owner.getOrg());
        order.setNumber(backups.length);
        this.dao.insert(order);

        List<UpOrderBackupServer> orderBackupList = new ArrayList<>();
        for (SpVm backup : backups) {
            SpVm sourceVm = backup.getBackupVm();

            int maxSequence = 0;
            List<SpVm> vmBackups = dao.list(SpVm.class, "backupVm", sourceVm);
            if (Utility.isNotEmpty(vmBackups)) {
                for (SpVm vmBackup : vmBackups) {
                    if (vmBackup.getBackupSequence() != null && maxSequence < vmBackup.getBackupSequence()) {
                        maxSequence = vmBackup.getBackupSequence();
                    }
                }
            }
            maxSequence++;

            UpOrderBackupServer orderBackup = new UpOrderBackupServer();
            if (backup.getId() != null) {
                orderBackup.setBackupVm(backup);
            }
            orderBackup.setSequence(maxSequence);
            orderBackup.setVm(sourceVm);
            orderBackup.setOwner(owner);
            orderBackup.setSpOrg(sourceVm.getSpOrg());
            orderBackup.setName("[" + type + "] VM Name: " + sourceVm.getName() + (backup.getId() == null ? "" : (" by VmBackup_id: " + backup.getId())) + "-" + date);
            orderBackup.setOrder(order);
            orderBackup.setRegion(order.getRegion());
            orderBackupList.add(orderBackup);
        }
        this.dao.insert(orderBackupList);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(type);
        task.setSpOrg(owner.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setBackupStrategyId(backupStrategy.getId());
        task.setRegion(order.getRegion());
        dao.insert(task);
        return order.getId();
    }

    @Override
	public Integer createSimpleOrderFileStorage(SpFileStorage[] fileStorages, OrderType type) {
		if (fileStorages == null || fileStorages.length == 0 || type == null) {
            return null;
        }
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		if(fileStorages[0].getOrder()!=null) {
			owner = fileStorages[0].getOrder().getOwner();
		}
		
        UpOrder order = new UpOrder();
        order.setRegion(fileStorages[0].getRegion());
        order.setType(type);
        order.setName("[" + type + "] Create Order @" + date);
        order.setOrderStatus(OrderStatus.pending_deploy);
        order.setOwner(owner);
        order.setApplyUser(applyUser);
        order.setSpOrg(owner.getOrg());
        order.setNumber(fileStorages.length);
        this.dao.insert(order);

        List<UpOrderFileStorage> fileStorageList = new ArrayList<>();
        for (SpFileStorage fileStorage : fileStorages) {
            
            UpOrderFileStorage orderFileStorage = new UpOrderFileStorage();
            orderFileStorage.setRegion(order.getRegion());
            orderFileStorage.setSpFileStorage(fileStorage);
            orderFileStorage.setOwner(owner);
            orderFileStorage.setSpOrg(fileStorage.getSpOrg());
            orderFileStorage.setName("[" + type + "] FileStorage Name: " + fileStorage.getName() + " by FileStorage_id: " + fileStorage.getId() + "-" + date);
            orderFileStorage.setOrder(order);
            fileStorageList.add(orderFileStorage);
        }
        this.dao.insert(fileStorageList);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(type);
        task.setSpOrg(owner.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(order.getRegion());
        dao.insert(task);
        return order.getId();
	}

    @Override
	public Integer createSimpleOrderObjectStroageBucket(SpObjectStorageBucket[] objectStorageBuckets, OrderType type) {
    	if (objectStorageBuckets == null || objectStorageBuckets.length == 0 || type == null) {
            return null;
        }
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		SpObjectStorageBucket entity = dao.load(SpObjectStorageBucket.class, objectStorageBuckets[0].getId());
		if(entity.getOrder()!=null) {
			owner = entity.getOrder().getOwner();
		}
		
        UpOrder order = new UpOrder();
        order.setRegion(entity.getRegion());
        order.setType(type);
        order.setName("[" + type + "] Create Order @" + date);
        order.setOrderStatus(OrderStatus.pending_deploy);
        order.setOwner(owner);
        order.setApplyUser(applyUser);
        order.setSpOrg(owner.getOrg());
        order.setNumber(objectStorageBuckets.length);
        this.dao.insert(order);

        List<UpOrderObjectStorageBucket> objectStorageBucketList = new ArrayList<>();
        for (SpObjectStorageBucket bucket : objectStorageBuckets) {
            UpOrderObjectStorageBucket orderBucket = new UpOrderObjectStorageBucket();
            orderBucket.setRegion(order.getRegion());
            orderBucket.setSpObjectStorageBucket(bucket);
            orderBucket.setOwner(owner);
            orderBucket.setSpOrg(entity.getSpOrg());
            orderBucket.setName(bucket.getName());
            orderBucket.setOrder(order);
            objectStorageBucketList.add(orderBucket);
        }
        this.dao.insert(objectStorageBucketList);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(type);
        task.setSpOrg(owner.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(order.getRegion());
        dao.insert(task);
        return order.getId();
	}

	@Override
	public Integer createPrivateImgOrder(SpVm vm, SpCatalog catalog, String imageName) {
//		String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		OrderType type = OrderType.private_image;

		UpOrder order = new UpOrder();
//		order.setRegion(ThreadCache.getRegion());

		List<UpOrderQuotaDetail> quotaDetails = dao.list(UpOrderQuotaDetail.class, MapUtil.of("spOrg", owner.getOrg(), "type", ProductType.IMAGE, "catalog", QuotaCatalog.NEW, "quotaDetailStatus", QuotaDetailStatus.start));
		AssertUtil.check(quotaDetails.size() > 0, "未找到私有镜像创建额度。");
		UpOrderQuotaDetail quotaDetail = quotaDetails.get(0);
		orderQuotaService.deployQuotaDetail(quotaDetail.getId());
		order.setRegion(quotaDetail.getRegion());
		order.setQuota(quotaDetail.getQuota());
		order.setQuotaDetail(quotaDetail);

		order.setType(type);
		order.setName("[" + type + "]" + imageName);
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(owner);
		order.setApplyUser(applyUser);
		order.setSpOrg(owner.getOrg());
		order.setNumber(1);
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quotaDetail.getQuota());
		this.dao.insert(order);

		UpOrderPrivateImage imgOrder =  new UpOrderPrivateImage();
		vm.getSpVapp();
		imgOrder.setVm(vm);
		imgOrder.setName(imageName);
		imgOrder.setCatalog(catalog);
		imgOrder.setSpOrg(owner.getOrg());
		imgOrder.setOrder(order);
		imgOrder.setRegion(order.getRegion());
		this.dao.insert(imgOrder);
		
		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(type);
		task.setSpOrg(owner.getOrg());
		task.setTaskStatus(UpTaskStatus.start);
		task.setRegion(order.getRegion());
		dao.insert(task);
		
//		messageService.createOrderMessage(order);
		return order.getId();
	}

	@Override
	public List<SpLoadBalancerVipBean> queryUnbindLBVip(Integer userId) {
		UpUser user = this.dao.load(UpUser.class, userId);
		String sql = "select id,name from sp_load_balancer_vip t where t.region_id = "+ThreadCache.getRegion().getId()+" and t.sp_org_id = " + user.getOrg().getId() + " and t.status <> '" + RecordStatus.deleted
				 + "' order by t.ip_address";
		List<SpLoadBalancerVipBean> list = new ArrayList<>();
		List<Object[]> datas = queryDao.querySql(sql, null);
		Map<String, Integer> ipMap = new HashMap<>();
		for (Object[] data : datas) {
			ipMap.put((String) data[1], (Integer) data[0]);
		}
		for (int i=10;i<250;i++) {
			String ip = "192.168.254."+i;
			SpLoadBalancerVipBean bean = new SpLoadBalancerVipBean();
			bean.setName(ip);
			if (!ipMap.containsKey(ip)) {
				bean.setId(ipMap.get(ip));
				list.add(bean);
			}
		}
		if (datas != null && datas.size() > 0) {
			for (Object[] data : datas) {
				SpLoadBalancerVipBean bean = new SpLoadBalancerVipBean();
				bean.setId((Integer) data[0]);
				bean.setName((String) data[1]);
				list.add(bean);
			}
		}
		return list;
	}


	@Override
	public Integer createPrivateImageDeleteOrder(SpVappTemplate vappTemplate) {
		String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;


		OrderType type = OrderType.private_image_delete;
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(type);
//		order.setName("[" + type + "] Create Order @" + date);
		order.setName("[" + type + "]" + vappTemplate.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(owner);
		order.setApplyUser(applyUser);
		order.setSpOrg(owner.getOrg());
		order.setNumber(1);
		this.dao.insert(order);

		UpOrderPrivateImage imgOrder =  new UpOrderPrivateImage();
		imgOrder.setRegion(order.getRegion());
//		imgOrder.setName(order.getName());
		imgOrder.setName(owner.getOrg().getId() + "-" + vappTemplate.getName());
		imgOrder.setRegion(order.getRegion());
		imgOrder.setOrigId(vappTemplate.getId());
		imgOrder.setSpOrg(owner.getOrg());
		imgOrder.setOrder(order);
		this.dao.insert(imgOrder);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(type);
		task.setSpOrg(owner.getOrg());
		task.setTaskStatus(UpTaskStatus.start);
		task.setRegion(order.getRegion());
		dao.insert(task);

		return order.getId();
	}

	@Override
	public Integer createSimpleOrderK8sCluster(SpK8sCluster[] clusters, OrderType type) {
		if(clusters == null || clusters.length == 0 || type == null) {
			return null;
		}
		String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		if(clusters[0].getOrder()!=null) {
			owner = clusters[0].getOrder().getOwner();
		}

		UpOrder order = new UpOrder();
		order.setRegion(clusters[0].getRegion());
		order.setType(type);
		if(clusters.length == 1) {
			order.setName("[" + type + "]" + clusters[0].getName());
		}else {
			order.setName("[" + type + "] Create Order @" + date);
		}
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(owner);
		order.setApplyUser(applyUser);
		order.setSpOrg(owner.getOrg());
		order.setNumber(clusters.length);
		this.dao.insert(order);

		List<UpOrderK8sCluster> orderK8sClusterList = new ArrayList<>();
		for (SpK8sCluster k8sCluster : clusters) {
			k8sCluster = this.dao.load(SpK8sCluster.class, k8sCluster.getId());
			UpOrderK8sCluster cloudOrderK8sCluster = new UpOrderK8sCluster();
			cloudOrderK8sCluster.setRegion(order.getRegion());
			cloudOrderK8sCluster.setCluster(k8sCluster);
			cloudOrderK8sCluster.setSpOrg(k8sCluster.getSpOrg());
			cloudOrderK8sCluster.setName("[" + type + "]  K8s Cluster Name: " + k8sCluster.getName() + "-" + date);
			cloudOrderK8sCluster.setOrder(order);
			orderK8sClusterList.add(cloudOrderK8sCluster);
		}
		this.dao.insert(orderK8sClusterList);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(type);
		task.setSpOrg(owner.getOrg());
		task.setTaskStatus(UpTaskStatus.start);
		task.setRegion(order.getRegion());
		dao.insert(task);

		return order.getId();
	}

	@Override
	public Integer createSimpleOrderK8sClusterNodePool(SpK8sClusterNodePool[] nodePools, OrderType type) {
		if (nodePools == null || nodePools.length == 0 || type == null) {
			return null;
		}
		String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		UpUser applyUser = ThreadCache.getUser();
		UpUser owner = applyUser;
		if (nodePools[0].getOrder() != null) {
			owner = nodePools[0].getOrder().getOwner();
		}

		UpOrder order = new UpOrder();
		order.setRegion(nodePools[0].getRegion());
		order.setType(type);
		if (nodePools.length == 1) {
			order.setName("[" + type + "]" + nodePools[0].getName());
		} else {
			order.setName("[" + type + "] Create Order @" + date);
		}
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(owner);
		order.setApplyUser(applyUser);
		order.setSpOrg(owner.getOrg());
		order.setNumber(nodePools.length);
		this.dao.insert(order);


		List<UpOrderK8sClusterNodePool> orderK8sClusterList = new ArrayList<>();
		for (SpK8sClusterNodePool k8sClusterNodePool : nodePools) {
			k8sClusterNodePool = dao.load(SpK8sClusterNodePool.class, k8sClusterNodePool.getId());
			UpOrderK8sClusterNodePool cloudOrderK8sClusterNodePool = new UpOrderK8sClusterNodePool();
			cloudOrderK8sClusterNodePool.setRegion(order.getRegion());
			cloudOrderK8sClusterNodePool.setCluster(k8sClusterNodePool.getK8sCluster());
			cloudOrderK8sClusterNodePool.setNodePool(k8sClusterNodePool);
			cloudOrderK8sClusterNodePool.setSpOrg(k8sClusterNodePool.getSpOrg());
			cloudOrderK8sClusterNodePool.setName("[" + type + "]  NodePool Name: " + k8sClusterNodePool.getName() + "-" + date);
			cloudOrderK8sClusterNodePool.setOrder(order);
			orderK8sClusterList.add(cloudOrderK8sClusterNodePool);
		}
		this.dao.insert(orderK8sClusterList);

		UpTask task = new UpTask();
		task.setName(order.getName());
		task.setType(UpTaskType.up_application);
		task.setOrder(order);
		task.setOrderType(type);
		task.setSpOrg(owner.getOrg());
		task.setTaskStatus(UpTaskStatus.start);
		task.setRegion(order.getRegion());
		dao.insert(task);

		return order.getId();
	}


	@Override
	public UpOrderBean[] query(UpOrderSearchBean searchBean) {
		UpOrder entity = new UpOrder();
		UpOrderBean bean = searchBean.getBean();
		if (bean != null) {
			if(StringUtils.isNotEmpty(bean.getName())) {
				String name = bean.getName();
				UpOrderBean fuzzy = new UpOrderBean();
				fuzzy.setName(name);
				fuzzy.setApplyUserName(name);
				fuzzy.setOwnerName(name);
				fuzzy.setSpOrgName(name);
				searchBean.setFuzzyBean(fuzzy);
			}
			entity.setOrderStatus(bean.getOrderStatus());
			entity.setType(bean.getType());
			searchBean.setBean(new UpOrderBean());

		}
		List<UpOrder> entitys = this.dao.query(searchBean, entity);
		return BeanCopyUtil.copy2BeanList(entitys, UpOrderBean.class);
	}

	@Override
	public void update(UpOrderBean bean) {
		UpOrder entity = dao.load(UpOrder.class, bean.getId());
		entity.setType(bean.getType());
		entity.setOrderStatus(bean.getOrderStatus());
		entity.setName(bean.getName());
		dao.update(entity);
	}

	@Override
	public void redepoly(Integer id) {
		List<UpTask> taskList = dao.list(UpTask.class, "order.id", id);
		UpTask task = taskList.stream().filter(t -> t.getParent() == null).sorted(new Comparator<UpTask>() {
			@Override
			public int compare(UpTask o1, UpTask o2) {
				return o2.getCreateTm().compareTo(o1.getCreateTm());
			}
		}).collect(Collectors.toList()).get(0);
		System.out.println(task.getId());
		upTaskService.redeploy(task.getId());
	}

}