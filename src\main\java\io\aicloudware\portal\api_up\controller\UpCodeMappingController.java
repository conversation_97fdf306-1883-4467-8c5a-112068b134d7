package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpCodeMapping;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpCodeMappingBean;
import io.aicloudware.portal.framework.sdk.bean.UpCodeMappingResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/code_mapping")
@Api(value = "/code_mapping", description = "代码映射表", position = 506)
public class UpCodeMappingController extends BaseUpController<UpCodeMapping, UpCodeMappingBean, UpCodeMappingResultBean> {

//    @Autowired
//    private IUpCodeMappingService codeMappingService;
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpCodeMappingResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryCodeMapping(@ApiParam(value = "查询条件") @RequestBody UpCodeMappingSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpCodeMappingBean.class)})
//    @ResponseBody
//    public ResponseBean getCodeMapping(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
//
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpCodeMappingBean.class)})
//    @ResponseBody
//    public ResponseBean addCodeMapping(@ApiParam(value = "实例对象") @Valid @RequestBody UpCodeMappingBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpCodeMappingListBean.class)})
//    @ResponseBody
//    public ResponseBean addCodeMapping(@ApiParam(value = "实例对象") @Valid @RequestBody UpCodeMappingListBean bean, BindingResult bindingResult) {
//        return addEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpCodeMappingBean.class)})
//    @ResponseBody
//    public ResponseBean updateCodeMapping(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                          @ApiParam(value = "实例对象") @Valid @RequestBody UpCodeMappingBean bean,
//                                          BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpCodeMappingListBean.class)})
//    @ResponseBody
//    public ResponseBean updateCodeMapping(@ApiParam(value = "实例对象") @Valid @RequestBody UpCodeMappingListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteCodeMapping(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteCodeMapping(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }
}
