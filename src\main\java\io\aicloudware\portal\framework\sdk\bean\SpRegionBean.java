package io.aicloudware.portal.framework.sdk.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.SpRecordBean;
import io.aicloudware.portal.framework.sdk.contants.CloudType;
import io.aicloudware.portal.framework.sdk.contants.SpSecurityVersion;
import io.aicloudware.portal.framework.validate.*;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "Region")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpRegionBean.class})
public class SpRegionBean extends SpRecordBean {

    private String title;

    private String code;

    private String securityUrlPort;

    private CloudType type;

    private SpSecurityVersion version;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSecurityUrlPort() {
        return securityUrlPort;
    }

    public void setSecurityUrlPort(String securityUrlPort) {
        this.securityUrlPort = securityUrlPort;
    }

    public CloudType getType() {
        return type;
    }

    public void setType(CloudType type) {
        this.type = type;
    }

    public SpSecurityVersion getVersion() {
        return version;
    }

    public void setVersion(SpSecurityVersion version) {
        this.version = version;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof SpRegionBean){
            return ((SpRegionBean)obj).getName().equals(this.getName());
        }
        if(obj instanceof SpRegionEntity){
            return ((SpRegionEntity)obj).getName().equals(this.getName());
        }
        if(obj instanceof String){
            return ((String)obj).equals(this.getName());
        }
        return false;
    }

    @Override
    public String toString() {
        return getName();
    }
}
