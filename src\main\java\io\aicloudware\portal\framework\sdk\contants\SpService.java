package io.aicloudware.portal.framework.sdk.contants;

public enum SpService {
    ecs("云主机","cloud-server",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create, UpPermissionType.update,
                    UpPermissionType.delete,UpPermissionType.refresh,UpPermissionType.power,UpPermissionType.binding,
                    UpPermissionType.export,UpPermissionType.vnc,UpPermissionType.restore,UpPermissionType.monitor,}
    ),
    evs("云盘","cloud-disk",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,}
            ),
    vpc("VPC","proprietary-network",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,UpPermissionType.binding,
                    }
            ),
    elasticip("弹性公网IP","elastic-pub-network-ip",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,UpPermissionType.binding,
            }
    ),
    securityGroup("安全组","security-group",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,
            }
    ),
    k8s("容器","container",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,UpPermissionType.binding,
                    UpPermissionType.export,UpPermissionType.restore,}
            ),
    redis("Redis","redis",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,UpPermissionType.power,
                    }
            ),
    rds("RDS","rds",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,UpPermissionType.power,
            }
            ),
    msg("消息队列","message-queues",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,
            }
    ),
    kafka("Kafka","kafka",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,
            }
    ),
    rabbitmq("RabbitMQ","rabbitmq",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,
            }
    ),
    fileStorage("文件存储","file-storage",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,
            }
    ),
    objectStorage("对象存储","object-storage",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,
            }
    ),
    vdi("云桌面","vdi",
            new UpPermissionType[]{UpPermissionType.view,UpPermissionType.create,
                    UpPermissionType.delete,UpPermissionType.refresh,
            }
    ),
    ;

    private final String title;
    private final String url;
    private final UpPermissionType[] permissionTypes;

    private SpService(String title, String url, UpPermissionType[] permissionTypes) {
        this.title = title;
        this.url = url;
        this.permissionTypes = permissionTypes;
    }

    public String getTitle() {
        return title;
    }
    public String getUrl(){ return url;}
    public UpPermissionType[] getPermissionTypes() {
        return permissionTypes;
    }
}
