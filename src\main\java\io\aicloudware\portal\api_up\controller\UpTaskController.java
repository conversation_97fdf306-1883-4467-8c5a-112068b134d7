package io.aicloudware.portal.api_up.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.service.IUpTaskService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpTaskBean;
import io.aicloudware.portal.framework.sdk.bean.UpTaskResultBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.utility.MapUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@Controller
@RequestMapping("/task")
@Api(value = "/task", description = "任务", position = 620)
public class UpTaskController extends BaseUpController<UpTask, UpTaskBean, UpTaskResultBean> {

    @Autowired
    private IUpTaskService taskService;

    @RequestMapping(value = "/status/{orderId}", method = RequestMethod.GET)
    @ApiOperation(notes = "/status/{orderId}", httpMethod = "GET", value = "订单ID")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回订单交付状态start(\"开始\"), running(\"运行中\"), finish(\"已完成\"), error(\"运行错误\"), undo(\"未运行\")", response = String.class)})
    @ResponseBody
    public ResponseBean getStatus(@ApiParam(value = "对象ID") @PathVariable Integer orderId) {
    	Integer orgId = ThreadCache.getOrgId();
    	return ResponseBean.success(MapUtil.of(orderId, taskService.getStatus(orgId, orderId)));
    }
    
    @RequestMapping(value = "/incomleted/{orderType}", method = RequestMethod.GET)
    @ApiOperation(notes = "/incomleted/{orderType}", httpMethod = "GET", value = "未完成的任务")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回订单交付状态start(\"开始\"), running(\"运行中\"), finish(\"已完成\"), error(\"运行错误\"), undo(\"未运行\")", response = String.class)})
    @ResponseBody
    public ResponseBean getStatus(@ApiParam(value = "对象ID") @PathVariable OrderType orderType) {
    	Integer orgId = ThreadCache.getOrgId();
    	return ResponseBean.success(taskService.getIncompletedTask(orgId, orderType));
    }
    
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpTaskBean.class)})
//    @ResponseBody
//    public ResponseBean getTask(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }

//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteTask(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteTask(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }
}
