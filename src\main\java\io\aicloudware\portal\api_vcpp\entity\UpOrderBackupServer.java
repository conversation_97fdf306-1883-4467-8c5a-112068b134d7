package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

@Entity
@Table(name = "up_order_backup_server")
@Access(AccessType.FIELD)
public class UpOrderBackupServer extends UpOrderProduct<UpOrderCloudServerBean> implements IOrderEntity{
    
    @Column(name = "sequence")
    private Integer sequence;
    
    @Column(name = "task_sequence")
    private Integer taskSequence;
    
    @JoinColumn(name = "cloud_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;
    
    @JoinColumn(name = "backup_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm backupVm;
    
	@JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;
	
	@JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;
	
	@JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Integer getTaskSequence() {
        return taskSequence;
    }

    public void setTaskSequence(Integer taskSequence) {
        this.taskSequence = taskSequence;
    }

    public SpVm getVm() {
        return vm;
    }

    public void setVm(SpVm vm) {
        this.vm = vm;
    }
    
    public SpVm getBackupVm() {
        return backupVm;
    }

    public void setBackupVm(SpVm backupVm) {
        this.backupVm = backupVm;
    }

    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public SpOrg getSpOrg() {
        return spOrg;
    }

    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

}
