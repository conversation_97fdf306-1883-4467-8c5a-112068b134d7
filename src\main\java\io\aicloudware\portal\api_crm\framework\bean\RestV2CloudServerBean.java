package io.aicloudware.portal.api_crm.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.KeyType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisNodeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ServerType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "云服务器")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, RestV2CloudServerBean.class})
public class RestV2CloudServerBean extends RecordBean {
//
//	@ApiModelProperty(value = "Quota code")
//	private String doorOrderItemId;

	@ApiModelProperty(value = "云主机类型")
	private ServerType serverType;

//	@ApiModelProperty(value = "CPU")
//	private Integer cpu;
//
//	@ApiModelProperty(value = "内存")
//	private Integer memory;
//
//	@ApiModelProperty(value = "系统盘")
//	private Integer diskG;
	
	@ApiModelProperty(value = "镜像ID")
	private Integer imageId;
	
	@ApiModelProperty(value = "磁盘")
	private UpOrderCloudDiskBean[] cloudDiskList;
	
	@ApiModelProperty(value = "专有网络")
	private Integer vpcId;
	
	@ApiModelProperty(value = "子网")
	private Integer networkId;

	@ApiModelProperty(value = "登录账号")
	private String account;
	
	@ApiModelProperty(value = "密码")
	private String password;
	
	@ApiModelProperty(value = "主机名")
	private String hostname;

	@ApiModelProperty(value = "订单ID")
	private Integer orderId;
	
	@ApiModelProperty(value = "组织名称")
	private String spOrgName;
	
	@ApiModelProperty(value = "组织ID")
	private Integer spOrgId;
	
	@ApiModelProperty(value = "Redis类型")
	private RedisType redisType;
	
	@ApiModelProperty(value = "Redis节点类型")
	private RedisNodeType redisNodeType;
	
	@ApiModelProperty(value = "分片数")
	private Integer sharding;
	
	// 虚机变更使用
	@ApiModelProperty(value = "变更vmId")
	private Integer vmId;

	@ApiModelProperty(value = "密钥方式")
	private KeyType keyType;
	
	// redis rds 变更使用
	@ApiModelProperty(value = "变更vappId")
	private Integer updateVappId;

	public ServerType getServerType() {
		return serverType;
	}

	public void setServerType(ServerType serverType) {
		this.serverType = serverType;
	}

//	public Integer getCpu() {
//		return cpu;
//	}
//
//	public void setCpu(Integer cpu) {
//		this.cpu = cpu;
//	}
//
//	public Integer getMemory() {
//		return memory;
//	}
//
//	public void setMemory(Integer memory) {
//		this.memory = memory;
//	}
//
//	public Integer getDiskG() {
//		return diskG;
//	}
//
//	public void setDiskG(Integer diskG) {
//		this.diskG = diskG;
//	}

	public Integer getImageId() {
		return imageId;
	}

	public void setImageId(Integer imageId) {
		this.imageId = imageId;
	}

	public UpOrderCloudDiskBean[] getCloudDiskList() {
		return cloudDiskList;
	}

	public void setCloudDiskList(UpOrderCloudDiskBean[] cloudDiskList) {
		this.cloudDiskList = cloudDiskList;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Integer networkId) {
		this.networkId = networkId;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getHostname() {
		return hostname;
	}

	public void setHostname(String hostname) {
		this.hostname = hostname;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getSpOrgName() {
		return spOrgName;
	}

	public void setSpOrgName(String spOrgName) {
		this.spOrgName = spOrgName;
	}

	public Integer getSpOrgId() {
		return spOrgId;
	}

	public void setSpOrgId(Integer spOrgId) {
		this.spOrgId = spOrgId;
	}

	public RedisType getRedisType() {
		return redisType;
	}

	public void setRedisType(RedisType redisType) {
		this.redisType = redisType;
	}

	public RedisNodeType getRedisNodeType() {
		return redisNodeType;
	}

	public void setRedisNodeType(RedisNodeType redisNodeType) {
		this.redisNodeType = redisNodeType;
	}

	public Integer getSharding() {
		return sharding;
	}

	public void setSharding(Integer sharding) {
		this.sharding = sharding;
	}

	public Integer getVmId() {
		return vmId;
	}

	public void setVmId(Integer vmId) {
		this.vmId = vmId;
	}

	public Integer getUpdateVappId() {
		return updateVappId;
	}

	public void setUpdateVappId(Integer updateVappId) {
		this.updateVappId = updateVappId;
	}

	public KeyType getKeyType() {
		return keyType;
	}

	public void setKeyType(KeyType keyType) {
		this.keyType = keyType;
	}

//	public String getDoorOrderItemId() {
//		return doorOrderItemId;
//	}
//
//	public void setDoorOrderItemId(String doorOrderItemId) {
//		this.doorOrderItemId = doorOrderItemId;
//	}
}
