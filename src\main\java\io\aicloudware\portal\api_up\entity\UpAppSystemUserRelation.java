package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpAppSystemUserRelationBean;
import io.aicloudware.portal.framework.sdk.contants.SpOrgStatus;
import io.aicloudware.portal.framework.sdk.contants.UpAppSystemUserRelationType;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

import javax.persistence.*;

@Entity
@Table(name = "up_app_system_user_relation")
@Access(AccessType.FIELD)
public class UpAppSystemUserRelation extends BaseUpEntity<UpAppSystemUserRelationBean> {

    @JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg org;

//    @JoinColumn(name = "app_system_id")
//    @ManyToOne(fetch = FetchType.LAZY)
//    private UpAppSystem appSystem;

    @JoinColumn(name = "user_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser user;

    @Column(name = "relation_type")
    @Enumerated(EnumType.STRING)
    private UpAppSystemUserRelationType relationType;

    public SpOrg getOrg() {
        return org;
    }

    public void setOrg(SpOrg org) {
        this.org = org;
    }

//    public UpAppSystem getAppSystem() {
//        return appSystem;
//    }
//
//    public void setAppSystem(UpAppSystem appSystem) {
//        this.appSystem = appSystem;
//    }

    public UpUser getUser() {
        return user;
    }

    public void setUser(UpUser user) {
        this.user = user;
    }

    public UpAppSystemUserRelationType getRelationType() {
        return relationType;
    }

    public void setRelationType(UpAppSystemUserRelationType relationType) {
        this.relationType = relationType;
    }
}
