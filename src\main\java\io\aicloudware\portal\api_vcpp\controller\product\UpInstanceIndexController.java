package io.aicloudware.portal.api_vcpp.controller.product;

import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.product.IUpInstanceIndexService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpInstanceIndexBean;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.framework.sdk.contants.SpServiceCatalog;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/instanceIndex")
public class UpInstanceIndexController extends BaseController {
    @Autowired
    private IUpInstanceIndexService upInstanceIndexService;

    @RequestMapping(value = "/init1", method = RequestMethod.GET)
    @ResponseBody
    public ResponseBean init1() {
        upInstanceIndexService.sync();
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/init", method = RequestMethod.GET)
    @ResponseBody
    public ResponseBean init() {
        Map<String, Object> initMap = new HashMap<>();
        initMap.put("serviceTypeList", Arrays.stream(SpService.values()).map(type -> MapUtil.of("key", type.name(), "value", type.getTitle())).collect(Collectors.toList()));
        initMap.put("serviceTypeMap", Arrays.stream(SpService.values()).collect(Collectors.toMap(SpService::name, SpService::getTitle)));
        initMap.put("serviceCatalogList", Arrays.stream(SpServiceCatalog.values()).map(catalog -> MapUtil.of("key", catalog.name(), "value", catalog.getTitle(), "types", catalog.getServices())).collect(Collectors.toList()));
        initMap.put("serviceCatalogMap", Arrays.stream(SpServiceCatalog.values()).collect(Collectors.toMap(SpServiceCatalog::name, SpServiceCatalog::getTitle)));
        return ResponseBean.success(initMap);
    }

    @RequestMapping(value = "/sync", method = RequestMethod.GET)
    @ResponseBody
    public ResponseBean sync() {
        upInstanceIndexService.sync(ThreadCache.getOrgId(), ThreadCache.getRegion());
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseBean list(@ApiParam(value = "查询条件") @RequestBody UpInstanceIndexBean bean) {
        return ResponseBean.success(upInstanceIndexService.list(bean));
    }
}
