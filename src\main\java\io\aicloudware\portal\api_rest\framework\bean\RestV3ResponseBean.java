package io.aicloudware.portal.api_rest.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "RestV3请求结果信息")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class RestV3ResponseBean<T> extends BaseBean {

    public static <T> RestV3ResponseBean<T> success(T data) {
        return new RestV3ResponseBean<T>(0, "", "").setData(data);
    }

    public static RestV3ResponseBean success() {
    	return new RestV3ResponseBean(0, "操作成功", "Successful");
    }

    public static RestV3ResponseBean error(int resultCode, String errorMsg, String resultMsgCode) {
        return new RestV3ResponseBean(resultCode, errorMsg, resultMsgCode).setData("");
    }

    @ApiModelProperty(value = "错误代码")
    private Integer resultCode;
    @ApiModelProperty(value = "错误消息")
    private String resultMsg;
    @ApiModelProperty(value = "返回结果数据")
    private T resultData;
    @ApiModelProperty(value = "响应消息编码")
    private String resultMsgCode;

    private RestV3ResponseBean(Integer resultCode, String resultMsg, String resultMsgCode) {
       this.resultCode = resultCode;
       this.resultMsg = resultMsg;
       this.resultMsgCode = resultMsgCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public T getResultData() {
        return resultData;
    }

    public void setResultData(T resultData) {
        this.resultData = resultData;
    }

    public String getResultMsgCode() {
        return resultMsgCode;
    }

    public void setResultMsgCode(String resultMsgCode) {
        this.resultMsgCode = resultMsgCode;
    }

    public <T> T getData(Class<T> type) {
        if (type == null) {
            return null;
        }
        if (String.class.equals(type)) {
            return (T) resultData;
        }
        return (T) resultData;
    }

    public RestV3ResponseBean<T> setData(T resultData) {
        this.resultData = resultData;
        return this;
    }

	public Integer getResultCode() {
		return resultCode;
	}

    @Override
    public String toString(){
        String str = "{resultCode:"+resultCode+",resultMsg:"+resultMsg+",resultData:"+(resultData == null ? "" : resultData)+", resultMsgCode: "+resultMsgCode+"}";
        return str;
//        return "{resultCode:"+resultCode+",resultMsg:"+resultMsg+",resultData:"+resultData == null ? "" : resultData+", resultMsgCode: "+resultMsgCode+"}";
    }
}
