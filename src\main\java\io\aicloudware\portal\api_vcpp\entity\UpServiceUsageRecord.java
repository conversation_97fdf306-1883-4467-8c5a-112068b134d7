package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpServiceUsageRecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanType;
import io.aicloudware.portal.framework.sdk.contants.UpServiceUsgeRecordStatus;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "up_service_usage_record")
@Access(AccessType.FIELD)
public final class UpServiceUsageRecord extends BaseUpEntity<UpServiceUsageRecordBean> {

	@JoinColumn(name = "owner_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpUser owner;

	@JoinColumn(name = "sp_org_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;

	@JoinColumn(name = "service_plan_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpServicePlan upServicePlan;

	@Column(name = "record_status")
	@Enumerated(EnumType.STRING)
	private UpServiceUsgeRecordStatus recordStatus;

	@Column(name = "service_plan_start_time")
	private Date servicePlanStartTime;

	@Column(name = "service_plan_end_time")
	private Date servicePlanEndTime;

//	@Column(name = "cost_explorer")
//	private BigDecimal costExplorer;

	@Column(name = "actual_cost")
	private BigDecimal actualCost;

	@Column(name = "service_plan_type")
	@Enumerated(EnumType.STRING)
	private UpServicePlanType servicePlanType;

	@Column(name = "instance_id")
	private Integer instanceId;

	@Column(name = "instance_name")
	private String instanceName;

	@Column(name = "order_id")
	private Integer orderId;

	@Column(name = "op_order_id")
	private Integer opOrderId;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "parent_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpServiceUsageRecord parent;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "parent")
	@Where(clause = "status!='deleted'")
	@org.hibernate.annotations.OrderBy(clause = "id")
	private List<UpServiceUsageRecord> childList;

	@Column(name = "is_parent")
	private Boolean isParent;

//	@Column(name = "service_plan_subscription")
//	@Enumerated(EnumType.STRING)
//	private UpServicePlanSubscription servicePlanSubscription;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "coupon_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpCoupon coupon;

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

	public UpServicePlan getUpServicePlan() {
		return upServicePlan;
	}

	public void setUpServicePlan(UpServicePlan upServicePlan) {
		this.upServicePlan = upServicePlan;
	}

	public UpServiceUsgeRecordStatus getRecordStatus() {
		return recordStatus;
	}

	public void setRecordStatus(UpServiceUsgeRecordStatus recordStatus) {
		this.recordStatus = recordStatus;
	}

	public Date getServicePlanStartTime() {
		return servicePlanStartTime;
	}

	public void setServicePlanStartTime(Date servicePlanStartTime) {
		this.servicePlanStartTime = servicePlanStartTime;
	}

	public Date getServicePlanEndTime() {
		return servicePlanEndTime;
	}

	public void setServicePlanEndTime(Date servicePlanEndTime) {
		this.servicePlanEndTime = servicePlanEndTime;
	}

//	public BigDecimal getCostExplorer() {
//		return costExplorer;
//	}
//
//	public void setCostExplorer(BigDecimal costExplorer) {
//		this.costExplorer = costExplorer;
//	}

	public BigDecimal getActualCost() {
		return actualCost;
	}

	public void setActualCost(BigDecimal actualCost) {
		this.actualCost = actualCost;
	}

	public UpServicePlanType getServicePlanType() {
		return servicePlanType;
	}

	public void setServicePlanType(UpServicePlanType servicePlanType) {
		this.servicePlanType = servicePlanType;
	}

	public Integer getInstanceId() {
		return instanceId;
	}

	public void setInstanceId(Integer instanceId) {
		this.instanceId = instanceId;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Integer getOpOrderId() {
		return opOrderId;
	}

	public void setOpOrderId(Integer opOrderId) {
		this.opOrderId = opOrderId;
	}

	public UpServiceUsageRecord getParent() {
		return parent;
	}

	public Boolean getIsParent() {
		return isParent;
	}

	public void setIsParent(Boolean isParent) {
		isParent = isParent;
	}

	public void setParent(UpServiceUsageRecord parent) {
		this.parent = parent;
	}

	public List<UpServiceUsageRecord> getChildList() {
		return childList;
	}

	public void setChildList(List<UpServiceUsageRecord> childList) {
		this.childList = childList;
	}

	public UpCoupon getCoupon() {
		return coupon;
	}

	public void setCoupon(UpCoupon serviceDiscount) {
		this.coupon = serviceDiscount;
	}

	public String getInstanceName() {
		return instanceName;
	}

	public void setInstanceName(String instanceName) {
		this.instanceName = instanceName;
	}
}
