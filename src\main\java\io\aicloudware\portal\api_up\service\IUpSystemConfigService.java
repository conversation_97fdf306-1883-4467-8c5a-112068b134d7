package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public interface IUpSystemConfigService {

    public UpSystemConfigBean get(UpSystemConfigKey key);

    public UpSystemConfigListBean query(UpSystemConfigListBean bean);

    public UpSystemConfigListBean save(UpSystemConfigListBean bean);

    public void clearCache();

	public void refreshConfig(UpSystemConfigKey key);

}
