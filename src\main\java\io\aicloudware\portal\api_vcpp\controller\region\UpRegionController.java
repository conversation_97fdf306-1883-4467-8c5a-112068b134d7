package io.aicloudware.portal.api_vcpp.controller.region;

import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.SpRegionBean;
import io.aicloudware.portal.platform_vcd.service.ISpOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.LinkedHashMap;
import java.util.Map;

@Controller
@RequestMapping("/region")
@Api(value = "/region", description = "资源配额", position = 508)
public class UpRegionController extends BaseUpController {

    @Autowired
    private ISpOrgService spTenantService;
	
	@RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(notes = "/list", httpMethod = "GET", value = "查询实例对象列表")
    @ResponseBody
    public ResponseBean regions() {
		Map<String, String> regions = new LinkedHashMap<>();
        for (SpRegionBean region : spTenantService.getRegionList()) {
        	regions.put(region.toString(), region.getTitle());
        }
    	return ResponseBean.success(regions);
    }
}
