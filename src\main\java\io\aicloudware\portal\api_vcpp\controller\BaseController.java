package io.aicloudware.portal.api_vcpp.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import io.aicloudware.portal.framework.remote.RemoteHost;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.Logger;

@Controller
public abstract class BaseController {

    protected final Logger logger = Logger.getLogger(getClass());

    @Value("#{'${ip_white_list}'.split(',')}")
    protected List<String> ipWhiteList;
    
	protected RemoteHost getLoginRemoteHost(HttpServletRequest request) {
        RemoteHost remoteHost = (RemoteHost) request.getSession(true).getAttribute("RemoteHost");
        AssertUtil.check(remoteHost != null, "用户未登录或登录已超时");
        return remoteHost;
    }
    
    @Autowired
    protected ICommonService commonService;

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(false);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    protected boolean isDeveloper(HttpServletRequest request) {
        Boolean developer = (Boolean) request.getSession(true).getAttribute("Developer");
        return !Boolean.FALSE.equals(developer);
    }
}
