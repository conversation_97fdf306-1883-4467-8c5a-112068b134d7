package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.hibernate.EntityProperty;
import io.aicloudware.portal.framework.sdk.bean.UpCouponBean;
import io.aicloudware.portal.framework.sdk.contants.CouponStatus;
import io.aicloudware.portal.framework.sdk.contants.CouponType;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "up_coupon")
@Access(AccessType.FIELD)
public final class UpCoupon extends BaseUpEntity<UpCouponBean> {

	@Column(name = "remark")
	private String remark;

	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private CouponType type;

	@Column(name = "coupon_status")
	@Enumerated(EnumType.STRING)
	private CouponStatus couponStatus;

	@Column(name = "stackable")
	private Boolean stackable;

	@Column(name = "template")
	private Boolean template;

	@Column(name = "total_count")
	private Integer totalCount;

	@Column(name = "limit_count_per_user")
	private Integer limitCountPerCustomer;

	@EntityProperty(isCopyOnUpdate = false)
	@JoinColumn(name = "owner_id")
	@ManyToOne(fetch = FetchType.LAZY)
	private UpUser owner;

	@Column(name = "start_time")
	private Date startTime;

	@Column(name = "end_time")
	private Date endTime;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "coupon")
	@Where(clause = "status!='deleted'")
	@org.hibernate.annotations.OrderBy(clause = "id")
	private List<UpServicePlan> servicePlans;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "coupon")
	@Where(clause = "status!='deleted'")
	@org.hibernate.annotations.OrderBy(clause = "month")
	private List<UpCouponItem> couponItems;

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<UpServicePlan> getServicePlans() {
		return servicePlans;
	}

	public void setServicePlans(List<UpServicePlan> servicePlans) {
		this.servicePlans = servicePlans;
	}

	public CouponType getType() {
		return type;
	}

	public void setType(CouponType type) {
		this.type = type;
	}

	public CouponStatus getCouponStatus() {
		return couponStatus;
	}

	public void setCouponStatus(CouponStatus couponStatus) {
		this.couponStatus = couponStatus;
	}

	public Boolean getStackable() {
		return stackable;
	}

	public void setStackable(Boolean stackable) {
		this.stackable = stackable;
	}

	public Boolean getTemplate() {
		return template;
	}

	public void setTemplate(Boolean template) {
		this.template = template;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public Integer getLimitCountPerCustomer() {
		return limitCountPerCustomer;
	}

	public void setLimitCountPerCustomer(Integer limitCountPerCustomer) {
		this.limitCountPerCustomer = limitCountPerCustomer;
	}

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public List<UpCouponItem> getCouponItems() {
		return couponItems;
	}

	public void setCouponItems(List<UpCouponItem> couponItems) {
		this.couponItems = couponItems;
	}
}
