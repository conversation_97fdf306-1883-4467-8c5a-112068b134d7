package io.aicloudware.portal.api_crm.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "用户")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, RestV2UserBean.class})
public class RestV2UserBean extends UpUserBean {
    private String customNo;

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }
}
