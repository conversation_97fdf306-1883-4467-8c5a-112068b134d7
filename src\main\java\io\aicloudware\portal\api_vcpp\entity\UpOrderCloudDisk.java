package io.aicloudware.portal.api_vcpp.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudStorageChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_order_cloud_disk")
@Access(AccessType.FIELD)
//public class UpOrderCloudDisk extends BaseUpEntity<UpOrderCloudDiskBean>{
public class UpOrderCloudDisk extends UpOrderProduct<UpOrderCloudDiskBean> implements IOrderEntity{

	@Column(name = "disk_type")
    @Enumerated(EnumType.STRING)
    private DiskType diskType;

	@Column(name = "payment_type")
	@Enumerated(EnumType.STRING)
	private PaymentType paymentType;

	@Column(name = "charge_type")
	@Enumerated(EnumType.STRING)
	private CloudStorageChargeType chargeType;

	@Column(name = "disk_gb")
	private Integer diskGB;
	
	@Column(name = "disk_number")
    private Integer diskNumber;
	
	@Column(name = "cloud_server_id")
	private Integer cloudServerId;

	@JoinColumn(name = "order_cloud_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private UpOrderCloudServer orderCloudServer;

	@JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

	@Column(name = "type")
	@Enumerated(EnumType.STRING)
	private SpVmDiskType type;

	@JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;
	
	@Column(name = "disk_config_id")
	private Integer diskConfigId;
	
    @Column(name = "task_sequence")
    private Integer taskSequence;

	@JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
	private SpOrg spOrg;
	
	public PaymentType getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(PaymentType paymentType) {
		this.paymentType = paymentType;
	}

	public CloudStorageChargeType getChargeType() {
		return chargeType;
	}

	public void setChargeType(CloudStorageChargeType chargeType) {
		this.chargeType = chargeType;
	}

	public Integer getDiskGB() {
		return diskGB;
	}

	public void setDiskGB(Integer diskGB) {
		this.diskGB = diskGB;
	}

	public Integer getCloudServerId() {
		return cloudServerId;
	}

	public void setCloudServerId(Integer cloudServerId) {
		this.cloudServerId = cloudServerId;
	}

	public UpOrderCloudServer getOrderCloudServer() {
		return orderCloudServer;
	}

	public void setOrderCloudServer(UpOrderCloudServer orderCloudServer) {
		this.orderCloudServer = orderCloudServer;
	}

	public UpUser getOwner() {
		return owner;
	}

	public void setOwner(UpUser owner) {
		this.owner = owner;
	}

	public SpVmDiskType getType() {
		return type;
	}

	public void setType(SpVmDiskType type) {
		this.type = type;
	}

	public UpOrder getOrder() {
		return order;
	}

	public void setOrder(UpOrder order) {
		this.order = order;
	}

	public Integer getDiskConfigId() {
		return diskConfigId;
	}

	public void setDiskConfigId(Integer diskConfigId) {
		this.diskConfigId = diskConfigId;
	}

	public SpOrg getSpOrg() {
		return spOrg;
	}

	public void setSpOrg(SpOrg spOrg) {
		this.spOrg = spOrg;
	}

    public Integer getTaskSequence() {
        return taskSequence;
    }

    public void setTaskSequence(Integer taskSequence) {
        this.taskSequence = taskSequence;
    }

	public Integer getDiskNumber() {
		return diskNumber;
	}

	public void setDiskNumber(Integer diskNumber) {
		this.diskNumber = diskNumber;
	}

	public DiskType getDiskType() {
		return diskType;
	}

	public void setDiskType(DiskType diskType) {
		this.diskType = diskType;
	}
}
