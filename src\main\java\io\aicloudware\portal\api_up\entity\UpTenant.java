package io.aicloudware.portal.api_up.entity;

import java.util.List;
import java.util.Set;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;
import org.hibernate.criterion.DetachedCriteria;

import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.entity.BaseEntity;
import io.aicloudware.portal.framework.entity.IDisplayNameEntity;
import io.aicloudware.portal.framework.sdk.bean.UpTenantBean;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;

@Entity
@Table(name = "up_tenant")
@Access(AccessType.FIELD)
public final class UpTenant extends BaseEntity<UpTenantBean> implements IDisplayNameEntity<UpTenantBean> {

	@Column(name = "display_name")
    private String displayName;
	
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "upTenant")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<SpOrg> spOrgs;

    
    public UpTenant() {
    }

    @Override
    public DetachedCriteria fillCriteria(DetachedCriteria criteria, SearchBean<UpTenantBean> searchBean, Set<String> aliasSet) {
        return super.fillCriteria(criteria, searchBean, aliasSet);
    }
    
    public UpTenant(Integer id) {
        super(id);
    }

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}


}
