package io.aicloudware.portal.api_crm.framework.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "云安全")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, RestV2SecurityBean.class})
public class RestV2SecurityBean extends RecordBean {

	@ApiModelProperty(value = "Quota code")
	private String doorOrderItemId;

	@ApiModelProperty(value = "安全产品类型")
	private UpOrderSystemEnums.ProductVmSetType type;

	@ApiModelProperty(value = "公网ip ID")
	private Integer spElasticIpId;
	
	@ApiModelProperty(value = "公网ip 端口")
	private String public_port;

	public Integer getSpElasticIpId() {
		return spElasticIpId;
	}

	public void setSpElasticIpId(Integer spElasticIpId) {
		this.spElasticIpId = spElasticIpId;
	}

	public String getPublic_port() {
		return public_port;
	}

	public void setPublic_port(String public_port) {
		this.public_port = public_port;
	}

	public UpOrderSystemEnums.ProductVmSetType getType() {
		return type;
	}

	public void setType(UpOrderSystemEnums.ProductVmSetType type) {
		this.type = type;
	}

	public String getDoorOrderItemId() {
		return doorOrderItemId;
	}

	public void setDoorOrderItemId(String doorOrderItemId) {
		this.doorOrderItemId = doorOrderItemId;
	}
}
