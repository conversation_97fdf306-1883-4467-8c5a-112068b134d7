package io.aicloudware.portal.api_rest.framework.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;

@ApiModel(value = "token")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, RestTokenBean.class })
public class RestTokenBean {

	private String token;

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}
	
}
