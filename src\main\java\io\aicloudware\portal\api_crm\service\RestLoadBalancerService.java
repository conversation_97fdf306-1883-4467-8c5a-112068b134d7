package io.aicloudware.portal.api_crm.service;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.*;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.quota.IUpOrderQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.SpMetricsBean;
import io.aicloudware.portal.framework.sdk.bean.SpMetricsDataBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderLoadBalanceBean;
import io.aicloudware.portal.framework.sdk.contants.*;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.*;
import io.aicloudware.portal.platform_vcd.service.ISpRegionService;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

@Service
@Transactional
public class RestLoadBalancerService extends BaseService implements IRestLoadBalancerService {

	@Autowired
	private IUpOrderService orderService;

	@Autowired
	private IUpOrderQuotaService upOrderQuotaService;

	@Autowired
	private ISpRegionService spRegionService;
	
	@Override
	public Integer save(UpOrderLoadBalanceBean bean) {
		UpOrderQuotaDetail quotaDetail = upOrderQuotaService.getQuotaDetail(new SpOrg(ThreadCache.getOrgId()), ThreadCache.getRegion(), bean.getDoorOrderItemId());
		UpOrderQuota quota = quotaDetail.getQuota();
		AssertUtil.check(quota.getOwner().getId().equals(ThreadCache.getUserId())
						&& quota.getSpOrg().getId().equals(ThreadCache.getOrgId())
						&& quota.getCatalog() == UpProductSystemEnums.QuotaCatalog.NEW
						&& quotaDetail.getType() == UpProductSystemEnums.ProductType.LOAD_BALANCER
				,"非法操作，订单状态异常！");
		AssertUtil.check(quota.getRegion().equals(ThreadCache.getRegion()),"地市信息异常！");

		UpUser user = dao.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user, "所选用户不存在！");
		
		AssertUtil.check(bean, "数据异常！");
		AssertUtil.check(bean.getName(), "请输入实例名！");
		AssertUtil.check(Pattern.matches("^[0-9a-zA-Z-]+$", bean.getName()), "实例名只能由字母数字中划线组成！");
		AssertUtil.check(Pattern.matches(".*[a-zA-Z]+.*", bean.getName()), "实例名必须包含字母！");
//		AssertUtil.check(bean.getElasticIpList().length>0 && bean.getElasticIpList()[0].getId() !=null,"请输入公网IP！");
		
		AssertUtil.check(bean.getVip() !=null,"请输入VIP！");
//		UpUser user = ThreadCache.getUser();
//		String sql = "select 1 from sp_ip_binding where sp_org_id= " + user.getOrg().getId() + " and status <> '" + RecordStatus.deleted
//				+ "' and  elastic_ip_id="+bean.getElasticIpList()[0].getId();
//		List<Object[]> datas = queryDao.querySql(sql, null);
//		AssertUtil.check(datas.size()==0,"公网IP已使用！");
		
		AssertUtil.check(bean.getVpcId(), "请选择VPC！");
		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在或已删除！");
		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC数据异常！");
		AssertUtil.check(bean.getBalanceServerRelationList() != null && bean.getBalanceServerRelationList().length >= 1 , "请添加云服务器！");
//		AssertUtil.check(bean.getLoadBalanceConfigId(), "缺少负载均衡产品配置！");

		AssertUtil.check(orderService.queryActiveOrder(OrderType.new_load_balance, user.getId()) == 0, "您有未完成的负载均衡申请！");
		String name = bean.getName()+"-"+System.currentTimeMillis() + String.format("%04d",(int)(Math.random()*1000));
		UpOrderLoadBalance entity = BeanCopyUtil.copy(bean, UpOrderLoadBalance.class);
		entity.setOwner(user);
		entity.setName(name);
		entity.setSpOrg(user.getOrg());
		if(entity.getBalanceServerRelationList()!=null && entity.getBalanceServerRelationList().size()>0) {
			Map<Integer,Object> map = new HashMap<>();
			for(UpOrderBalanceServerRelation relation : entity.getBalanceServerRelationList()) {
				AssertUtil.check(relation, "添加云服务器异常！");
				AssertUtil.check(relation.getCloudServerId(), "请选择云服务器！");
				AssertUtil.check(relation.getWeight() !=null, "请输入权重！");
				AssertUtil.check(relation.getWeight() >=0 && relation.getWeight() <= 100, "请输入0-100的权重数字！");
				AssertUtil.check(!map.containsKey(relation.getCloudServerId()),"重复选择了同一台云服务器！");
				map.put(relation.getCloudServerId(),"");
				
				SpVm vm = this.dao.load(SpVm.class,relation.getCloudServerId());
				AssertUtil.check(vm, "云服务器信息异常！");
				AssertUtil.check(vm.getSpOrg().getId().equals(user.getOrg().getId()),"云服务器所属用户组异常！");
//				String sql = "select count(1) from sp_ip_binding where vm_id = "+relation.getCloudServerId()+" and status <> '"+RecordStatus.deleted+"'";
//				int num = ListUtil.first(this.queryDao.querySql(sql, null));
//				AssertUtil.check(num == 0 , "所选云服务器已绑定负载均衡！");
				
				relation.setName(name);
			}
		}
		
		UpOrder order = new UpOrder();
		order.setRegion(ThreadCache.getRegion());
		order.setType(OrderType.new_load_balance);
		order.setName("[" + OrderType.new_load_balance + "]" + bean.getName());
		order.setOrderStatus(OrderStatus.pending_deploy);
		order.setOwner(user);
		order.setApplyUser(user);
		order.setPaymentType(bean.getPaymentType());
		order.setSpOrg(user.getOrg());
		order.setNumber(1);
		order.setQuotaDetail(quotaDetail);
		order.setQuota(quota);

		if(entity.getMonitorList()!=null && entity.getMonitorList().size()>0) {
			for(UpOrderMonitor monitor : entity.getMonitorList()) {
				AssertUtil.check(monitor, "添加监听异常！");
				AssertUtil.check(monitor.getName(), "请输入监听名称！");
				AssertUtil.check(monitor.getFrontLayer(), "请选择前端协议！");
				AssertUtil.check(monitor.getFrontPort(), "请输入前端端口号！");
				AssertUtil.check(monitor.getBalanceArithmetic(), "请选择均衡算法！");
				AssertUtil.check(monitor.getBackendLayer(), "请选择后端云服务器协议！");
				AssertUtil.check(monitor.getBackendPort(), "请选择后端云服务器端口号！");
				AssertUtil.check(monitor.getInterval(), "输入间隔时间！");
				AssertUtil.check(monitor.getTimeout(), "输入超时时间！");
				AssertUtil.check(monitor.getMaxNumber(), "输入最大重试次数！");
				monitor.setOrder(order);
				monitor.setName(name);
				monitor.setOrg(user.getOrg());
				monitor.setRegion(order.getRegion());
			}
		}
		
		UpOrderElasticIp newElasticIp = null;
		
		entity.setOrder(order);
		if(entity.getVip()!=null) {
			List<Object[]> vsList = queryDao.querySql("select * from sp_load_balancer_virtual_server where sp_org_id="+user.getOrg().getId()+" and status='"+RecordStatus.active+"' and ip_address='"+entity.getVip()+"'",null);
			AssertUtil.check(vsList.size()==0,"所选VIP已被使用！");
//			AssertUtil.check(entity.getElasticIpList().get(0)!=null , "请输入正确的弹性公网IP信息！");
//			UpOrderElasticIp elasticIpEntity = entity.getElasticIpList().get(0);
//			
//			if(elasticIpEntity.getId()==null) {
//				elasticIpEntity.setChargePeriod(ChargePeriod.hour);
//				elasticIpEntity.setName(name);
//				elasticIpEntity.setOwner(user);
//				elasticIpEntity.setPaymentType(bean.getPaymentType());
//				elasticIpEntity.setResourceType(ResourceType.loadBalance);
//				elasticIpEntity.setOrderLoadBalance(entity);
//				elasticIpEntity.setOrder(order);
//				elasticIpEntity.setSpOrg(user.getOrg());
//				entity.getElasticIpList().set(0, elasticIpEntity);
//				AssertUtil.check(elasticIpEntity.getBandwidthConfigId(), "缺少带宽产品配置！");
//				
//				UpProductBandwidthSet bandwidthSet = this.dao.load(UpProductBandwidthSet.class, elasticIpEntity.getBandwidthConfigId());
//				AssertUtil.check(bandwidthSet != null && bandwidthSet.getEnabled(), "带宽产品配置信息异常！");
//				order.setBandwidthNum(elasticIpEntity.getBandwidth());
//				order.setBandwidthPrice(bandwidthSet.getPrice().multiply(BigDecimal.valueOf(elasticIpEntity.getBandwidth())));
//				order.setBandwidthSet(bandwidthSet);
//				newElasticIp = elasticIpEntity;
////				UpProductLoadBalanceSet loadBalanceSet = this.dao.load(UpProductLoadBalanceSet.class, entity.getLoadBalanceConfigId());
////				AssertUtil.check(loadBalanceSet != null && loadBalanceSet.getEnabled(), "负载均衡产品配置信息异常！");
////				order.setLoadBalanceNum(elasticIpEntity.getBandwidth());
////				order.setLoadBalancePrice(loadBalanceSet.getPrice().multiply(BigDecimal.valueOf(elasticIpEntity.getBandwidth())));
//			}else if(elasticIpEntity.getId()!=null) {
//				SpElasticIp elasticIp = this.dao.load(SpElasticIp.class,elasticIpEntity.getId());
//				AssertUtil.check(elasticIp, "弹性公网IP信息异常！");
//				AssertUtil.check(elasticIp.getSpOrg().getId().equals(user.getOrg().getId()),"弹性公网IP所属用户组异常！");
//				if(elasticIp.getIpBinding()!=null && elasticIp.getIpBinding().size()>0) {
//					for(SpIpBinding binding: elasticIp.getIpBinding()) {
//						AssertUtil.check(binding.getStatus().equals(RecordStatus.active),"所选弹性公网IP已被使用！");
//					}
//				}
//				List<Object[]> vsList = queryDao.querySql("select * from sp_load_balancer_virtual_server where sp_org_id="+elasticIp.getSpOrg().getId()+" and status='"+RecordStatus.active+"' and ip_address='"+elasticIp.getIpAddress()+"'",null);
//				AssertUtil.check(vsList.size()==0,"所选弹性公网IP已被使用！");
//				entity.setElasticIpList(null);
////				UpProductLoadBalanceSet loadBalanceSet = this.dao.load(UpProductLoadBalanceSet.class, entity.getLoadBalanceConfigId());
////				AssertUtil.check(loadBalanceSet != null && loadBalanceSet.getEnabled(), "负载均衡产品配置信息异常！");
//				entity.setElasticIpId(elasticIpEntity.getId());
//				entity.setElasticIpList(null);
//			}
		}
		this.dao.insert(order);
		entity.setOrder(order);
		entity.setRegion(order.getRegion());
		this.dao.insert(entity);
		
        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setSpOrg(user.getOrg());
        task.setTaskStatus(UpTaskStatus.start);
        task.setRegion(order.getRegion());
        dao.insert(task);
		upOrderQuotaService.deployQuotaDetail(quotaDetail);
		return order.getId();
	}

	@Override
	public void updateLBPoolAndVs(SpLoadBalancer loadBalancer) {
		SpOrg spOrg = dao.load(SpOrg.class, loadBalancer.getSpOrg().getId());
		loadBalancer.setSpOrg(spOrg);
		List<SpOVDC> ovdcList = dao.list(SpOVDC.class, MapUtil.of("status", RecordStatus.active, "region", loadBalancer.getRegion(), "spOrg", spOrg));
		AssertUtil.check(ovdcList, "未找到OVDC，区域："+loadBalancer.getRegion()+" 组织："+spOrg.getName());


		refresh(spOrg, loadBalancer.getRegion());
	}

	private void updateVip(SpLoadBalancerVirtualServer vs, SpLoadBalancerVirtualServer dbVs) {
		Map<String, Object> paramMap = MapUtil.of("org",vs.getSpOrg(), "status", RecordStatus.active, "ipAddr", dbVs.getIpAddress());
		List<SpLoadBalancerVip> vipList = queryDao.queryHql("select b from SpLoadBalancerVip b where b.spOrg=:org and b.status=:status and b.ipAddress=:ipAddr", paramMap);
		if (Utility.isNotEmpty(vipList)) {
			for (SpLoadBalancerVip vip : vipList) {
				vip.setIpAddress(vs.getIpAddress());
				dao.update(vip);
			}
		}
	}

	@Override
	public void updateLBPool(SpLoadBalancerPool lbPool) {
		SpOrg spOrg = dao.load(SpOrg.class, lbPool.getSpOrg().getId());
		lbPool.setSpOrg(spOrg);

		refresh(spOrg, lbPool.getRegion());
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void refresh(SpOrg entity, SpRegionEntity region) {
		if ("System".equalsIgnoreCase(entity.getName()) || "public".equalsIgnoreCase(entity.getName())) {
			return;
		}
		// TODO: refresh load balancers
	}

	private List<SpLoadBalancer> getLoadBalancerFromJSON(SpOrg spOrg, JSONObject jsonLB, String edgeId, SpRegionEntity region) {
		SpLoadBalancer loadBalancer = new SpLoadBalancer();
		loadBalancer.setName(spOrg.getName());
		loadBalancer.setSpOrg(spOrg);
		loadBalancer.setSpUuid(spOrg.getSpUuid()+"-"+spRegionService.CIDCRP35().getName());
		loadBalancer.setEnabled(true);
		loadBalancer.setRegion(region);
		loadBalancer.setLoadBalancerPoolList(new ArrayList<SpLoadBalancerPool>());
		loadBalancer.setLoadBalancerVirutalServerList(new ArrayList<SpLoadBalancerVirtualServer>());

		try {
			JSONArray jsonVSs = (JSONArray) jsonLB.get("virtualServer");
			for (int i = 0;i<jsonVSs.length();i++) {
				JSONObject jsonVS = jsonVSs.getJSONObject(i);
				SpLoadBalancerVirtualServer vs = new SpLoadBalancerVirtualServer();
				String vsName = jsonVS.getString("display_name");
				String vsId = jsonVS.getString("virtualServerId");
				vs.setName(vsName);
				vs.setVsId(vsId);
				vs.setRegion(region);
				vs.setAccelerated(jsonVS.getBoolean("accelerationEnabled"));
				vs.setDefaultPool(jsonVS.getString("defaultPoolId"));
				if (jsonVS.has("description")) {
					vs.setDescription(jsonVS.getString("description"));
				}
				vs.setEnabled(jsonVS.getBoolean("enabled"));
				vs.setIpAddress(jsonVS.getString("ipAddress"));
				vs.setProtocal(jsonVS.getString("protocol"));
				vs.setPort(jsonVS.getString("port"));
				vs.setLoadBalancer(loadBalancer);
				vs.setSpOrg(spOrg);
				String uuid = spOrg.getSpUuid()+"-"+loadBalancer.getName()+"~"+vsId;
				vs.setSpUuid(uuid);
				loadBalancer.getLoadBalancerVirutalServerList().add(vs);
			}

			JSONArray jsonPools = (JSONArray) jsonLB.get("pool");

			for (int i = 0;i<jsonPools.length();i++) {
				JSONObject jsonPool = jsonPools.getJSONObject(i);
				SpLoadBalancerPool pool = new SpLoadBalancerPool();
				String poolName = jsonPool.getString("name");
				String poolId = jsonPool.getString("poolId");
				pool.setLoadBalancer(loadBalancer);
				String uuid = spOrg.getSpUuid()+"-"+edgeId+"-"+loadBalancer.getName()+"~"+poolId;

				pool.setSpUuid(uuid);
				pool.setName(poolName);
				if (jsonPool.has("description")) {
					pool.setDescription(jsonPool.getString("description"));
				}
				pool.setSpOrg(spOrg);
				pool.setRegion(region);
				pool.setPoolId(poolId);
				pool.setAlgorithm(jsonPool.getString("algorithm"));
				pool.setLoadBalancerMemberList(new ArrayList<SpLoadBalancerMember>());
				loadBalancer.getLoadBalancerPoolList().add(pool);

				JSONArray jsonMembers = jsonPool.getJSONArray("member");
				for (int m = 0;m<jsonMembers.length();m++) {
					JSONObject jsonMember = jsonMembers.getJSONObject(m);
					SpLoadBalancerMember lbMember = new SpLoadBalancerMember();
					pool.getLoadBalancerMemberList().add(lbMember);
					lbMember.setName(jsonMember.getString("name"));
					if (jsonMember.has("ipAddress")) {
						lbMember.setIpAddress(jsonMember.getString("ipAddress"));
					}
					lbMember.setWeight(""+jsonMember.getInt("weight"));
					lbMember.setSpOrg(spOrg);
					lbMember.setRegion(region);
					lbMember.setLoadBalancerPool(pool);
					lbMember.setSpUuid(pool.getSpUuid()+"~"+lbMember.getIpAddress());
					lbMember.setAlgorithm(pool.getAlgorithm());
					lbMember.setHealthCheckPort(""+jsonMember.getInt("monitorPort"));
					lbMember.setPort(""+jsonMember.getInt("port"));
					lbMember.setProtocol("tcp");
					lbMember.setLoadBalancerHealthCheck(new ArrayList<SpLoadBalancerHealthCheck>());

					SpLoadBalancerHealthCheck hc = new SpLoadBalancerHealthCheck();
					lbMember.getLoadBalancerHealthCheck().add(hc);
					hc.setInterval("3");
					hc.setSpOrg(spOrg);
					hc.setTimeout("3");
					hc.setMode("mode");
					hc.setRegion(region);
					hc.setSpUuid(lbMember.getSpUuid()+"-hc");
					hc.setUrl("");
					hc.setMaxRetry("3");
					hc.setName("hc");
					hc.setLoadBalancerMember(lbMember);
				}
			}
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new RuntimeException("Fail to get loadbalancer info from server."+e.getMessage());
		}
		return Collections.singletonList(loadBalancer);
	}

	public List<SpLoadBalancer> getNsxtLoadBalancerFromJSON(SpOrg spOrg, JSONObject jsonLB, SpRegionEntity region) {
		SpLoadBalancer loadBalancer = new SpLoadBalancer();
		loadBalancer.setName(spOrg.getName());
		loadBalancer.setSpOrg(spOrg);
		loadBalancer.setSpUuid(spOrg.getSpUuid()+"-"+spRegionService.CIDCRP35().getName());
		loadBalancer.setEnabled(true);
		loadBalancer.setRegion(region);
		loadBalancer.setLoadBalancerPoolList(new ArrayList<SpLoadBalancerPool>());
		loadBalancer.setLoadBalancerVirutalServerList(new ArrayList<SpLoadBalancerVirtualServer>());

		try {
			JSONArray jsonVSs = (JSONArray) jsonLB.get("virtualServer");
			for (int i = 0;i<jsonVSs.length();i++) {
				JSONObject jsonVS = jsonVSs.getJSONObject(i);
				SpLoadBalancerVirtualServer vs = new SpLoadBalancerVirtualServer();
				String vsName = jsonVS.getString("display_name");
				String vsId = jsonVS.getString("id");
				vs.setName(vsName);
				vs.setVsId(vsId);
				vs.setRegion(region);
				vs.setDefaultPool(jsonVS.getString("pool_path"));
				vs.setEnabled(jsonVS.getBoolean("enabled"));
				vs.setIpAddress(jsonVS.getString("ip_address"));
				String applicationProfile = jsonVS.getString("application_profile_path");
				String[] protocol = new String[]{"tcp","udp","http"};
				Arrays.stream(protocol).filter(x -> applicationProfile.indexOf(x)>-1).forEach(x -> {
					vs.setProtocal(x);
				});
				vs.setPort(jsonVS.getJSONArray("ports").getString(0));
				vs.setLoadBalancer(loadBalancer);
				vs.setSpOrg(spOrg);
				String uuid = spOrg.getSpUuid()+"-"+loadBalancer.getName()+"~"+vsId;
				vs.setSpUuid(uuid);
				loadBalancer.getLoadBalancerVirutalServerList().add(vs);
			}

			JSONArray jsonPools = (JSONArray) jsonLB.get("pool");

			for (int i = 0;i<jsonPools.length();i++) {
				JSONObject jsonPool = jsonPools.getJSONObject(i);
				SpLoadBalancerPool pool = new SpLoadBalancerPool();
				String poolName = jsonPool.getString("display_name");
				String poolId = jsonPool.getString("id");
				pool.setLoadBalancer(loadBalancer);
				String uuid = spOrg.getSpUuid()+"-"+loadBalancer.getName()+"~"+poolId;

				pool.setSpUuid(uuid);
				pool.setName(poolName);
				pool.setSpOrg(spOrg);
				pool.setRegion(region);
				pool.setPoolId(poolId);
				pool.setAlgorithm(jsonPool.getString("algorithm"));
				pool.setLoadBalancerMemberList(new ArrayList<SpLoadBalancerMember>());
				loadBalancer.getLoadBalancerPoolList().add(pool);

				JSONArray jsonMembers = jsonPool.getJSONArray("members");
				for (int m = 0;m<jsonMembers.length();m++) {
					JSONObject jsonMember = jsonMembers.getJSONObject(m);
					SpLoadBalancerMember lbMember = new SpLoadBalancerMember();
					pool.getLoadBalancerMemberList().add(lbMember);
					lbMember.setName(jsonMember.getString("display_name"));
					if (jsonMember.has("ip_address")) {
						lbMember.setIpAddress(jsonMember.getString("ip_address"));
					}
					lbMember.setWeight(""+jsonMember.getInt("weight"));
					lbMember.setSpOrg(spOrg);
					lbMember.setRegion(region);
					lbMember.setLoadBalancerPool(pool);
					lbMember.setSpUuid(pool.getSpUuid()+"~"+lbMember.getIpAddress());
					lbMember.setAlgorithm(pool.getAlgorithm());
					lbMember.setHealthCheckPort(""+jsonMember.getInt("port"));
					lbMember.setPort(""+jsonMember.getInt("port"));
					lbMember.setProtocol("tcp");
					lbMember.setLoadBalancerHealthCheck(new ArrayList<SpLoadBalancerHealthCheck>());

					SpLoadBalancerHealthCheck hc = new SpLoadBalancerHealthCheck();
					lbMember.getLoadBalancerHealthCheck().add(hc);
					hc.setInterval("3");
					hc.setSpOrg(spOrg);
					hc.setTimeout("3");
					hc.setMode("mode");
					hc.setRegion(region);
					hc.setSpUuid(lbMember.getSpUuid()+"-hc");
					hc.setUrl("");
					hc.setMaxRetry("3");
					hc.setName("hc");
					hc.setLoadBalancerMember(lbMember);
				}
			}
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new RuntimeException("Fail to get loadbalancer info from server."+e.getMessage());
		}
		List<SpLoadBalancer> loadBalancerList = new ArrayList<>();
		loadBalancerList.add(loadBalancer);
		return loadBalancerList;
	}

	@Override
	public ArrayList<SpMetricsBean> queryLBHistoryMetrics(Integer id, float timelen) {
		SpLoadBalancerVirtualServer vs = dao.load(SpLoadBalancerVirtualServer.class, id);
		long currentTimestamp = System.currentTimeMillis()/1000;
		long startTimestamp = (long)(currentTimestamp-(3600*timelen));
		logger.info("startTimestamp:"+startTimestamp);
//		List<SpLoadBalancerVirtualServerStatistics> statList = dao.list(SpLoadBalancerVirtualServerStatistics.class, "loadBalancerVirtualServer", vs);
		List<SpLoadBalancerVirtualServerStatistics> statList = this.queryDao.queryHql("from SpLoadBalancerVirtualServerStatistics where loadBalancerVirtualServer=:vs and timeStamp>:ts order by timeStamp", MapUtil.of("vs", vs,"ts",startTimestamp));

		ArrayList<SpMetricsBean> list = new ArrayList<>();
		SpMetricsBean httpReqTotalMb = new SpMetricsBean();
		httpReqTotalMb.setMetricName("httpReqTotal");
		list.add(httpReqTotalMb);

		SpMetricsBean totalSessionsMb = new SpMetricsBean();
		totalSessionsMb.setMetricName("totalSessions");
		list.add(totalSessionsMb);


		List<SpMetricsDataBean> httpReqTotalDataList = new ArrayList<>();
		List<SpMetricsDataBean> totalSessionsDataList = new ArrayList<>();
		for (SpLoadBalancerVirtualServerStatistics stat:statList) {
			Date date = new Date(stat.getTimeStamp()*1000);
			SpMetricsDataBean httpReqTotal = new SpMetricsDataBean();
			httpReqTotal.setTime(date);
			httpReqTotal.setValue(BigDecimal.valueOf(stat.getHttpReqTotal()));
			httpReqTotalDataList.add(httpReqTotal);

			SpMetricsDataBean totalSessionsDataBean = new SpMetricsDataBean();
			totalSessionsDataBean.setTime(date);
			totalSessionsDataBean.setValue(BigDecimal.valueOf(stat.getTotalSessions()));
			totalSessionsDataList.add(totalSessionsDataBean);
		}

		httpReqTotalMb.setData(httpReqTotalDataList.toArray(new SpMetricsDataBean[0]));
		totalSessionsMb.setData(totalSessionsDataList.toArray(new SpMetricsDataBean[0]));

		return list;
	}
}
