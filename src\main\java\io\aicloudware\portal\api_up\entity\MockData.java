package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseSpEntity;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.bean.RecordBean;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "mock_data")
@Access(AccessType.FIELD)
public class MockData extends BaseSpEntity<RecordBean> {

    @Column(name = "order_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private OrderType orderType;

    @JoinColumn(name = "req_deployment_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private ReqDeployment reqDeployment;

    @JoinColumn(name = "req_vm_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private ReqVm reqVm;

    @Column(name = "request_id", nullable = false)
    private String requestId;

    @Column(name = "task_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UpTaskStatus taskStatus;

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public ReqDeployment getReqDeployment() {
        return reqDeployment;
    }

    public void setReqDeployment(ReqDeployment reqDeployment) {
        this.reqDeployment = reqDeployment;
    }

    public ReqVm getReqVm() {
        return reqVm;
    }

    public void setReqVm(ReqVm reqVm) {
        this.reqVm = reqVm;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public UpTaskStatus getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(UpTaskStatus taskStatus) {
        this.taskStatus = taskStatus;
    }
}
