package io.aicloudware.portal.api_crm.controller;

import io.aicloudware.portal.api_crm.service.IRestUserService;
import io.aicloudware.portal.api_rest.framework.annotation.IpValidate;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_up.service.IUpUserService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpUserBean;
import io.aicloudware.portal.framework.sdk.bean.UpUserResultBean;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * rest api
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/api/v2/user")
@Api(value = "/api/v2/user", description = "REST API V2")
public class RestApiV2UserController extends BaseUpController<UpUser, UpUserBean, UpUserResultBean> {

    @Autowired
    private IUpUserService upUserService;

    @Autowired
    private IRestUserService restUserService;
//
//    @RequestMapping(value = "/admin", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseBean admin(@RequestBody UpUserBean bean, HttpServletRequest request) {
//        return ResponseBean.success(upUserService.login(bean, true));
//    }
//
//	@RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseBean add(@RequestBody UpUserBean bean, HttpServletRequest request) {
//        return ResponseBean.success(upUserService.addUser(bean));
//    }
//
//	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ResponseBody
//    public ResponseBean delete(@PathVariable Integer id, HttpServletRequest request) {
//        return deleteEntity(id);
//    }

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "user", type = "login")
//    @RestLogAspect
    public ResponseBean login(@RequestBody UpUserBean bean, HttpServletRequest request) {
        return ResponseBean.success(restUserService.login(bean));
    }

    @RequestMapping(value = "/changeRegion", method = RequestMethod.POST)
    @ResponseBody
    @IpValidate(module = "user", type = "changeRegion")
    public ResponseBean changeRegion(@RequestBody UpUserBean bean, HttpServletRequest request) {
        return ResponseBean.success(upUserService.buildTokenMap(ThreadCache.getUserId(), bean.getRegion()));
    }


}
