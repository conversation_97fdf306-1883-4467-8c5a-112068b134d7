package io.aicloudware.portal.api_rest.framework.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "VPC信息")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, RestSubnetBean.class })
public class RestSubnetBean extends BaseRestBean {

	@ApiModelProperty(value = "用户ID")
	private String subnetSegment;
	
	@ApiModelProperty(value = "用户ID")
	private String operationType;

	public String getSubnetSegment() {
		return subnetSegment;
	}

	public void setSubnetSegment(String subnetSegment) {
		this.subnetSegment = subnetSegment;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}
}
