package io.aicloudware.portal.platform_vcd.controller;

import io.aicloudware.portal.api_up.service.IUpUserService;
import io.aicloudware.portal.framework.annotation.AuditLogSpEntity;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.bean.ResultListBean;
import io.aicloudware.portal.framework.bean.SearchBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseSpInvokeController;
import io.aicloudware.portal.framework.redis.RedisService;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.service.ICommonService;
import io.aicloudware.portal.framework.service.IInvokeService;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.aicloudware.portal.platform_vcd.entity.SpOVDCNetwork;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVPC;
import io.aicloudware.portal.platform_vcd.service.ISpVPCService;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/vdiPool")
@Api(value = "/vdiPool", description = "vdiPool", position = 32)
public class SpVdiPoolController extends BaseSpInvokeController<SpVPC, SpVPCBean, SpVPCResultBean> {

    @Autowired
    private ISpVPCService vpcService;

    @Override
    protected IInvokeService<SpVPC, ? extends SearchBean<SpVPCBean>, SpVPCBean> getInvokeService() {
        return vpcService;
    }

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpVPCSearchBean.class)})
    @ResponseBody
    public ResponseBean query(@ApiParam(value = "查询条件") @RequestBody SpVPCSearchBean searchBean) {

        SpVdiPoolBean bean = new SpVdiPoolBean();
        bean.setId(1);
        bean.setName("DMZ-EUC-POOL");
        bean.setServicePlanName("8c36g");
        bean.setImageName("Linux");
        bean.setVmCount(4);
        try {
            bean.setCreateTm(new SimpleDateFormat("yyyy-MM-dd").parse("2025-06-11"));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        bean.setArchitecture("x86_64");

        SpVdiPoolResultBean result = new SpVdiPoolResultBean();
        fillPageInfo(searchBean, result);
        result.setDataList(new SpVdiPoolBean[]{bean});
        return ResponseBean.success(result);
//        return queryEntity(searchBean);
    }

}
