package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaBean;
import io.aicloudware.portal.framework.sdk.contants.SpResourceType;
import io.aicloudware.portal.framework.sdk.contants.SpService;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Table(name = "up_quota")
@Access(AccessType.FIELD)
public class UpQuota extends BaseUpEntity<UpQuotaBean> {

    @JoinColumn(name = "owner_id")
    @ManyToOne(fetch = FetchType.LAZY)
    @Where(clause = "status!='deleted'")
    private UpUser owner;

    @Column(name = "service")
    @Enumerated(EnumType.STRING)
    private SpService service;

    @Column(name = "resource_type")
    @Enumerated(EnumType.STRING)
    private SpResourceType resourceType;

    @Column(name = "quota")
    private Integer quota;

    @Column(name = "used_quota")
    private Integer usedQuota;

    @JoinColumn(name = "sp_org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg org;

    @Column(name = "is_admin_quota")
    private Boolean isAdminQuota;

    @Column(name = "is_user_template")
    private Boolean isUserTemplate;

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public SpService getService() {
        return service;
    }

    public void setService(SpService service) {
        this.service = service;
    }

    public Boolean getAdminQuota() {
        return isAdminQuota;
    }

    public void setAdminQuota(Boolean adminQuota) {
        isAdminQuota = adminQuota;
    }

    public SpResourceType getResourceType() {
        return resourceType;
    }

    public void setResourceType(SpResourceType resourceType) {
        this.resourceType = resourceType;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Integer getUsedQuota() {
        return usedQuota;
    }

    public void setUsedQuota(Integer usedQuota) {
        this.usedQuota = usedQuota;
    }

    public SpOrg getOrg() {
        return org;
    }

    public void setOrg(SpOrg org) {
        this.org = org;
    }

    public Boolean getIsAdminQuota() {
        return isAdminQuota;
    }

    public void setIsAdminQuota(Boolean isAdminQuota) {
        this.isAdminQuota = isAdminQuota;
    }

    public Boolean getIsUserTemplate() {
        return isUserTemplate;
    }

    public void setIsUserTemplate(Boolean userTemplate) {
        isUserTemplate = userTemplate;
    }
}
