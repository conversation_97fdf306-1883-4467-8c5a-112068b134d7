package io.aicloudware.portal.framework.sdk.bean.vcpp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.bean.RecordBean;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.GroupSequence;

@ApiModel(value = "VDI")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, UpOrderVdiBean.class})
public class UpOrderVdiBean extends RecordBean {

	@ApiModelProperty(value = "IMAGE")
	private Integer imageId;

	@ApiModelProperty(value = "配置ID")
	private Integer servicePlanId;

	@ApiModelProperty(value = "CPU")
	private Integer cpu;
	
	@ApiModelProperty(value = "内存")
	private Integer memory;
	
	@ApiModelProperty(value = "镜像ID")
	private Integer disk;
	
	@ApiModelProperty(value = "专有网络")
	private Integer vpcId;
	
	@ApiModelProperty(value = "子网")
	private Integer networkId;

	private Integer ownerId;

	public Integer getOwnerId() {
		return ownerId;
	}

	public void setOwnerId(Integer ownerId) {
		this.ownerId = ownerId;
	}

	public Integer getImageId() {
		return imageId;
	}

	public void setImageId(Integer imageId) {
		this.imageId = imageId;
	}

	public Integer getServicePlanId() {
		return servicePlanId;
	}

	public void setServicePlanId(Integer servicePlanId) {
		this.servicePlanId = servicePlanId;
	}

	public Integer getCpu() {
		return cpu;
	}

	public void setCpu(Integer cpu) {
		this.cpu = cpu;
	}

	public Integer getMemory() {
		return memory;
	}

	public void setMemory(Integer memory) {
		this.memory = memory;
	}

	public Integer getDisk() {
		return disk;
	}

	public void setDisk(Integer disk) {
		this.disk = disk;
	}

	public Integer getVpcId() {
		return vpcId;
	}

	public void setVpcId(Integer vpcId) {
		this.vpcId = vpcId;
	}

	public Integer getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Integer networkId) {
		this.networkId = networkId;
	}
}
