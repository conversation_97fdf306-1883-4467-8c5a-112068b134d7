package io.aicloudware.portal.api_up.entity;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessBean;
import org.hibernate.annotations.Where;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.List;

@Entity
@Table(name = "up_approval_process")
@Access(AccessType.FIELD)
public class UpApprovalProcess extends BaseUpEntity<UpApprovalProcessBean> {

    @Column(name = "description", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String description;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "approvalProcess")
    @Where(clause = "status!='deleted'")
    private List<UpApprovalProcessNode> approvalProcessNodeList;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "approvalProcess")
    @Where(clause = "status!='deleted'")
    private List<UpApprovalScene> approvalSceneList;

    public List<UpApprovalProcessNode> getApprovalProcessNodeList() {
        return approvalProcessNodeList;
    }

    public void setApprovalProcessNodeList(List<UpApprovalProcessNode> approvalProcessNodeList) {
        this.approvalProcessNodeList = approvalProcessNodeList;
    }

    public List<UpApprovalScene> getApprovalSceneList() {
        return approvalSceneList;
    }

    public void setApprovalSceneList(List<UpApprovalScene> approvalSceneList) {
        this.approvalSceneList = approvalSceneList;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
