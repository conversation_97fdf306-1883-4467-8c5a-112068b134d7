package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.UpQuota;
import io.aicloudware.portal.api_up.entity.UpQuotaLog;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaLogBean;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaLogSearchBean;
import io.aicloudware.portal.framework.sdk.contants.QuotaLogStatus;
import io.aicloudware.portal.framework.service.BaseTaskService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.BeanCopyUtil;
import io.aicloudware.portal.framework.utility.MapUtil;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class UpQuotaLogService extends BaseTaskService implements IUpQuotaLogService {

    @Autowired
    private IUpQuotaService upQuotaService;

    @Override
    public UpQuotaLogBean[] query(UpQuotaLogSearchBean searchBean, UpQuotaLog entity) {
        if(!ThreadCache.getUser().getSystemAdmin()){
            entity.setOwner(new UpUser(ThreadCache.getUserId()));
        }

        String hql = "from UpQuotaLog where region.id = " + ThreadCache.getRegion().getId() +
                (ThreadCache.getUser().getSystemAdmin() ? " and quotaLogStatus = '" + QuotaLogStatus.pending_approve + "'" : " and owner.id = " + ThreadCache.getUserId()) +
                " and parent is null order by record_tm desc";
        List<UpQuotaLog> entitys  = queryDao.queryHql(hql, null);

        UpQuotaLogBean[] beans = BeanCopyUtil.copy2BeanList(entitys, UpQuotaLogBean.class);
        return Arrays.stream(beans).map(bean -> {
            bean.setServiceText(bean.getService().getTitle());
            bean.setResourceTypeText(bean.getResourceType().getTitle());
            return bean;
        }).collect(Collectors.toList()).toArray(new UpQuotaLogBean[entitys.size()]);
    }

    @Override
    public void save(UpQuotaLogBean bean) {
        UpUser user = ThreadCache.getUser();
        if(bean.getId() == null) {
            String hql = "from UpQuotaLog where owner.id = " + user.getId() + " and region.id = " + ThreadCache.getRegion().getId() +
                    " and resourceType = '" + bean.getResourceType() + "' and quotaLogStatus in ('pending_approve','approve_reject') " +
                    " and appSystem.id = " + bean.getAppSystemId();
            List<UpQuotaLog> entitys = queryDao.queryHql(hql, null);
            // new
            AssertUtil.check(entitys.size() == 0, "已有一条未完成的 [" + bean.getResourceType().getTitle() + "] 申请记录！");

            UpQuotaLog entity = new UpQuotaLog();
            BeanCopyUtil.copy(bean, entity);
            entity.setOwner(user);
            entity.setOrg(new SpOrg(ThreadCache.getOrgId()));
            entity.setRecordTm(new Date());
            entity.setApprover(user.getSystemAdmin() ? user : null);
            entity.setRegion(ThreadCache.getRegion());
            entity.setName(entity.getReason().length() > 20 ? entity.getReason().substring(0, 20) : entity.getReason());
            entity.setQuotaLogStatus(QuotaLogStatus.pending_approve);
            dao.insert(entity);

            UpQuotaLog children = new UpQuotaLog();
//        BeanCopyUtil.copy(bean, children);
            children.setOwner(user);
            children.setOrg(new SpOrg(ThreadCache.getOrgId()));
            children.setRecordTm(new Date());
            children.setApprover(user.getSystemAdmin() ? user : null);
            children.setRegion(ThreadCache.getRegion());
            children.setName(System.currentTimeMillis()+"");
            children.setParent(entity);
            children.setReason(bean.getReason());
            children.setQuotaLogStatus(QuotaLogStatus.pending_approve);
            dao.insert(children);
        }else{
            // update
            UpQuotaLog entity = queryDao.load(UpQuotaLog.class, bean.getId());
            entity.setApplyQuota(bean.getApplyQuota() == null ? entity.getApplyQuota() : bean.getApplyQuota());
            entity.setQuotaLogStatus(bean.getQuotaLogStatus());
            dao.update(entity, "quotaLogStatus", "applyQuota");

            UpQuotaLog children = new UpQuotaLog();
            children.setOwner(entity.getOwner());
            children.setOrg(new SpOrg(ThreadCache.getOrgId()));
            children.setRecordTm(new Date());
            children.setApprover(user.getSystemAdmin() ? user : null);
            children.setQuotaLogStatus(bean.getQuotaLogStatus());
            children.setRegion(ThreadCache.getRegion());
            children.setName(System.currentTimeMillis()+"");
            children.setParent(entity);
            children.setReason(StringUtils.isEmpty(bean.getReason()) && bean.getQuotaLogStatus() == QuotaLogStatus.close_give_up ? "["+user.getName()+"]关闭了配额申请单！" : bean.getReason());
            dao.insert(children);

            if(bean.getQuotaLogStatus() == QuotaLogStatus.approved){
                saveQuota(entity);
            }
        }
    }

    private void saveQuota(UpQuotaLog entity){
        UpQuota quota = null;
        List<UpQuota> dbQuota =  dao.list(UpQuota.class, MapUtil.of("org", entity.getOrg(), "appSystem", entity.getAppSystem(), "resourceType", entity.getResourceType(), "isUserTemplate", false));
        if(!dbQuota.isEmpty()){
            quota = dbQuota.get(0);
            quota.setQuota(entity.getApplyQuota());
            dao.update(quota, "quota");
            return;
        }

        quota = new UpQuota();
        quota.setOrg(entity.getOrg());
        quota.setAppSystem(entity.getAppSystem());
        quota.setResourceType(entity.getResourceType());
        quota.setQuota(entity.getApplyQuota());
        quota.setName(entity.getAppSystem().getName() + "-" + entity.getResourceType());
        quota.setOwner(entity.getOwner());
        quota.setIsUserTemplate(false);
        quota.setIsAdminQuota(false);
        quota.setRegion(ThreadCache.getRegion());
        quota.setUsedQuota(0); // TODO 同步usedQuota
        quota.setService(upQuotaService.getService(entity.getResourceType()));
        dao.insert(quota);
    }
}
