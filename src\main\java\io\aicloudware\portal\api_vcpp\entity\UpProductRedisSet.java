package io.aicloudware.portal.api_vcpp.entity;

import java.math.BigDecimal;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.sdk.bean.product.UpProductRedisSetBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.RedisType;

@Entity
@Table(name = "up_product_redis_set")
@Access(AccessType.FIELD)
public class UpProductRedisSet extends BaseUpEntity<UpProductRedisSetBean> {

	private static final long serialVersionUID = 2037634668993200385L;

	@Column(name = "payment_type")
	private String paymentType;
	
	@JoinColumn(name = "cpu_item_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UpProductItem cpuProductItem;
	
	@Column(name = "cpu_unit")
	private Integer cpuUnit;
	
	@Column(name = "cpu_price")
	private BigDecimal cpuPrice;
	
	@JoinColumn(name = "memory_item_id")
    @ManyToOne(fetch = FetchType.EAGER)
    private UpProductItem memoryProductItem;

	@Column(name = "memory_unit")
	private Integer memoryUnit;
	
	@Column(name = "memory_price")
	private BigDecimal memoryPrice;
	
	@Column(name = "enabled")
	private Boolean enabled;
	
	@Column(name = "type")
    @Enumerated(EnumType.STRING)
	private RedisType type;

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public UpProductItem getCpuProductItem() {
		return cpuProductItem;
	}

	public void setCpuProductItem(UpProductItem cpuProductItem) {
		this.cpuProductItem = cpuProductItem;
	}

	public Integer getCpuUnit() {
		return cpuUnit;
	}

	public void setCpuUnit(Integer cpuUnit) {
		this.cpuUnit = cpuUnit;
	}

	public BigDecimal getCpuPrice() {
		return cpuPrice;
	}

	public void setCpuPrice(BigDecimal cpuPrice) {
		this.cpuPrice = cpuPrice;
	}

	public UpProductItem getMemoryProductItem() {
		return memoryProductItem;
	}

	public void setMemoryProductItem(UpProductItem memoryProductItem) {
		this.memoryProductItem = memoryProductItem;
	}

	public Integer getMemoryUnit() {
		return memoryUnit;
	}

	public void setMemoryUnit(Integer memoryUnit) {
		this.memoryUnit = memoryUnit;
	}

	public BigDecimal getMemoryPrice() {
		return memoryPrice;
	}

	public void setMemoryPrice(BigDecimal memoryPrice) {
		this.memoryPrice = memoryPrice;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public RedisType getType() {
		return type;
	}

	public void setType(RedisType type) {
		this.type = type;
	}

}
