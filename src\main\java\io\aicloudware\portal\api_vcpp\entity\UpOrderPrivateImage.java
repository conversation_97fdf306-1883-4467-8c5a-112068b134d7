package io.aicloudware.portal.api_vcpp.entity;

import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderPrivateImageBean;
import io.aicloudware.portal.platform_vcd.entity.SpCatalog;
import io.aicloudware.portal.platform_vcd.entity.SpOrg;
import io.aicloudware.portal.platform_vcd.entity.SpVm;

import javax.persistence.*;

@Entity
@Table(name = "up_order_private_image")
@Access(AccessType.FIELD)
public class UpOrderPrivateImage extends UpOrderProduct<UpOrderPrivateImageBean> implements IOrderEntity{

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;

    @JoinColumn(name = "cloud_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;

    @JoinColumn(name = "catalog_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpCatalog catalog;

    @Column(name = "orig_id")
    private Integer origId;

    @Override
    public UpOrder getOrder() {
        return order;
    }

    @Override
    public void setOrder(UpOrder order) {
        this.order = order;
    }

    @Override
    public SpOrg getSpOrg() {
        return spOrg;
    }

    @Override
    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

    public SpVm getVm() {
        return vm;
    }

    public void setVm(SpVm vm) {
        this.vm = vm;
    }

    public SpCatalog getCatalog() {
        return catalog;
    }

    public void setCatalog(SpCatalog catalog) {
        this.catalog = catalog;
    }

    @Override
    public Integer getOrigId() {
        return origId;
    }

    @Override
    public void setOrigId(Integer origId) {
        this.origId = origId;
    }
}
