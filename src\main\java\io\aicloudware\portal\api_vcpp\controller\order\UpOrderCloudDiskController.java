package io.aicloudware.portal.api_vcpp.controller.order;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderCloudDiskService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderService;
import io.aicloudware.portal.api_vcpp.service.product.IUpProductService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudDiskBean;
import io.aicloudware.portal.framework.sdk.contants.SpVmDiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudStorageChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.DiskType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ProductDiskSetType;
import io.aicloudware.portal.framework.utility.AssertUtil;

import io.swagger.annotations.ApiParam;

/**
 * 云盘
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/cloudDisk")
public class UpOrderCloudDiskController extends BaseController {

	@Autowired
	private IUpOrderCloudDiskService cloudDiskService;
	
	@Autowired
	private IUpProductService productService;
	
	@Autowired
	private IUpOrderService spOrderService;
	
	@RequestMapping(value = "/quota/init/{code}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable String code) {
		UpUser user = commonService.load(UpUser.class, ThreadCache.getUserId());
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (CloudStorageChargeType type : CloudStorageChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
        Map<String, String> diskStandardType = new LinkedHashMap<String, String>();
        for (DiskType type : DiskType.values()) {
        	diskStandardType.put(type.toString(), type.getTitle());
        }
        datas.put("diskStandardType", diskStandardType);
        
        Map<String, String> diskType = new LinkedHashMap<String, String>();
        for (SpVmDiskType type : SpVmDiskType.values()) {
        	diskType.put(type.toString(), type.getTitle());
        }
        datas.put("diskType", diskType);
        
        // 云服务器
        datas.put("cloudServers", spOrderService.querySpVm(ThreadCache.getUserId()));
        
        // 配置
        datas.put("diskConfig", productService.mapDiskSet(ProductDiskSetType.vm_disk,code));
		return ResponseBean.success(datas);
	}
	
	@RequestMapping(value = "/init/{id}")
    @ResponseBody
	public ResponseBean init(@ApiParam(value = "对象ID") @PathVariable Integer id) {
		UpUser orderOwner = commonService.load(UpUser.class, ThreadCache.getUserId());

		UpUser user = commonService.load(UpUser.class, id);
		AssertUtil.check(user != null && user.getOrg()!=null, "用户信息异常");
		Map<String,Object> datas = new HashMap<>();
		// 付费方式
		Map<String, String> paymentType = new LinkedHashMap<String, String>();
        for (PaymentType type : PaymentType.values()) {
        	paymentType.put(type.toString(), type.getTitle());
        }
        datas.put("paymentType", paymentType);
        
        // 计费方式
        Map<String, String> chargeType = new LinkedHashMap<String, String>();
        for (CloudStorageChargeType type : CloudStorageChargeType.values()) {
        	chargeType.put(type.toString(), type.getTitle());
        }
        datas.put("chargeType", chargeType);
        
        Map<String, String> diskStandardType = new LinkedHashMap<String, String>();
        for (DiskType type : DiskType.values()) {
        	diskStandardType.put(type.toString(), type.getTitle());
        }
        datas.put("diskStandardType", diskStandardType);
        
        Map<String, String> diskType = new LinkedHashMap<String, String>();
        for (SpVmDiskType type : SpVmDiskType.values()) {
        	diskType.put(type.toString(), type.getTitle());
        }
        datas.put("diskType", diskType);
        
        // 云服务器
        datas.put("cloudServers", spOrderService.querySpVm(id));
        
        // 配置
        datas.put("diskConfig", productService.mapDiskSet(ProductDiskSetType.vm_disk));
		return ResponseBean.success(datas);
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.STORAGE, description = "管理员新增订单")
	public ResponseBean save(@RequestBody UpOrderCloudDiskBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(cloudDiskService.save(bean, applyUser));
	}
	
	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/quota/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.STORAGE, description = "用户新增订单")
	public ResponseBean quotaSave(@RequestBody UpOrderCloudDiskBean bean, HttpServletRequest request) {
		AssertUtil.check(bean.getQuotaId(), "请选择协议");
		AssertUtil.check(bean.getQuotaDetailId(), "请选择订单项");
		bean.setOwnerId(ThreadCache.getUserId());
		return ResponseBean.success(cloudDiskService.save(bean, ThreadCache.getUser()));
	}
}