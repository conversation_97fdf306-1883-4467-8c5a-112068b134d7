package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.ReqDeployment;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.SpDeploymentBean;
import io.aicloudware.portal.framework.sdk.bean.SpDeploymentResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/req_deployment")
@Api(value = "/req_deployment", description = "申请单部署信息", position = 601)
public class ReqDeploymentController extends BaseUpController<ReqDeployment, SpDeploymentBean, SpDeploymentResultBean> {

//    @Autowired
//    private ISpDeploymentService spDeploymentService;
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = SpDeploymentResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryReqDeployment(@ApiParam(value = "查询条件") @RequestBody SpDeploymentSearchBean searchBean) {
//        ReqDeployment entity = BeanCopyUtil.copy(searchBean.getBean(), ReqDeployment.class);
//        SpDeploymentBean[] entityList = commonService.query(searchBean, entity, SpDeploymentBean.class);
//        SpDeploymentResultBean result = new SpDeploymentResultBean();
//        fillPageInfo(searchBean, result);
//        result.setDataList(entityList);
//        return ResponseBean.success(result);
//    }
//
//    @Override
//    protected SpDeploymentBean[] doUpdate(List<SpDeploymentBean> beanList) {
//        return spDeploymentService.updateReqDeploymentList(beanList);
//    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = SpDeploymentBean.class)})
//    @ResponseBody
//    public ResponseBean updateReqDeployment(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                            @ApiParam(value = "实例对象") @Valid @RequestBody SpDeploymentBean bean,
//                                            BindingResult bindingResult) {
//        ReqDeploymentListBean listBean = new ReqDeploymentListBean();
//        listBean.setDataList(new SpDeploymentBean[]{bean});
//        spDeploymentService.saveChangeDeploymentLog(listBean);
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = ReqDeploymentListBean.class)})
//    @ResponseBody
//    public ResponseBean updateReqDeployment(@ApiParam(value = "实例对象") @Valid @RequestBody ReqDeploymentListBean bean, BindingResult bindingResult) {
//        spDeploymentService.saveChangeDeploymentLog(bean);
//        return updateEntity(bean, bindingResult);
//    }
}
