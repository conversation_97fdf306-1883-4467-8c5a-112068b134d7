package io.aicloudware.portal.api_up.entity;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import io.aicloudware.portal.framework.common.ApiConstants;
import io.aicloudware.portal.framework.entity.BaseUpEntity;
import io.aicloudware.portal.framework.entity.IEnvironmentEntity;
import io.aicloudware.portal.framework.sdk.bean.UpApplicationChangeLogBean;

@Entity
@Table(name = "up_application_change_log")
@Access(AccessType.FIELD)
public class UpApplicationChangeLog extends BaseUpEntity<UpApplicationChangeLogBean> implements IEnvironmentEntity<UpApplicationChangeLogBean> {

    @JoinColumn(name = "application_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpApplication application;

    @JoinColumn(name = "vm_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private ReqVm vm;

    @JoinColumn(name = "operation_user_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser operationUser;

    @Column(name = "change_info", length = ApiConstants.STRING_MIDDLE_LENGTH)
    private String changeInfo;

    public UpApplication getApplication() {
        return application;
    }

    public void setApplication(UpApplication application) {
        this.application = application;
    }

    public UpUser getOperationUser() {
        return operationUser;
    }

    public void setOperationUser(UpUser operationUser) {
        this.operationUser = operationUser;
    }

    public String getChangeInfo() {
        return changeInfo;
    }

    public void setChangeInfo(String changeInfo) {
        this.changeInfo = changeInfo;
    }

    public ReqVm getVm() {
        return vm;
    }

    public void setVm(ReqVm vm) {
        this.vm = vm;
    }

}
