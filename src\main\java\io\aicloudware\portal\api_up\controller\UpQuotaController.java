package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpQuota;
import io.aicloudware.portal.api_up.service.IUpQuotaService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaBean;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaListBean;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaResultBean;
import io.aicloudware.portal.framework.sdk.bean.UpQuotaSearchBean;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/quota")
@Api(value = "/quota", description = "资源配额", position = 508)
public class UpQuotaController extends BaseUpController<UpQuota, UpQuotaBean, UpQuotaResultBean> {

    @Autowired
    private IUpQuotaService upQuotaService;

    @RequestMapping(value = "/admin/query", method = RequestMethod.POST)
    @ApiOperation(notes = "/admin/query", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean adminQueryUserQuota(@ApiParam(value = "查询条件") @RequestBody UpQuotaSearchBean searchBean) {
//        AssertUtil.check(searchBean, "查询条件不能为空！");
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
//        if (searchBean.getBean() == null) {
//            searchBean.setBean(new UpQuotaBean());
//        }
//        UpQuotaBean bean = searchBean.getBean();
//        bean.setAdminQuota(true);
//        bean.setOrgId(ThreadCache.getOrgId());
//        bean.setRegion(ThreadCache.getRegion());
        return ResponseBean.success(upQuotaService.overviewQuota(null));
    }

    @RequestMapping(value = "/admin/detail/{id}", method = RequestMethod.GET)
    @ApiOperation(notes = "/admin/detail/{id}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean adminDetail(@ApiParam(value = "对象ID") @PathVariable Integer id) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        return ResponseBean.success(upQuotaService.adminDetail(id));
    }

    @RequestMapping(value = "/admin/queryUserTemplate", method = RequestMethod.POST)
    @ApiOperation(notes = "/admin/queryUserTemplate", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean queryUserTemplate(@ApiParam(value = "查询条件") @RequestBody UpQuotaSearchBean searchBean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        searchBean = searchBean == null ? new UpQuotaSearchBean() : searchBean;
        searchBean.setOrderName1(StringUtils.isEmpty(searchBean.getOrderName1()) ? "service" : searchBean.getOrderName1());
        searchBean.setOrderName2(StringUtils.isEmpty(searchBean.getOrderName2()) ? "name" : searchBean.getOrderName2());
        searchBean.setOrderBy1(searchBean.getOrderBy1() == null ? true : searchBean.getOrderBy1());
        return ResponseBean.success(upQuotaService.queryTemplate(searchBean));
    }

    @RequestMapping(value = "/admin/saveUserTemplate", method = RequestMethod.POST)
    @ApiOperation(notes = "/admin/saveUserTemplate", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean saveUserTemplate(@RequestBody UpQuotaBean bean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        upQuotaService.saveTemplate(bean);
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/admin/queryUserQuota", method = RequestMethod.POST)
    @ApiOperation(notes = "/admin/queryUserQuota", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean queryUserQuota(@ApiParam(value = "查询条件") @RequestBody UpQuotaSearchBean searchBean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        UpQuotaBean bean = searchBean.getBean();
        bean.setAdminQuota(false);
        bean.setOrgId(ThreadCache.getOrgId());
        bean.setRegionId(ThreadCache.getRegion().getId());
        bean.setRegionName(ThreadCache.getRegion().getName());

        searchBean.setPageSize(Integer.MAX_VALUE);
//        searchBean.setOrderName1("ownerId");
        searchBean.setBean(bean);
        return queryEntity(searchBean);
    }

    @RequestMapping(value = "/admin/saveUserQuota", method = RequestMethod.POST)
    @ApiOperation(notes = "/user/save", httpMethod = "POST", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean userSave(@ApiParam(value = "查询条件") @RequestBody UpQuotaListBean listBean) {
        AssertUtil.check(ThreadCache.getUser().getSystemAdmin(), "无操作权限！");
        this.upQuotaService.userQuotaSave(listBean);
        return ResponseBean.success(true);
    }

//    @RequestMapping(value = "/user/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/user/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
//    @ResponseBody
//    public ResponseBean userQuery(@ApiParam(value = "查询条件") @RequestBody UpQuotaSearchBean searchBean) {
//        if (searchBean.getBean() == null) {
//            searchBean.setBean(new UpQuotaBean());
//        }
//        UpQuotaBean bean = searchBean.getBean();
//        bean.setAdminQuota(false);
//        bean.setOrgId(ThreadCache.getOrgId());
//        bean.setOwnerId(ThreadCache.getUserId());
//        bean.setRegion(ThreadCache.getRegion());
//        return queryEntity(searchBean);
//    }

//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpQuotaBean.class)})
//    @ResponseBody
//    public ResponseBean updateQuota(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                    @ApiParam(value = "实例对象") @Valid @RequestBody UpQuotaBean bean,
//                                    BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }

//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteQuota(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }

    @RequestMapping(value = "/sync", method = RequestMethod.GET)
    @ResponseBody
    public ResponseBean sync() {
        upQuotaService.sync(ThreadCache.getOrgId(), ThreadCache.getRegion());
        return ResponseBean.success("");
    }

    @RequestMapping(value = "/user/detail/{resourceType}", method = RequestMethod.GET)
    @ApiOperation(notes = "/user/detail/{resourceType}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean userDetail(@ApiParam(value = "对象ID") @PathVariable String resourceType) {
        return ResponseBean.success(upQuotaService.userDetail(resourceType));
    }

    @RequestMapping(value = "/user/appSystemQuota/{appSystemId}", method = RequestMethod.GET)
    @ApiOperation(notes = "/user/appSystemQuota/{appSystemId}", httpMethod = "GET", value = "查询实例对象列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpQuotaResultBean.class)})
    @ResponseBody
    public ResponseBean appSystemQuota(@ApiParam(value = "对象ID") @PathVariable Integer appSystemId) {
        return ResponseBean.success(upQuotaService.appSystemQuota(appSystemId));
    }
}
