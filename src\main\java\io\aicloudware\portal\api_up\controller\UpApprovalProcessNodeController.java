package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.entity.UpApprovalProcessNode;
import io.aicloudware.portal.framework.controller.BaseUpController;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessNodeBean;
import io.aicloudware.portal.framework.sdk.bean.UpApprovalProcessNodeResultBean;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/approval_process_node")
@Api(value = "/approval_process_node", description = "审批流程节点设定", position = 611)
public class UpApprovalProcessNodeController extends BaseUpController<UpApprovalProcessNode, UpApprovalProcessNodeBean, UpApprovalProcessNodeResultBean> {

//    protected UpApprovalProcessNodeBean[] doInsert(List<UpApprovalProcessNode> entityList) {
//        return commonService.insert(getBeanType(), entityList, false);
//    }
//
//    protected UpApprovalProcessNodeBean[] doUpdate(List<UpApprovalProcessNodeBean> beanList) {
//        return commonService.update(getEntityType(), getBeanType(), beanList, false);
//    }
//
//    @RequestMapping(value = "/query", method = RequestMethod.POST)
//    @ApiOperation(notes = "/query", httpMethod = "POST", value = "查询实例对象列表")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalProcessNodeResultBean.class)})
//    @ResponseBody
//    public ResponseBean queryApprovalProcessNode(@ApiParam(value = "查询条件") @RequestBody UpApprovalProcessNodeSearchBean searchBean) {
//        return queryEntity(searchBean);
//    }
//
//    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
//    @ApiOperation(notes = "/get/{id}", httpMethod = "GET", value = "获取实例对象")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalProcessNodeBean.class)})
//    @ResponseBody
//    public ResponseBean getApprovalProcessNode(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return getEntity(id);
//    }
//
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ApiOperation(notes = "/add", httpMethod = "POST", value = "添加实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalProcessNodeBean.class)})
//    @ResponseBody
//    public ResponseBean addApprovalProcessNode(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalProcessNodeBean bean, BindingResult bindingResult) {
//        bean.setSeq(null == bean.getMaxSeq() ? 1 : bean.getMaxSeq() + 1);
//        return addEntity(bean, bindingResult);
//    }
//
////    @RequestMapping(value = "/add_list", method = RequestMethod.POST)
////    @ApiOperation(notes = "/add_list", httpMethod = "POST", value = "批量添加实例")
////    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalProcessNodeListBean.class)})
////    @ResponseBody
////    public ResponseBean addApprovalProcessNode(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalProcessNodeListBean bean, BindingResult bindingResult) {
////        return addEntity(bean, bindingResult);
////    }
//
//    @RequestMapping(value = "/update/{id}", method = RequestMethod.PUT)
//    @ApiOperation(notes = "/update/{id}", httpMethod = "PUT", value = "修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象", response = UpApprovalProcessNodeBean.class)})
//    @ResponseBody
//    public ResponseBean updateApprovalProcessNode(@ApiParam(value = "对象ID") @PathVariable Integer id,
//                                                  @ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalProcessNodeBean bean,
//                                                  BindingResult bindingResult) {
//        return updateEntity(id, bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/update_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/update_list", httpMethod = "POST", value = "批量修改实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回实例对象列表", response = UpApprovalProcessNodeListBean.class)})
//    @ResponseBody
//    public ResponseBean updateApprovalProcessNode(@ApiParam(value = "实例对象") @Valid @RequestBody UpApprovalProcessNodeListBean bean, BindingResult bindingResult) {
//        return updateEntity(bean, bindingResult);
//    }
//
//    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
//    @ApiOperation(notes = "/delete/{id}", httpMethod = "DELETE", value = "删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApprovalProcessNode(@ApiParam(value = "对象ID") @PathVariable Integer id) {
//        return deleteEntity(id);
//    }
//
//    @RequestMapping(value = "/delete_list", method = RequestMethod.POST)
//    @ApiOperation(notes = "/delete_list", httpMethod = "POST", value = "批量删除实例")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回操作结果", response = Boolean.class)})
//    @ResponseBody
//    public ResponseBean deleteApprovalProcessNode(@ApiParam(value = "对象ID列表") @Valid @RequestBody UpSimpleOperateBean bean, BindingResult bindingResult) {
//        return deleteEntity(bean, bindingResult);
//    }
}
