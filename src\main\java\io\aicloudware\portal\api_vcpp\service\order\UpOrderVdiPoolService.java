package io.aicloudware.portal.api_vcpp.service.order;

import io.aicloudware.portal.api_up.entity.UpTask;
import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.entity.UpOrder;
import io.aicloudware.portal.api_vcpp.entity.UpOrderVdiPool;
import io.aicloudware.portal.api_vcpp.entity.UpServicePlan;
import io.aicloudware.portal.api_vcpp.service.product.IUpServicePlanService;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderVdiPoolBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderStatus;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.OrderType;
import io.aicloudware.portal.framework.sdk.contants.UpServicePlanItemType;
import io.aicloudware.portal.framework.sdk.contants.UpTaskStatus;
import io.aicloudware.portal.framework.sdk.contants.UpTaskType;
import io.aicloudware.portal.framework.service.BaseService;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.platform_vcd.entity.SpVappTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.atomic.AtomicReference;

@Service
@Transactional
public class UpOrderVdiPoolService extends BaseService implements IUpOrderVdiPoolService {

    @Autowired
    private IUpServicePlanService upServicePlanService;

    @Override
    public Integer save(UpOrderVdiPoolBean bean, UpUser applyUser) {

        AssertUtil.check(bean.getServicePlan() != null && bean.getServicePlan().getId() != null,  "请选择配置！");
        AssertUtil.check(bean.getImageId(), "请选择镜像！");
        AssertUtil.check(bean.getVpcId(), "请选择专有网络！");

//		SpVPC vpc = this.dao.load(SpVPC.class, bean.getVpcId());
//		AssertUtil.check(vpc != null && RecordStatus.active.equals(vpc.getStatus()), "VPC不存在！");
//		AssertUtil.check(vpc.getSpOrg().getId().equals(user.getOrg().getId()), "VPC不存在！");
        AssertUtil.check(bean.getNetworkId(), "请选择子网！");
//		SpOVDCNetwork ovdcNetwork = this.dao.load(SpOVDCNetwork.class, bean.getNetworkId());
//		AssertUtil.check(ovdcNetwork != null && RecordStatus.active.equals(ovdcNetwork.getStatus()), "子网不存在！");
//		AssertUtil.check(ovdcNetwork.getSpOrg().getId().equals(user.getOrg().getId()), "子网不存在！");

        AssertUtil.check(bean.getName(), "请输入桌面名！");


        // TODO 缺少校验
        SpVappTemplate template = this.dao.load(SpVappTemplate.class, bean.getImageId());

        UpServicePlan servicePlan = dao.load(UpServicePlan.class, bean.getServicePlan().getId());

        AtomicReference<Integer> cpu = new AtomicReference<>();
        AtomicReference<Integer> memory = new AtomicReference<>();

        servicePlan.getServicePlanItems().forEach(item -> {
            if(item.getServicePlanItemType() == UpServicePlanItemType.CPU) {
                cpu.set(item.getAmount());
            }

            if(item.getServicePlanItemType() == UpServicePlanItemType.MEMORY_GB){
                memory.set(item.getAmount());
            }
        });


        UpOrder order = new UpOrder();
        order.setRegion(ThreadCache.getRegion());
        order.setType(OrderType.new_vdi);
        order.setName("[" + OrderType.new_cloud_server + "]" + bean.getName());
        order.setOrderStatus(OrderStatus.pending_deploy);
        order.setOwner(applyUser);
        order.setApplyUser(applyUser);
        order.setSpOrg(applyUser.getOrg());
        order.setNumber(1);
        this.dao.insert(order);

        String random = System.currentTimeMillis() + String.format("%04d", (int) (Math.random() * 1000));



        UpOrderVdiPool vdiPoolOrder = new UpOrderVdiPool();
        vdiPoolOrder.setRegion(order.getRegion());
        vdiPoolOrder.setName(bean.getName() + "-" + random);
        vdiPoolOrder.setOrder(order);
        vdiPoolOrder.setSpOrg(applyUser.getOrg());
        vdiPoolOrder.setRegion(order.getRegion());
        vdiPoolOrder.setCpu(cpu.get());
        vdiPoolOrder.setMemory(memory.get());
        vdiPoolOrder.setServicePlan(servicePlan);
        vdiPoolOrder.setImage(template);
        vdiPoolOrder.setVpcId(bean.getVpcId());
        vdiPoolOrder.setNetworkId(bean.getNetworkId());
        this.dao.insert(vdiPoolOrder);

        UpTask task = new UpTask();
        task.setName(order.getName());
        task.setType(UpTaskType.up_application);
        task.setOrder(order);
        task.setOrderType(order.getType());
        task.setTaskStatus(UpTaskStatus.start);
        task.setSpOrg(applyUser.getOrg());
        task.setRegion(order.getRegion());
        dao.insert(task);
        return order.getId();
    }

}
