package io.aicloudware.portal.api_vcpp.entity;

import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import io.aicloudware.portal.framework.sdk.contants.ServerTypeExt;
import io.aicloudware.portal.platform_vcd.entity.*;
import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.Where;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.framework.entity.IOrderEntity;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderCloudServerBean;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.CloudServerChargeType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.KeyType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.PaymentType;
import io.aicloudware.portal.framework.sdk.contants.UpOrderSystemEnums.ServerType;

@Entity
@Table(name = "up_order_cloud_server")
@Access(AccessType.FIELD)
public class UpOrderCloudServer extends UpOrderProduct<UpOrderCloudServerBean> implements IOrderEntity {

    @Column(name = "server_type")
    @Enumerated(EnumType.STRING)
    private ServerType serverType;
    
    @Column(name = "payment_type")
    @Enumerated(EnumType.STRING)
    private PaymentType paymentType;

    @Column(name = "charge_type")
    @Enumerated(EnumType.STRING)
    private CloudServerChargeType chargeType;

    @Column(name = "server_config_id")
    private Integer serverConfigId;

    @Column(name = "cpu")
    private Integer cpu;

    @Column(name = "memory")
    private Integer memory;

    @Column(name = "image_id")
    private Integer imageId;

    @Column(name = "vpc_id")
    private Integer vpcId;

    @Column(name = "network_id")
    private Integer networkId;

    @Column(name = "external_vpc_id")
    private Integer externalVpcId;

    @Column(name = "external_network_id")
    private Integer externalNetworkId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderCloudServer")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<UpOrderElasticIp> elasticIpList;

    @Column(name = "key_type")
    @Enumerated(EnumType.STRING)
    private KeyType keyType;

    @Column(name = "key_id")
    private Integer keyId;

    @Column(name = "account")
    private String account;

    @Column(name = "resource_region")
    private String resourceRegion;
    
    @Column(name = "db_name")
    private String dbName;

    @Column(name = "password")
    private String password;

    @Column(name = "hostname")
    private String hostname;

    @Column(name = "task_sequence")
    private Integer taskSequence;

    @JoinColumn(name = "cloud_server_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVm vm;

    @JoinColumn(name = "owner_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpUser owner;

    @JoinColumn(name = "security_group_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private SpSecurityGroup securityGroup;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderCloudServer")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<UpOrderCloudDisk> cloudDiskList;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderCloudServer")
    @Where(clause = "status!='deleted'")
    @OrderBy(clause = "id")
    private List<UpOrderCloudServerNetwork> orderCloudServerNetworkList;

    @JoinColumn(name = "order_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UpOrder order;

    @JoinColumn(name = "org_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpOrg spOrg;
    
    @JoinColumn(name = "vapp_template_machine_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVappTemplateMachine templateMachine;
    
    // redis rds 变更使用
    @JoinColumn(name = "update_vapp_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private SpVapp updateVapp;

    @Column(name = "server_type_ext")
    @Enumerated(EnumType.STRING)
    private ServerTypeExt serverTypeExt;

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }

    public CloudServerChargeType getChargeType() {
        return chargeType;
    }

    public void setChargeType(CloudServerChargeType chargeType) {
        this.chargeType = chargeType;
    }

    public Integer getServerConfigId() {
        return serverConfigId;
    }

    public void setServerConfigId(Integer serverConfigId) {
        this.serverConfigId = serverConfigId;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Integer getMemory() {
        return memory;
    }

    public void setMemory(Integer memory) {
        this.memory = memory;
    }

    public Integer getImageId() {
        return imageId;
    }

    public void setImageId(Integer imageId) {
        this.imageId = imageId;
    }

    public Integer getVpcId() {
        return vpcId;
    }

    public void setVpcId(Integer vpcId) {
        this.vpcId = vpcId;
    }

    public Integer getNetworkId() {
        return networkId;
    }

    public void setNetworkId(Integer networkId) {
        this.networkId = networkId;
    }

    public Integer getExternalVpcId() {
        return externalVpcId;
    }

    public void setExternalVpcId(Integer externalVpcId) {
        this.externalVpcId = externalVpcId;
    }

    public Integer getExternalNetworkId() {
        return externalNetworkId;
    }

    public void setExternalNetworkId(Integer externalNetworkId) {
        this.externalNetworkId = externalNetworkId;
    }

    public KeyType getKeyType() {
        return keyType;
    }

    public void setKeyType(KeyType keyType) {
        this.keyType = keyType;
    }

    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public UpUser getOwner() {
        return owner;
    }

    public void setOwner(UpUser owner) {
        this.owner = owner;
    }

    public List<UpOrderCloudDisk> getCloudDiskList() {
        return cloudDiskList;
    }

    public void setCloudDiskList(List<UpOrderCloudDisk> cloudDiskList) {
        this.cloudDiskList = cloudDiskList;
    }

    public List<UpOrderElasticIp> getElasticIpList() {
        return elasticIpList;
    }

    public void setElasticIpList(List<UpOrderElasticIp> elasticIpList) {
        this.elasticIpList = elasticIpList;
    }

    public UpOrder getOrder() {
        return order;
    }

    public void setOrder(UpOrder order) {
        this.order = order;
    }

    public SpOrg getSpOrg() {
        return spOrg;
    }

    public void setSpOrg(SpOrg spOrg) {
        this.spOrg = spOrg;
    }

    public Integer getTaskSequence() {
        return taskSequence;
    }

    public void setTaskSequence(Integer taskSequence) {
        this.taskSequence = taskSequence;
    }

    public SpVm getVm() {
        return vm;
    }

    public void setVm(SpVm vm) {
        this.vm = vm;
    }

	public ServerType getServerType() {
		return serverType;
	}

	public void setServerType(ServerType serverType) {
		this.serverType = serverType;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

    public List<UpOrderCloudServerNetwork> getOrderCloudServerNetworkList() {
        return orderCloudServerNetworkList;
    }

    public void setOrderCloudServerNetworkList(List<UpOrderCloudServerNetwork> orderCloudServerNetworkList) {
        this.orderCloudServerNetworkList = orderCloudServerNetworkList;
    }

	public SpVappTemplateMachine getTemplateMachine() {
		return templateMachine;
	}

	public void setTemplateMachine(SpVappTemplateMachine templateMachine) {
		this.templateMachine = templateMachine;
	}

	public SpVapp getUpdateVapp() {
		return updateVapp;
	}

	public void setUpdateVapp(SpVapp updateVapp) {
		this.updateVapp = updateVapp;
	}

    public ServerTypeExt getServerTypeExt() {
        return serverTypeExt;
    }

    public void setServerTypeExt(ServerTypeExt serverTypeExt) {
        this.serverTypeExt = serverTypeExt;
    }

    public String getResourceRegion() {
        return resourceRegion;
    }

    public void setResourceRegion(String resourceRegion) {
        this.resourceRegion = resourceRegion;
    }

    public SpSecurityGroup getSecurityGroup() {
        return securityGroup;
    }

    public void setSecurityGroup(SpSecurityGroup securityGroup) {
        this.securityGroup = securityGroup;
    }
}
