package io.aicloudware.portal.api_vcpp.controller.order;

import io.aicloudware.portal.api_up.entity.UpUser;
import io.aicloudware.portal.api_vcpp.controller.BaseController;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderVdiPoolService;
import io.aicloudware.portal.api_vcpp.service.order.IUpOrderVdiService;
import io.aicloudware.portal.framework.annotation.AuditLogUpOrder;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.common.ThreadCache;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderVdiBean;
import io.aicloudware.portal.framework.sdk.bean.vcpp.UpOrderVdiPoolBean;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * RDS服务器申请
 * <AUTHOR>
 */
@Controller
@RequestMapping("/order/vdiPool")
public class UpOrderVdiPoolController extends BaseController {

	@Autowired
	private IUpOrderVdiPoolService upOrderVdiPoolService;

	/**
	 * 保存
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    @AuditLogUpOrder(type = UpProductSystemEnums.ProductType.VDI, description = "管理员新增订单")
    public ResponseBean save(@RequestBody UpOrderVdiPoolBean bean, HttpServletRequest request) {
		UpUser applyUser = commonService.load(UpUser.class, ThreadCache.getUserId());
		return ResponseBean.success(upOrderVdiPoolService.save(bean, applyUser));
	}
}