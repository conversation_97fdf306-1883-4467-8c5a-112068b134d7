package io.aicloudware.portal.api_rest.framework.bean;

import javax.validation.GroupSequence;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.UpProductSystemEnums;
import io.aicloudware.portal.framework.validate.V01;
import io.aicloudware.portal.framework.validate.V02;
import io.aicloudware.portal.framework.validate.V03;
import io.aicloudware.portal.framework.validate.V04;
import io.aicloudware.portal.framework.validate.V05;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "message")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({ V01.class, V02.class, V03.class, V04.class, V05.class, RestMessageBean.class })
public class RestMessageBean extends BaseRestBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3083392327351789522L;

	@ApiModelProperty(value = "资源类型")
	String instId;
	
	@ApiModelProperty(value = "资源类型")
	private String warntitle;
	
	@ApiModelProperty(value = "资源类型")
	private String warnContent;

	// 必传 1=资源操作 2=创建租户 3=创建用户 4=云测入云结果 5=暂停 6=恢复
	@ApiModelProperty(value = "资源类型")
	private String messageType;

	// 必传 1=成功 -1 =失败
	@ApiModelProperty(value = "结果")
	private Integer resultCode;

	// 选填 失败原因
	@ApiModelProperty(value = "失败原因")
	private String failReason;

	// messageType=1必传 资源操作结果
	@ApiModelProperty(value = "失败原因")
	private RestResourceMessageBean resourceMessage;

	@ApiModelProperty(value = "失败原因")
	private RestCloudWlanObjectBean cloudWlanObject;

	private UpProductSystemEnums.QuotaDetailChannel channel;
	
	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public Integer getResultCode() {
		return resultCode;
	}

	public void setResultCode(Integer resultCode) {
		this.resultCode = resultCode;
	}

	public String getFailReason() {
		return failReason;
	}

	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}

	public RestResourceMessageBean getResourceMessage() {
		return resourceMessage;
	}

	public void setResourceMessage(RestResourceMessageBean resourceMessage) {
		this.resourceMessage = resourceMessage;
	}

	public String getInstId() {
		return instId;
	}

	public void setInstId(String instId) {
		this.instId = instId;
	}

	public String getWarntitle() {
		return warntitle;
	}

	public void setWarntitle(String warntitle) {
		this.warntitle = warntitle;
	}

	public String getWarnContent() {
		return warnContent;
	}

	public void setWarnContent(String warnContent) {
		this.warnContent = warnContent;
	}

	public RestCloudWlanObjectBean getCloudWlanObject() {
		return cloudWlanObject;
	}

	public void setCloudWlanObject(RestCloudWlanObjectBean cloudWlanObject) {
		this.cloudWlanObject = cloudWlanObject;
	}

	public UpProductSystemEnums.QuotaDetailChannel getChannel() {
		return channel;
	}

	public void setChannel(UpProductSystemEnums.QuotaDetailChannel channel) {
		this.channel = channel;
	}
}
