package io.aicloudware.portal.framework.sdk.bean.V3;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.aicloudware.portal.framework.sdk.contants.SpServerConnectionType;
import io.aicloudware.portal.framework.validate.*;
import io.swagger.annotations.ApiModel;

import javax.validation.GroupSequence;

@ApiModel(value = "服务环境设置")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@GroupSequence({V01.class, V02.class, V03.class, V04.class, V05.class, SpServerConnectionV3Bean.class})
public class SpServerConnectionV3Bean {

    private String cloudConnectionName;
    private String cloudConnectionUuid;
    private SpServerConnectionType cloudConnectionType;

    public String getCloudConnectionName() {
        return cloudConnectionName;
    }

    public void setCloudConnectionName(String cloudConnectionName) {
        this.cloudConnectionName = cloudConnectionName;
    }

    public String getCloudConnectionUuid() {
        return cloudConnectionUuid;
    }

    public void setCloudConnectionUuid(String cloudConnectionUuid) {
        this.cloudConnectionUuid = cloudConnectionUuid;
    }

    public SpServerConnectionType getCloudConnectionType() {
        return cloudConnectionType;
    }

    public void setCloudConnectionType(SpServerConnectionType cloudConnectionType) {
        this.cloudConnectionType = cloudConnectionType;
    }
}
