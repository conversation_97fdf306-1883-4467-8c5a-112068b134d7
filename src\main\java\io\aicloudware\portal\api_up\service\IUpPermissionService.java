package io.aicloudware.portal.api_up.service;

import io.aicloudware.portal.api_up.entity.ReqDeployment;
import io.aicloudware.portal.api_up.entity.ReqVm;
import io.aicloudware.portal.framework.sdk.bean.*;
import io.aicloudware.portal.framework.service.ITaskRunnable;
import io.aicloudware.portal.platform_vcd.entity.SpRegionEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public interface IUpPermissionService extends ITaskRunnable {

    public void notifyRefreshUsage();

    public void checkDeploymentAdd(SpDeploymentListBean bean);

    public void checkDeploymentAdd(List<ReqDeployment> requestList, boolean isCheckOrSave);

    public void checkVmChangeResource(SpVmListBean bean);

    public void checkVmChangeResource(List<ReqVm> requestList, boolean isCheckOrSave);

    void sync(Integer orgId, SpRegionEntity region);

    UpPermissionBean[] queryUserTemplate(UpPermissionSearchBean searchBean);

    void saveUserTemplate(UpPermissionBean bean);

    UpPermissionBean adminDetail(Integer id);

    void saveUserPermission(UpPermissionListBean listBean);

    void savePermissionByUserId(UpPermissionListBean listBean);

    List<UpPermissionBean> adminList(Integer id);

    void updateUserTemplate(UpPermissionBean bean);

    UpPermissionBean[] listPermissionByTemplate(Integer id);

    UpPermissionBean[] userList(Integer id);
}
